/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AnalysisResults: typeof import('./src/components/AnalysisResults.vue')['default']
    DocumentNavigator: typeof import('./src/components/DocumentNavigator.vue')['default']
    DocumentPdfViewer: typeof import('./src/components/DocumentPdfViewer.vue')['default']
    DocumentPdfViewerHtml: typeof import('./src/components/DocumentPdfViewerHtml.vue')['default']
    DocumentPdfViewerOfficial: typeof import('./src/components/DocumentPdfViewerOfficial.vue')['default']
    DocumentUpload: typeof import('./src/components/DocumentUpload.vue')['default']
    DocumentViewer: typeof import('./src/components/DocumentViewer.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLoading: typeof import('element-plus/es')['ElLoading']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    HighlightInteraction: typeof import('./src/components/HighlightInteraction.vue')['default']
    PdfViewerAdvanced: typeof import('./src/components/PdfViewerAdvanced.vue')['default']
    PdfViewerBuiltIn: typeof import('./src/components/PdfViewerBuiltIn.vue')['default']
    PdfViewerEnhanced: typeof import('./src/components/PdfViewerEnhanced.vue')['default']
    PdfViewerExample: typeof import('./src/components/PdfViewerExample.vue')['default']
    PdfViewerOfficial: typeof import('./src/components/PdfViewerOfficial.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StructuredAnalysisResults: typeof import('./src/components/StructuredAnalysisResults.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
