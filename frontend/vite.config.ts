import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import WindiCSS from 'vite-plugin-windicss'
import { viteStaticCopy } from 'vite-plugin-static-copy'


export default defineConfig({
  plugins: [
    vue(),
    WindiCSS(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia'],
      dts: true
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      dts: true
    }),
    // {{RIPER-5+SMART-6:
    //   Action: "PDF.js-Integration"
    //   Task_ID: "PDFJS-STATIC-COPY"
    //   Timestamp: "2025-08-18T17:40:36+08:00"
    //   Authoring_Subagent: "vite-config-expert"
    //   Principle_Applied: "静态资源自动复制原则"
    //   Quality_Check: "确保PDF.js官方查看器资源正确复制到构建目录。"
    // }}
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/pdfjs-dist/web/*',
          dest: 'pdfjs/web'
        },
        {
          src: 'node_modules/pdfjs-dist/build/pdf.worker.min.js',
          dest: 'pdfjs/build'
        },
        {
          src: 'node_modules/pdfjs-dist/build/pdf.min.js',
          dest: 'pdfjs/build'
        }
      ]
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      },
      '/maxkb-api': {
        target: 'http://*************:8080',
        changeOrigin: true,
        secure: false,
        rewrite: path => path.replace(/^\/maxkb-api/, '/api')
      }
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    }
  },
  optimizeDeps: {
    include: ['vue', 'vue-router', 'pinia', 'element-plus', 'axios'],
    exclude: ['pdfjs-dist'] // PDF.js通过静态资源加载，避免Vite处理
  },

  assetsInclude: ['**/*.pdf'],
  define: {
    // {{RIPER-5+SMART-6:
    //   Action: "PDF.js-Configuration"
    //   Task_ID: "PDFJS-WORKER-PATH"
    //   Timestamp: "2025-08-18T17:40:36+08:00"
    //   Authoring_Subagent: "vite-config-expert"
    //   Principle_Applied: "生产环境路径配置原则"
    //   Quality_Check: "确保PDF.js Worker在生产环境中正确加载。"
    // }}
    'process.env.PDF_WORKER_SRC': JSON.stringify('/pdfjs/build/pdf.worker.min.js'),
    'process.env.PDF_VIEWER_URL': JSON.stringify('/pdfjs/web/viewer.html')
  }
})
