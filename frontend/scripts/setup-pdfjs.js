#!/usr/bin/env node

/**
 * {{RIPER-5+SMART-6:
 *   Action: "Setup-Script"
 *   Task_ID: "PDFJS-SETUP-AUTOMATION"
 *   Timestamp: "2025-08-18T17:40:36+08:00"
 *   Authoring_Subagent: "devops-automation-expert"
 *   Principle_Applied: "自动化部署与配置原则"
 *   Quality_Check: "完整的PDF.js安装和配置自动化脚本。"
 * }}
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 PDF.js官方查看器安装脚本启动...')

// 配置常量
const CONFIG = {
  PDFJS_VERSION: '^3.11.174',
  VITE_PLUGIN_VERSION: '^1.0.6',
  TYPES_VERSION: '^10.0.0',
  PUBLIC_DIR: path.join(__dirname, '../public'),
  PDFJS_DIR: path.join(__dirname, '../public/pdfjs'),
  NODE_MODULES_PDFJS: path.join(__dirname, '../node_modules/pdfjs-dist')
}

// 步骤1：检查依赖
function checkDependencies() {
  console.log('📦 检查依赖包...')
  
  const packageJsonPath = path.join(__dirname, '../package.json')
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  
  const requiredDeps = {
    'pdfjs-dist': CONFIG.PDFJS_VERSION
  }
  
  const requiredDevDeps = {
    'vite-plugin-static-copy': CONFIG.VITE_PLUGIN_VERSION,
    '@types/uuid': CONFIG.TYPES_VERSION
  }
  
  let needsInstall = false
  
  // 检查生产依赖
  for (const [dep, version] of Object.entries(requiredDeps)) {
    if (!packageJson.dependencies?.[dep]) {
      console.log(`❌ 缺少依赖: ${dep}`)
      needsInstall = true
    } else {
      console.log(`✅ 已安装: ${dep}@${packageJson.dependencies[dep]}`)
    }
  }
  
  // 检查开发依赖
  for (const [dep, version] of Object.entries(requiredDevDeps)) {
    if (!packageJson.devDependencies?.[dep]) {
      console.log(`❌ 缺少开发依赖: ${dep}`)
      needsInstall = true
    } else {
      console.log(`✅ 已安装: ${dep}@${packageJson.devDependencies[dep]}`)
    }
  }
  
  if (needsInstall) {
    console.log('📥 安装缺失的依赖...')
    
    // 安装生产依赖
    const prodDeps = Object.entries(requiredDeps)
      .filter(([dep]) => !packageJson.dependencies?.[dep])
      .map(([dep, version]) => `${dep}@${version}`)
    
    if (prodDeps.length > 0) {
      execSync(`npm install ${prodDeps.join(' ')}`, { stdio: 'inherit' })
    }
    
    // 安装开发依赖
    const devDeps = Object.entries(requiredDevDeps)
      .filter(([dep]) => !packageJson.devDependencies?.[dep])
      .map(([dep, version]) => `${dep}@${version}`)
    
    if (devDeps.length > 0) {
      execSync(`npm install -D ${devDeps.join(' ')}`, { stdio: 'inherit' })
    }
  }
}

// 步骤2：复制PDF.js静态资源
function copyPdfJsAssets() {
  console.log('📁 复制PDF.js静态资源...')
  
  // 确保目标目录存在
  if (!fs.existsSync(CONFIG.PUBLIC_DIR)) {
    fs.mkdirSync(CONFIG.PUBLIC_DIR, { recursive: true })
  }
  
  if (!fs.existsSync(CONFIG.PDFJS_DIR)) {
    fs.mkdirSync(CONFIG.PDFJS_DIR, { recursive: true })
  }
  
  // 复制web目录
  const webSrc = path.join(CONFIG.NODE_MODULES_PDFJS, 'web')
  const webDest = path.join(CONFIG.PDFJS_DIR, 'web')
  
  if (fs.existsSync(webSrc)) {
    console.log('📋 复制web目录...')
    copyDirectory(webSrc, webDest)
  } else {
    console.error('❌ 找不到pdfjs-dist/web目录')
    process.exit(1)
  }
  
  // 复制build目录
  const buildSrc = path.join(CONFIG.NODE_MODULES_PDFJS, 'build')
  const buildDest = path.join(CONFIG.PDFJS_DIR, 'build')
  
  if (fs.existsSync(buildSrc)) {
    console.log('📋 复制build目录...')
    copyDirectory(buildSrc, buildDest)
  } else {
    console.error('❌ 找不到pdfjs-dist/build目录')
    process.exit(1)
  }
  
  console.log('✅ PDF.js静态资源复制完成')
}

// 辅助函数：递归复制目录
function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }
  
  const entries = fs.readdirSync(src, { withFileTypes: true })
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name)
    const destPath = path.join(dest, entry.name)
    
    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
    }
  }
}

// 步骤3：配置Vite
function configureVite() {
  console.log('⚙️ 检查Vite配置...')
  
  const viteConfigPath = path.join(__dirname, '../vite.config.ts')
  
  if (!fs.existsSync(viteConfigPath)) {
    console.error('❌ 找不到vite.config.ts文件')
    return
  }
  
  const viteConfig = fs.readFileSync(viteConfigPath, 'utf8')
  
  // 检查是否已配置vite-plugin-static-copy
  if (!viteConfig.includes('vite-plugin-static-copy')) {
    console.log('⚠️ Vite配置需要手动更新')
    console.log('请确保vite.config.ts包含以下配置：')
    console.log(`
import { viteStaticCopy } from 'vite-plugin-static-copy'

export default defineConfig({
  plugins: [
    // ... 其他插件
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/pdfjs-dist/web/*',
          dest: 'pdfjs/web'
        },
        {
          src: 'node_modules/pdfjs-dist/build/pdf.worker.min.js',
          dest: 'pdfjs/build'
        },
        {
          src: 'node_modules/pdfjs-dist/build/pdf.min.js',
          dest: 'pdfjs/build'
        }
      ]
    })
  ]
})
    `)
  } else {
    console.log('✅ Vite配置已正确设置')
  }
}

// 步骤4：验证安装
function verifyInstallation() {
  console.log('🔍 验证安装...')
  
  const requiredFiles = [
    'public/pdfjs/web/viewer.html',
    'public/pdfjs/web/viewer.js',
    'public/pdfjs/build/pdf.min.js',
    'public/pdfjs/build/pdf.worker.min.js'
  ]
  
  let allFilesExist = true
  
  for (const file of requiredFiles) {
    const filePath = path.join(__dirname, '..', file)
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file}`)
    } else {
      console.log(`❌ ${file}`)
      allFilesExist = false
    }
  }
  
  if (allFilesExist) {
    console.log('🎉 PDF.js安装验证成功！')
    console.log('')
    console.log('📖 使用说明：')
    console.log('1. 使用PdfViewerOfficial.vue组件进行PDF.js官方查看器集成')
    console.log('2. 使用PdfViewerEnhanced.vue组件进行智能混合架构')
    console.log('3. 现有的DocumentPdfViewer.vue组件作为Canvas备用方案')
    console.log('')
    console.log('🔗 示例用法：')
    console.log(`
<template>
  <PdfViewerEnhanced
    :pdf-url="pdfUrl"
    :config="{ strategy: 'auto' }"
    @page-changed="onPageChanged"
    @document-loaded="onDocumentLoaded"
  />
</template>
    `)
  } else {
    console.error('❌ 安装验证失败，请检查错误信息')
    process.exit(1)
  }
}

// 主执行流程
async function main() {
  try {
    console.log('🎯 开始PDF.js官方查看器集成...')
    console.log('')
    
    checkDependencies()
    console.log('')
    
    copyPdfJsAssets()
    console.log('')
    
    configureVite()
    console.log('')
    
    verifyInstallation()
    
  } catch (error) {
    console.error('❌ 安装过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  checkDependencies,
  copyPdfJsAssets,
  configureVite,
  verifyInstallation
}
