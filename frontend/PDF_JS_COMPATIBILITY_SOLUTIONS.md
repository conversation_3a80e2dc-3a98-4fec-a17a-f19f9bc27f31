# PDF.js Vue 3 兼容性解决方案

## 问题描述
Vue 3 + PDF.js 3.11.174 出现 "Cannot read from private field" 错误

## 根本原因
- Vue 3 响应式系统将对象包装在 Proxy 中
- PDF.js 3.11.174 大量使用 ES2022 私有字段语法 (`#privateField`)
- Proxy 无法正确处理私有字段访问

## 解决方案

### 方案A: markRaw() 解决方案 ⭐ (推荐)
```javascript
import { markRaw } from 'vue'

// 在设置 pdfDocument 时使用 markRaw
pdfDocument.value = markRaw(pdfDoc)
```

**优点:**
- 保持最新 PDF.js 版本
- 最小代码修改
- 性能最优

**缺点:**
- 失去该对象的响应式特性

### 方案B: 版本降级解决方案
```bash
npm install pdfjs-dist@2.16.105
```

**优点:**
- 完全兼容 Vue 3
- 无需代码修改

**缺点:**
- 失去新版本功能
- 安全更新滞后

### 方案C: 原生引用解决方案
```javascript
// 存储原生引用
let nativePdfDocument = null

// 加载时
const pdfDoc = await loadingTask.promise
nativePdfDocument = pdfDoc
pdfDocument.value = pdfDoc // 仅用于响应式状态

// 使用时
const page = await nativePdfDocument.getPage(pageNum)
```

## 当前实施状态
✅ 已实施方案A (markRaw)
- 修改了 DocumentPdfViewer.vue
- 添加了错误处理和日志

## 测试建议
1. 测试 PDF 加载和渲染
2. 验证页面切换功能
3. 检查缩放和下载功能
4. 确认错误处理机制

## 如果问题仍然存在
考虑降级到 PDF.js 2.16.105:
```bash
npm uninstall pdfjs-dist
npm install pdfjs-dist@2.16.105
```
