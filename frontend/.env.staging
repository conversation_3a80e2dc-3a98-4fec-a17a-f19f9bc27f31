# 本地开发环境

# 页面标题

VITE_APP_TITLE = AI智能问答

# 开发环境配置
VITE_APP_ENV = 'production'

# 管理系统/开发环境 ************
VITE_APP_BASE_API = 'http://**************/api'

# ai智能问答接口 - 使用代理避免CORS问题 http://**************:88 /maxkb-api
VITE_APP_AI_BASE_API = 'http://**************/qa-api/api/application'

# ai智能问答接口appID ad920610-4a90-11f0-8e38-2a7ae2834ee8
VITE_AI_APP_ID='ad920610-4a90-11f0-8e38-2a7ae2834ee8'

# websocket地址
VITE_WS_BASE_URL='wss://wss.lke.cloud.tencent.com'

# 接口地址
VITE_API_URL=/


# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip