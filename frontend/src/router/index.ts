import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'



const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    redirect: '/ai-customer'
  },
  {
    path: '/document-analysis',
    name: 'DocumentAnalysis',
    component: () => import('@/views/DocumentAnalysis.vue'),
    meta: {
      title: '文档智能分析'
    }
  },
  {
    path: '/ai-customer',
    name: 'AICustomerService',
    component: () => import('@/views/ai/index.vue'),
    meta: {
      title: 'AI智能客服'
    }
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

// Navigation guards
router.beforeEach((to, from, next) => {
  // Set page title
  if (to.meta?.title) {
    document.title = `${to.meta.title} `
  } else {
    document.title = '文档智能分析系统'
  }
  next()
})

export default router
