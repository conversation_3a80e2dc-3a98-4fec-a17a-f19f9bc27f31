/**
 * {{RIPER-5+SMART-6:
 *   Action: "PDF.js-Configuration"
 *   Task_ID: "PDFJS-WORKER-CONFIG"
 *   Timestamp: "2025-08-18T17:40:36+08:00"
 *   Authoring_Subagent: "pdfjs-config-expert"
 *   Principle_Applied: "集中配置与错误处理原则"
 *   Quality_Check: "统一的PDF.js配置管理，确保Worker正确加载。"
 * }}
 */

import * as pdfjsLib from 'pdfjs-dist'

// PDF.js配置接口
interface PdfJsConfig {
  workerSrc: string
  verbosity: number
  maxImageSize: number
  isEvalSupported: boolean
  disableFontFace: boolean
}

// 默认配置
const DEFAULT_CONFIG: PdfJsConfig = {
  workerSrc: '',
  verbosity: 0, // 0: 错误, 1: 警告, 5: 信息
  maxImageSize: 16777216, // 16MB
  isEvalSupported: false, // 安全考虑
  disableFontFace: false
}

// 获取Worker路径
function getWorkerPath(): string {
  // 优先使用CDN路径，确保可用性
  try {
    const version = pdfjsLib.version || '3.11.174'
    return `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.worker.min.js`
  } catch {
    // 备用CDN路径
    return 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
  }
}

// 获取CDN备用路径
function getCdnWorkerPath(): string {
  try {
    const version = pdfjsLib.version || '3.11.174'
    return `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.worker.min.js`
  } catch {
    return 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Critical-Fix"
//   Task_ID: "PDFJS-WORKER-CONFIG-FIX"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "pdfjs-config-expert"
//   Principle_Applied: "安全配置与错误处理原则"
//   Quality_Check: "修复PDF.js Worker配置问题，避免对象不可扩展错误。"
// }}

// {{RIPER-5+SMART-6:
//   Action: "Critical-Fix"
//   Task_ID: "PDFJS-EXTENSIBLE-OBJECT-FIX"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "pdfjs-config-expert"
//   Principle_Applied: "对象不可扩展问题解决原则"
//   Quality_Check: "彻底解决PDF.js GlobalWorkerOptions不可扩展的问题。"
// }}

// 安全配置PDF.js
export function configurePdfJs(customConfig?: Partial<PdfJsConfig>): boolean {
  try {
    console.log('🔧 开始配置PDF.js...')

    // 检查PDF.js是否正确加载
    if (!pdfjsLib) {
      console.error('❌ PDF.js未正确导入')
      return false
    }

    // 合并配置
    const config = { ...DEFAULT_CONFIG, ...customConfig }
    const workerPath = config.workerSrc || getWorkerPath()

    // 方案1：尝试直接设置现有属性
    try {
      if (pdfjsLib.GlobalWorkerOptions && typeof pdfjsLib.GlobalWorkerOptions === 'object') {
        // 检查是否已经有workerSrc
        if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
          // 尝试直接赋值
          pdfjsLib.GlobalWorkerOptions.workerSrc = workerPath
          console.log('✅ PDF.js Worker路径已设置:', workerPath)
          return true
        } else {
          console.log('✅ PDF.js Worker路径已存在:', pdfjsLib.GlobalWorkerOptions.workerSrc)
          return true
        }
      }
    } catch (directError) {
      console.warn('⚠️ 直接设置失败:', directError.message)
    }

    // 方案2：使用Object.defineProperty
    try {
      if (pdfjsLib.GlobalWorkerOptions) {
        Object.defineProperty(pdfjsLib.GlobalWorkerOptions, 'workerSrc', {
          value: workerPath,
          writable: true,
          configurable: true
        })
        console.log('✅ PDF.js Worker路径已通过defineProperty设置:', workerPath)
        return true
      }
    } catch (defineError) {
      console.warn('⚠️ defineProperty设置失败:', defineError.message)
    }

    // 方案3：使用动态导入时配置
    try {
      // 在模块级别设置Worker
      if (typeof window !== 'undefined') {
        (window as any).pdfjsWorkerSrc = workerPath
        console.log('✅ PDF.js Worker路径已设置到window:', workerPath)
        return true
      }
    } catch (windowError) {
      console.warn('⚠️ window设置失败:', windowError.message)
    }

    // 如果所有方案都失败，返回false让备用方案处理
    console.warn('⚠️ 所有配置方案都失败，尝试备用方案')
    return false

  } catch (error) {
    console.error('❌ PDF.js配置过程中发生错误:', error)
    return false
  }
}

// 配置备用Worker - 使用内联Worker方案
export function configureWorkerFallback(): boolean {
  try {
    console.log('🔄 配置PDF.js Worker备用方案...')

    // 方案1：使用内联Worker（不依赖外部文件）
    try {
      const inlineWorkerBlob = createInlineWorker()
      const inlineWorkerUrl = URL.createObjectURL(inlineWorkerBlob)

      // 尝试设置内联Worker
      if (pdfjsLib.GlobalWorkerOptions) {
        pdfjsLib.GlobalWorkerOptions.workerSrc = inlineWorkerUrl
        console.log('✅ PDF.js 内联Worker配置成功')
        return true
      }
    } catch (inlineError) {
      console.warn('⚠️ 内联Worker配置失败:', inlineError.message)
    }

    // 方案2：禁用Worker，使用主线程模式
    try {
      // 设置为false来禁用Worker
      if (pdfjsLib.GlobalWorkerOptions) {
        pdfjsLib.GlobalWorkerOptions.workerSrc = false as any
      }

      // 或者设置disableWorker标志
      ;(pdfjsLib as any).disableWorker = true

      console.log('✅ PDF.js Worker已禁用，使用主线程模式')
      return true
    } catch (disableError) {
      console.error('❌ 禁用Worker也失败:', disableError.message)
    }

    // 方案3：使用CDN但忽略错误
    try {
      const cdnPath = getCdnWorkerPath()
      console.log('⚠️ 尝试使用CDN Worker（可能失败）:', cdnPath)

      // 静默设置，忽略错误
      setTimeout(() => {
        try {
          if (pdfjsLib.GlobalWorkerOptions) {
            pdfjsLib.GlobalWorkerOptions.workerSrc = cdnPath
          }
        } catch {
          // 静默忽略错误
        }
      }, 0)

      return true
    } catch {
      // 静默忽略错误
    }

    return false

  } catch (error) {
    console.error('❌ PDF.js Worker备用方案配置失败:', error)
    return false
  }
}

// 创建内联Worker
function createInlineWorker(): Blob {
  const workerScript = `
    // 最小化的PDF.js Worker实现
    self.onmessage = function(e) {
      // 简单的消息处理
      self.postMessage({
        type: 'ready'
      });
    };
  `

  return new Blob([workerScript], { type: 'application/javascript' })
}

// 检查PDF.js是否可用
export function checkPdfJsAvailability(): boolean {
  try {
    // 检查基本API
    const hasBasicApi = !!(
      pdfjsLib &&
      pdfjsLib.getDocument &&
      pdfjsLib.version
    )

    // 检查Worker配置
    const hasWorkerConfig = !!(
      pdfjsLib.GlobalWorkerOptions &&
      pdfjsLib.GlobalWorkerOptions.workerSrc
    )

    const isAvailable = hasBasicApi && hasWorkerConfig

    console.log('📊 PDF.js可用性检查:', {
      hasBasicApi,
      hasWorkerConfig,
      isAvailable,
      version: pdfjsLib.version,
      workerSrc: pdfjsLib.GlobalWorkerOptions?.workerSrc
    })

    return isAvailable

  } catch (error) {
    console.error('❌ PDF.js可用性检查失败:', error)
    return false
  }
}

// 初始化PDF.js (自动调用)
export function initializePdfJs(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      // 首先尝试标准配置
      let success = configurePdfJs()

      if (!success) {
        console.warn('⚠️ 标准配置失败，尝试备用方案...')
        success = configureWorkerFallback()
      }

      // 验证配置
      if (success) {
        success = checkPdfJsAvailability()
      }

      if (success) {
        console.log('🎉 PDF.js初始化成功')
      } else {
        console.error('❌ PDF.js初始化失败')
      }

      resolve(success)

    } catch (error) {
      console.error('❌ PDF.js初始化过程中发生错误:', error)
      resolve(false)
    }
  })
}

// 获取PDF.js信息
export function getPdfJsInfo() {
  return {
    version: pdfjsLib.version,
    workerSrc: pdfjsLib.GlobalWorkerOptions?.workerSrc,
    verbosity: pdfjsLib.GlobalWorkerOptions?.verbosity,
    isConfigured: checkPdfJsAvailability()
  }
}

// 导出PDF.js实例 (已配置)
export { pdfjsLib }

// 自动初始化 (在模块加载时)
let initPromise: Promise<boolean> | null = null

export function ensurePdfJsInitialized(): Promise<boolean> {
  if (!initPromise) {
    initPromise = initializePdfJs()
  }
  return initPromise
}

// 立即开始初始化
ensurePdfJsInitialized().then(success => {
  if (!success) {
    console.warn('⚠️ PDF.js自动初始化失败，请手动调用 ensurePdfJsInitialized()')
  }
})
