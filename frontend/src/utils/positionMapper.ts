

export interface DocumentPosition {
  page: number
  paragraphIndex: number
  start: number
  end: number
  elementId: string
  textContent?: string
}

export interface HighlightPosition {
  x: number
  y: number
  width: number
  height: number
  elementId: string
  boundingRect: DOMRect
}

export interface PositionMapping {
  documentPosition: DocumentPosition
  highlightPosition: HighlightPosition
  confidence: number
  matchType: 'exact' | 'fuzzy' | 'semantic'
}

/**
 * 位置映射管理器
 */
export class PositionMapper {
  private documentContainer: HTMLElement | null = null
  private positionCache: Map<string, PositionMapping> = new Map()
  private textNodeMap: Map<string, Text[]> = new Map()

  constructor(container?: HTMLElement) {
    if (container) {
      this.setDocumentContainer(container)
    }
  }

  /**
   * 设置文档容器
   */
  setDocumentContainer(container: HTMLElement) {
    this.documentContainer = container
    this.buildTextNodeMap()
  }

  /**
   * 构建文本节点映射
   */
  private buildTextNodeMap() {
    if (!this.documentContainer) return

    this.textNodeMap.clear()
    const walker = document.createTreeWalker(
      this.documentContainer,
      NodeFilter.SHOW_TEXT,
      null
    )

    let node: Node | null
    while (node = walker.nextNode()) {
      const textNode = node as Text
      const parentElement = textNode.parentElement
      if (parentElement) {
        const elementId = parentElement.id || this.generateElementId(parentElement)
        if (!parentElement.id) {
          parentElement.id = elementId
        }

        if (!this.textNodeMap.has(elementId)) {
          this.textNodeMap.set(elementId, [])
        }
        this.textNodeMap.get(elementId)!.push(textNode)
      }
    }
  }

  /**
   * 生成元素ID
   */
  private generateElementId(element: HTMLElement): string {
    const tagName = element.tagName.toLowerCase()
    const className = element.className ? `-${element.className.replace(/\s+/g, '-')}` : ''
    const index = Array.from(element.parentNode?.children || []).indexOf(element)
    return `${tagName}${className}-${index}-${Date.now()}`
  }

  /**
   * 根据文档位置查找DOM位置
   */
  findDOMPosition(documentPosition: DocumentPosition): PositionMapping | null {
    const cacheKey = this.getCacheKey(documentPosition)
    if (this.positionCache.has(cacheKey)) {
      return this.positionCache.get(cacheKey)!
    }

    let mapping: PositionMapping | null = null

    // 1. 尝试精确匹配
    if (documentPosition.elementId) {
      mapping = this.findByElementId(documentPosition)
    }

    // 2. 尝试文本内容匹配
    if (!mapping && documentPosition.textContent) {
      mapping = this.findByTextContent(documentPosition)
    }

    // 3. 尝试位置索引匹配
    if (!mapping) {
      mapping = this.findByPositionIndex(documentPosition)
    }

    if (mapping) {
      this.positionCache.set(cacheKey, mapping)
    }

    return mapping
  }

  /**
   * 通过元素ID查找
   */
  private findByElementId(documentPosition: DocumentPosition): PositionMapping | null {
    const element = document.getElementById(documentPosition.elementId)
    if (!element) return null

    const rect = element.getBoundingClientRect()
    const containerRect = this.documentContainer?.getBoundingClientRect()
    
    if (!containerRect) return null

    return {
      documentPosition,
      highlightPosition: {
        x: rect.left - containerRect.left,
        y: rect.top - containerRect.top,
        width: rect.width,
        height: rect.height,
        elementId: documentPosition.elementId,
        boundingRect: rect
      },
      confidence: 1.0,
      matchType: 'exact'
    }
  }

  /**
   * 通过文本内容查找
   */
  private findByTextContent(documentPosition: DocumentPosition): PositionMapping | null {
    if (!documentPosition.textContent || !this.documentContainer) return null

    const searchText = documentPosition.textContent.trim()
    const walker = document.createTreeWalker(
      this.documentContainer,
      NodeFilter.SHOW_TEXT,
      null
    )

    let bestMatch: { element: HTMLElement; confidence: number; range: Range } | null = null
    let node: Node | null

    while (node = walker.nextNode()) {
      const textNode = node as Text
      const textContent = textNode.textContent || ''
      
      // 精确匹配
      const exactIndex = textContent.indexOf(searchText)
      if (exactIndex !== -1) {
        const range = document.createRange()
        range.setStart(textNode, exactIndex)
        range.setEnd(textNode, exactIndex + searchText.length)
        
        const parentElement = textNode.parentElement
        if (parentElement) {
          bestMatch = {
            element: parentElement,
            confidence: 1.0,
            range
          }
          break
        }
      }

      // 模糊匹配
      const similarity = this.calculateTextSimilarity(textContent, searchText)
      if (similarity > 0.8) {
        const parentElement = textNode.parentElement
        if (parentElement && (!bestMatch || similarity > bestMatch.confidence)) {
          const range = document.createRange()
          range.selectNodeContents(textNode)
          
          bestMatch = {
            element: parentElement,
            confidence: similarity,
            range
          }
        }
      }
    }

    if (bestMatch) {
      const rect = bestMatch.range.getBoundingClientRect()
      const containerRect = this.documentContainer.getBoundingClientRect()

      return {
        documentPosition,
        highlightPosition: {
          x: rect.left - containerRect.left,
          y: rect.top - containerRect.top,
          width: rect.width,
          height: rect.height,
          elementId: bestMatch.element.id || this.generateElementId(bestMatch.element),
          boundingRect: rect
        },
        confidence: bestMatch.confidence,
        matchType: bestMatch.confidence === 1.0 ? 'exact' : 'fuzzy'
      }
    }

    return null
  }

  /**
   * 通过位置索引查找
   */
  private findByPositionIndex(documentPosition: DocumentPosition): PositionMapping | null {
    if (!this.documentContainer) return null

    // 根据段落索引查找
    const paragraphs = this.documentContainer.querySelectorAll('.document-paragraph, p, div')
    const targetParagraph = paragraphs[documentPosition.paragraphIndex]
    
    if (targetParagraph) {
      const rect = targetParagraph.getBoundingClientRect()
      const containerRect = this.documentContainer.getBoundingClientRect()

      return {
        documentPosition,
        highlightPosition: {
          x: rect.left - containerRect.left,
          y: rect.top - containerRect.top,
          width: rect.width,
          height: rect.height,
          elementId: targetParagraph.id || this.generateElementId(targetParagraph as HTMLElement),
          boundingRect: rect
        },
        confidence: 0.7,
        matchType: 'semantic'
      }
    }

    return null
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    const longer = text1.length > text2.length ? text1 : text2
    const shorter = text1.length > text2.length ? text2 : text1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  /**
   * 计算编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        )
      }
    }

    return matrix[str2.length][str1.length]
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(position: DocumentPosition): string {
    return `${position.elementId}-${position.start}-${position.end}-${position.paragraphIndex}`
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.positionCache.clear()
  }

  /**
   * 获取映射统计信息
   */
  getStats() {
    const mappings = Array.from(this.positionCache.values())
    const exactCount = mappings.filter(m => m.matchType === 'exact').length
    const fuzzyCount = mappings.filter(m => m.matchType === 'fuzzy').length
    const semanticCount = mappings.filter(m => m.matchType === 'semantic').length
    
    const avgConfidence = mappings.length > 0 
      ? mappings.reduce((sum, m) => sum + m.confidence, 0) / mappings.length 
      : 0

    return {
      totalMappings: mappings.length,
      exactMatches: exactCount,
      fuzzyMatches: fuzzyCount,
      semanticMatches: semanticCount,
      averageConfidence: avgConfidence,
      accuracyRate: exactCount / Math.max(mappings.length, 1)
    }
  }
}

/**
 * 创建位置映射器实例
 */
export function createPositionMapper(container?: HTMLElement): PositionMapper {
  return new PositionMapper(container)
}

/**
 * 滚动到指定位置
 */
export function scrollToPosition(mapping: PositionMapping, behavior: ScrollBehavior = 'smooth') {
  const element = document.getElementById(mapping.highlightPosition.elementId)
  if (element) {
    element.scrollIntoView({
      behavior,
      block: 'center',
      inline: 'nearest'
    })
  }
}
