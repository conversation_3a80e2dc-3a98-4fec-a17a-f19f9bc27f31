export const deepClone = function (origin, target: object = {}) {
  for (const prop in target) {
    if (target[prop] !== null && typeof target[prop] === 'object') {
      origin[prop] = Object.prototype.toString.call(target[prop]) === '[object Array]' ? [] : {}
      deepClone(origin[prop], target[prop])
    } else {
      origin[prop] = target[prop]
    }
  }
}
/**
 * 获取页面参数
 * @param variable 参数名
 * @returns 参数值
 */
export const getQueryVariable = (variable: string): string => {
  try {
    const pages = getCurrentPages()
    if (!pages || pages.length === 0) {
      console.warn('getCurrentPages() returned empty array')
      return ''
    }

    const currentPage = pages[pages.length - 1]
    const options = currentPage.options || {}

    // 直接从页面参数中获取
    if (options[variable]) {
      return decodeURIComponent(options[variable])
    }

    // 从页面完整路径中解析
    const fullPath =
      currentPage.route +
      '?' +
      Object.keys(options)
        .map(key => `${key}=${options[key]}`)
        .join('&')

    const queryString = fullPath.split('?')[1]
    if (!queryString) {
      return ''
    }

    const params = new Map()
    queryString.split('&').forEach(item => {
      const [key, value] = item.split('=')
      if (key && value) {
        params.set(key, decodeURIComponent(value))
      }
    })

    return params.get(variable) || ''
  } catch (error) {
    console.error('获取页面参数失败:', error)
    return ''
  }
}

/**
 * 滚动至指定dom底部
 * @param sDom DOM元素
 * @param sTop 滚动位置
 */
export const scrollToBottom = (sDom: HTMLElement, sTop: number): void => {
  if (!sDom) return

  sDom.scrollTo({
    top: sTop
    // behavior: 'smooth'
  })
}

/**
 * 数组去重
 * @param arr 需要去重的数组
 * @param replaceKey 用于比较的键
 * @param holdKey 需要保持的键
 */
interface TempItem {
  index: number
  [key: string]: any
}

interface TempObject {
  [key: string]: TempItem
}

export const arrayUnique = <T extends { [key: string]: any }>(arr: T[], replaceKey: string | number, holdKey: string | number): T[] => {
  const temp: TempObject = {}

  return arr.reduce((prev: T[], cur: T) => {
    if (!temp[cur[replaceKey]]) {
      temp[cur[replaceKey]] = { index: prev.length }
      prev.push(cur)
    } else {
      const oldItem = temp[cur[replaceKey]]
      cur[holdKey] = oldItem[holdKey]
      prev.splice(oldItem.index, 1, cur)
    }

    return prev
  }, [])
}

/**
 * 生成请求ID
 * @param length ID长度
 * @returns 生成的请求ID
 */
export const generateRequestId = (length: number = 10): string => {
  const data: string[] = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z'
  ]
  let nums: string = ''
  for (let i = 0; i < length; i++) {
    const r: number = parseInt(String(Math.random() * 61), 10)
    nums += data[r]
  }
  return `${nums}-${parseInt(String(Math.random() * 10000000000), 10)}`
}

/**
 * HTML转义映射
 */
interface HtmlEscapeMap {
  [key: string]: string
}

const HTML_ESCAPE_MAP: HtmlEscapeMap = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#39;',
  '/': '&#x2F;'
}

/**
 * 转义HTML字符串
 * @param str 需要转义的字符串
 */
function escapeHtml(str: string): string {
  return str.replace(/[&<>"'/]/g, (match: string): string => HTML_ESCAPE_MAP[match])
}

/**
 * 转义用户输入
 * @param input 用户输入的字符串
 */
export const escapeUserInput = (input: string): string => {
  const parts: string[] = input.split(/(<script[^>]*>.*?<\/script>|<[^>]*>)/gi)
  return parts
    .map((part: string): string => {
      if (part.startsWith('<script') || part.startsWith('<')) {
        return escapeHtml(part)
      }
      return part
    })
    .join('')
}
