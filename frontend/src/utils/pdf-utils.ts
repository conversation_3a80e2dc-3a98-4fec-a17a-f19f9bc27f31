/**
 * PDF 工具函数
 * 用于处理各种格式的PDF文件流数据
 */

/**
 * 将Base64字符串转换为ArrayBuffer
 * @param base64 Base64字符串
 * @returns ArrayBuffer
 */
export function base64ToArrayBuffer(base64: string): ArrayBuffer {
  // 移除data:application/pdf;base64,前缀（如果存在）
  const cleanBase64 = base64.replace(/^data:application\/pdf;base64,/, '')

  const binaryString = window.atob(cleanBase64)
  const bytes = new Uint8Array(binaryString.length)

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }

  return bytes.buffer
}

/**
 * 将Blob转换为ArrayBuffer
 * @param blob Blob对象
 * @returns Promise<ArrayBuffer>
 */
export function blobToArrayBuffer(blob: Blob): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as ArrayBuffer)
    reader.onerror = reject
    reader.readAsArrayBuffer(blob)
  })
}

/**
 * 专门处理后台返回的 Blob 格式 PDF 文件流
 * @param response 后台返回的响应（可能是 Blob 或包含 Blob 的对象）
 * @returns Promise<ArrayBuffer>
 */
export async function processBlobPdfResponse(response: any): Promise<ArrayBuffer> {
  // 情况1: 直接是 Blob 对象
  if (response instanceof Blob) {
    // 验证是否为 PDF Blob
    if (response.type && !response.type.includes('pdf')) {
      console.warn('Blob 类型不是 PDF:', response.type)
    }

    return await blobToArrayBuffer(response)
  }

  // 情况2: axios 响应包装的 Blob { data: Blob, status: 200, ... }
  if (response && response.data && response.data instanceof Blob) {
    console.log('检测到 axios 包装的 Blob 格式')
    return await blobToArrayBuffer(response.data)
  }

  // 情况3: 自定义包装的 Blob { success: true, data: Blob, ... }
  if (response && response.success && response.data instanceof Blob) {
    console.log('检测到自定义包装的 Blob 格式')
    return await blobToArrayBuffer(response.data)
  }

  // 情况4: 包含 blob 字段的对象
  if (response && response.blob instanceof Blob) {
    console.log('检测到 blob 字段')
    return await blobToArrayBuffer(response.blob)
  }

  // 情况5: 包含 file 字段的 Blob
  if (response && response.file instanceof Blob) {
    console.log('检测到 file 字段的 Blob')
    return await blobToArrayBuffer(response.file)
  }

  throw new Error(`不是有效的 Blob PDF 响应: ${typeof response}`)
}

/**
 * 专门处理后台返回的PDF文件流
 * @param response 后台API响应
 * @returns Promise<ArrayBuffer | string>
 */
export async function processBackendPdfResponse(response: any): Promise<ArrayBuffer | string> {
  console.log('开始处理后台PDF响应:', response)

  // 情况1: 直接返回ArrayBuffer（axios responseType: 'arraybuffer'）
  if (response instanceof ArrayBuffer) {
    console.log('检测到ArrayBuffer格式')
    return response
  }

  // 情况2: 直接返回Uint8Array
  if (response instanceof Uint8Array) {
    console.log('检测到Uint8Array格式')
    return response.buffer
  }

  // 情况3: 直接返回Blob
  if (response instanceof Blob) {
    console.log('检测到Blob格式')
    return await blobToArrayBuffer(response)
  }

  // 情况4: axios包装的响应对象 { data: ArrayBuffer, status: 200, ... }
  if (response && response.data) {
    console.log('检测到axios响应格式，处理data字段')
    return await processBackendPdfResponse(response.data)
  }

  // 情况5: 自定义响应格式 { success: true, data: ArrayBuffer, message: '' }
  if (response && response.success && response.data) {
    console.log('检测到自定义成功响应格式')
    return await processBackendPdfResponse(response.data)
  }

  // 情况6: Base64编码的PDF
  if (typeof response === 'string') {
    if (response.startsWith('data:application/pdf;base64,')) {
      console.log('检测到Base64 PDF格式')
      return base64ToArrayBuffer(response)
    }
    // 可能是URL
    if (response.includes('.pdf') || response.includes('/pdf/')) {
      console.log('检测到PDF URL格式')
      return response
    }
  }

  // 情况7: 包含base64字段的对象
  if (response && response.base64) {
    console.log('检测到base64字段')
    return base64ToArrayBuffer(response.base64)
  }

  // 情况8: 包含file字段的对象
  if (response && response.file) {
    console.log('检测到file字段')
    return await processBackendPdfResponse(response.file)
  }

  // 情况9: 包含buffer字段的对象
  if (response && response.buffer) {
    console.log('检测到buffer字段')
    return await processBackendPdfResponse(response.buffer)
  }

  // 情况10: Node.js Buffer对象（转换为ArrayBuffer）
  if (response && response.type === 'Buffer' && response.data) {
    console.log('检测到Node.js Buffer格式')
    const uint8Array = new Uint8Array(response.data)
    return uint8Array.buffer
  }

  throw new Error(`不支持的PDF数据格式: ${typeof response}`)
}

/**
 * 处理API响应，提取PDF数据
 * @param response API响应数据
 * @returns Promise<ArrayBuffer | string>
 */
export async function processPdfResponse(response: any): Promise<ArrayBuffer | string> {
  // 1. 如果是字符串（URL）
  if (typeof response === 'string') {
    return response
  }

  // 2. 如果是ArrayBuffer
  if (response instanceof ArrayBuffer) {
    return response
  }

  // 3. 如果是Uint8Array
  if (response instanceof Uint8Array) {
    return response.buffer
  }

  // 4. 如果是Blob
  if (response instanceof Blob) {
    return await blobToArrayBuffer(response)
  }

  // 5. 如果是包含data字段的对象
  if (response && response.data) {
    return await processPdfResponse(response.data)
  }

  // 6. 如果是Base64字符串
  if (typeof response === 'string' && response.includes('base64')) {
    return base64ToArrayBuffer(response)
  }

  // 7. 如果是包含base64字段的对象
  if (response && response.base64) {
    return base64ToArrayBuffer(response.base64)
  }

  // 8. 如果是包含url字段的对象
  if (response && response.url) {
    return response.url
  }

  // 9. 如果是包含file字段的对象
  if (response && response.file) {
    return await processPdfResponse(response.file)
  }

  throw new Error('不支持的PDF数据格式')
}

/**
 * 验证是否为有效的PDF数据
 * @param data PDF数据
 * @returns boolean
 */
export function isValidPdfData(data: any): boolean {
  if (typeof data === 'string') {
    // URL格式
    return data.includes('.pdf') || data.includes('pdf')
  }

  if (data instanceof ArrayBuffer) {
    // 检查PDF文件头
    const uint8Array = new Uint8Array(data)
    const header = String.fromCharCode(...uint8Array.slice(0, 4))
    return header === '%PDF'
  }

  if (data instanceof Uint8Array) {
    const header = String.fromCharCode(...data.slice(0, 4))
    return header === '%PDF'
  }

  return false
}

/**
 * 创建PDF下载链接
 * @param data PDF数据
 * @param filename 文件名
 */
export function downloadPdf(data: ArrayBuffer | Blob, filename: string = 'document.pdf') {
  let blob: Blob

  if (data instanceof ArrayBuffer) {
    blob = new Blob([data], { type: 'application/pdf' })
  } else {
    blob = data
  }

  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

/**
 * 获取PDF文件信息
 * @param data PDF数据
 * @returns Promise<{size: number, pages?: number}>
 */
export async function getPdfInfo(data: ArrayBuffer): Promise<{ size: number; pages?: number }> {
  const size = data.byteLength

  try {
    // 动态导入pdfjs-dist以避免打包问题
    const pdfjsLib = await import('pdfjs-dist')

    // 配置worker
    if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`
    }

    const pdf = await pdfjsLib.getDocument({ data }).promise
    const pages = pdf.numPages

    return { size, pages }
  } catch (error) {
    console.warn('无法获取PDF页数:', error)
    return { size }
  }
}

/**
 * 压缩PDF数据（简单的质量降低）
 * @param data PDF数据
 * @param quality 质量（0-1）
 * @returns Promise<ArrayBuffer>
 */
export async function compressPdf(data: ArrayBuffer, quality: number = 0.8): Promise<ArrayBuffer> {
  // 注意：这是一个简化的示例，实际的PDF压缩需要更复杂的处理
  // 在生产环境中，建议使用专门的PDF处理库或后端服务

  if (quality >= 1) {
    return data
  }

  try {
    const pdfjsLib = await import('pdfjs-dist')

    if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`
    }

    const pdf = await pdfjsLib.getDocument({ data }).promise

    // 这里只是一个示例，实际压缩需要重新生成PDF
    // 可以通过降低渲染质量来模拟压缩效果
    console.log('PDF压缩功能需要专门的库支持')

    return data
  } catch (error) {
    console.error('PDF压缩失败:', error)
    return data
  }
}

/**
 * 将PDF转换为图片
 * @param data PDF数据
 * @param pageNumber 页码（从1开始）
 * @param scale 缩放比例
 * @returns Promise<string> Base64图片数据
 */
export async function pdfToImage(data: ArrayBuffer, pageNumber: number = 1, scale: number = 1.5): Promise<string> {
  const pdfjsLib = await import('pdfjs-dist')

  if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
    pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`
  }

  const pdf = await pdfjsLib.getDocument({ data }).promise
  const page = await pdf.getPage(pageNumber)
  const viewport = page.getViewport({ scale })

  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')!
  canvas.height = viewport.height
  canvas.width = viewport.width

  const renderContext = {
    canvasContext: context,
    viewport: viewport
  }

  await page.render(renderContext).promise

  return canvas.toDataURL('image/png')
}

/**
 * 批量处理PDF页面转图片
 * @param data PDF数据
 * @param scale 缩放比例
 * @returns Promise<string[]> Base64图片数组
 */
export async function pdfToImages(data: ArrayBuffer, scale: number = 1.5): Promise<string[]> {
  const pdfjsLib = await import('pdfjs-dist')

  if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
    pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`
  }

  const pdf = await pdfjsLib.getDocument({ data }).promise
  const images: string[] = []

  for (let i = 1; i <= pdf.numPages; i++) {
    const image = await pdfToImage(data, i, scale)
    images.push(image)
  }

  return images
}

/**
 * 错误处理工具
 */
export class PdfError extends Error {
  constructor(
    message: string,
    public code?: string
  ) {
    super(message)
    this.name = 'PdfError'
  }
}

/**
 * PDF处理状态枚举
 */
export enum PdfProcessStatus {
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
  PROCESSING = 'processing'
}

/**
 * PDF文件类型检查
 */
export function isPdfFile(file: File): boolean {
  return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 创建 ArrayBuffer 的安全副本，避免分离问题
 * @param buffer 原始 ArrayBuffer
 * @returns ArrayBuffer 副本
 */
export function cloneArrayBuffer(buffer: ArrayBuffer): ArrayBuffer {
  const cloned = new ArrayBuffer(buffer.byteLength)
  new Uint8Array(cloned).set(new Uint8Array(buffer))
  return cloned
}

/**
 * 安全地处理 PDF 数据，避免 ArrayBuffer 分离问题
 * @param data PDF 数据
 * @returns 处理后的安全数据
 */
export function safePdfData(data: any): any {
  if (data instanceof ArrayBuffer) {
    // 创建副本避免分离
    return cloneArrayBuffer(data)
  } else if (data instanceof Uint8Array) {
    // 从 Uint8Array 创建新的 ArrayBuffer
    return data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength)
  }
  return data
}

/**
 * 修复 PDF.js 的 ArrayBuffer 分离问题
 * @param data 原始数据
 * @returns 修复后的数据
 */
export function fixPdfJsArrayBuffer(data: any): any {
  if (data instanceof ArrayBuffer) {
    // 检查 ArrayBuffer 是否已分离
    try {
      new Uint8Array(data)
      return data // 如果没有错误，说明没有分离
    } catch (error) {
      console.warn('检测到分离的 ArrayBuffer，无法修复')
      throw new Error('ArrayBuffer 已分离，请重新获取数据')
    }
  }
  return data
}

/**
 * 完整的 Blob 到 PDF.js 处理流程
 * @param blobResponse 后台接口返回的 Blob 响应
 * @returns Promise<ArrayBuffer> 可用于 PDF.js 的 ArrayBuffer
 */
export async function processBlobToPdfjs(blobResponse: any): Promise<ArrayBuffer> {
  console.log('=== 开始 Blob 到 PDF.js 处理流程 ===')
  console.log('输入数据类型:', typeof blobResponse)
  console.log('是否为 Blob:', blobResponse instanceof Blob)

  let arrayBuffer: ArrayBuffer

  try {
    // 处理不同格式的 Blob 响应
    if (blobResponse instanceof Blob) {
      console.log('直接处理 Blob 对象')
      console.log('Blob 大小:', blobResponse.size, 'bytes')
      console.log('Blob 类型:', blobResponse.type)

      arrayBuffer = await blobResponse.arrayBuffer()
    } else if (blobResponse && blobResponse.data instanceof Blob) {
      console.log('从 axios 响应中提取 Blob')
      arrayBuffer = await blobResponse.data.arrayBuffer()
    } else if (blobResponse && blobResponse.blob instanceof Blob) {
      console.log('从自定义响应格式中提取 Blob')
      arrayBuffer = await blobResponse.blob.arrayBuffer()
    } else if (blobResponse && typeof blobResponse === 'object') {
      console.log('从对象响应中查找 Blob')

      // 查找可能的 Blob 字段
      const blobFields = ['data', 'blob', 'file', 'content', 'pdf']
      let foundBlob = null

      for (const field of blobFields) {
        if (blobResponse[field] instanceof Blob) {
          foundBlob = blobResponse[field]
          console.log(`在 ${field} 字段找到 Blob`)
          break
        }
      }

      if (foundBlob) {
        arrayBuffer = await foundBlob.arrayBuffer()
      } else {
        throw new Error('响应对象中未找到有效的 Blob 数据')
      }
    } else {
      throw new Error(`无效的 Blob 响应格式: ${typeof blobResponse}`)
    }

    // 验证 ArrayBuffer
    if (!arrayBuffer || arrayBuffer.byteLength === 0) {
      throw new Error('转换后的 ArrayBuffer 为空')
    }

    console.log('ArrayBuffer 转换成功，大小:', arrayBuffer.byteLength, 'bytes')

    // 验证 PDF 文件头
    const header = new Uint8Array(arrayBuffer.slice(0, 4))
    const headerString = String.fromCharCode(...header)

    if (!headerString.startsWith('%PDF')) {
      console.warn('警告: 文件头不是标准 PDF 格式:', headerString)
      // 但仍然返回数据，让 PDF.js 尝试处理
    } else {
      console.log('PDF 文件头验证通过:', headerString)
    }

    // 创建安全的数据副本避免 ArrayBuffer 分离问题
    const safeArrayBuffer = arrayBuffer.slice(0)
    console.log('创建安全数据副本完成')

    return safeArrayBuffer
  } catch (error) {
    console.error('Blob 处理失败:', error)
    throw new Error(`Blob 文件流处理失败: ${error.message}`)
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "254b5570-805b-4fd0-8a5f-0b4ce6872759"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-integration-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加性能优化工具函数，支持懒加载和虚拟滚动。"
// }}

/**
 * PDF渲染性能优化工具函数
 */

// 页面缓存管理
interface PageCache {
  pageNumber: number
  canvas: HTMLCanvasElement
  timestamp: number
  scale: number
}

class PdfPageCache {
  private cache = new Map<string, PageCache>()
  private maxSize: number

  constructor(maxSize: number = 50) {
    this.maxSize = maxSize
  }

  // 生成缓存键
  private getCacheKey(pageNumber: number, scale: number): string {
    return `${pageNumber}-${Math.round(scale * 100)}`
  }

  // 获取缓存页面
  get(pageNumber: number, scale: number): HTMLCanvasElement | null {
    const key = this.getCacheKey(pageNumber, scale)
    const cached = this.cache.get(key)

    if (cached) {
      // 更新访问时间
      cached.timestamp = Date.now()
      return cached.canvas.cloneNode(true) as HTMLCanvasElement
    }

    return null
  }

  // 设置缓存页面
  set(pageNumber: number, scale: number, canvas: HTMLCanvasElement): void {
    const key = this.getCacheKey(pageNumber, scale)

    // 如果缓存已满，删除最旧的项
    if (this.cache.size >= this.maxSize) {
      this.evictOldest()
    }

    // 克隆canvas避免引用问题
    const clonedCanvas = canvas.cloneNode(true) as HTMLCanvasElement
    const ctx = clonedCanvas.getContext('2d')
    if (ctx) {
      ctx.drawImage(canvas, 0, 0)
    }

    this.cache.set(key, {
      pageNumber,
      canvas: clonedCanvas,
      timestamp: Date.now(),
      scale
    })
  }

  // 清除缓存
  clear(): void {
    this.cache.clear()
  }

  // 删除最旧的缓存项
  private evictOldest(): void {
    let oldestKey = ''
    let oldestTime = Date.now()

    for (const [key, cached] of this.cache) {
      if (cached.timestamp < oldestTime) {
        oldestTime = cached.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  // 获取缓存统计
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.cache.size > 0 ? 1 : 0 // 简化的命中率计算
    }
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Enhanced"
//   Task_ID: "02a7b0ba-4810-4bfb-9184-ef3e3aa57966"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-integration-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "扩展页面缓存支持文本层缓存。"
// }}

// 增强的页面缓存，支持文本层
interface EnhancedPageCache extends PageCache {
  textLayerData?: any
}

class EnhancedPdfPageCache extends PdfPageCache {
  private textLayerCache = new Map<string, any>()

  // 设置文本层缓存
  setTextLayer(pageNumber: number, scale: number, textLayerData: any): void {
    const key = this.getCacheKey(pageNumber, scale)
    this.textLayerCache.set(key, textLayerData)
  }

  // 获取文本层缓存
  getTextLayer(pageNumber: number, scale: number): any | null {
    const key = this.getCacheKey(pageNumber, scale)
    return this.textLayerCache.get(key) || null
  }

  // 清除所有缓存
  clear(): void {
    super.clear()
    this.textLayerCache.clear()
  }

  // 获取增强的缓存统计
  getEnhancedStats() {
    return {
      ...this.getStats(),
      textLayerCacheSize: this.textLayerCache.size
    }
  }
}

// 创建全局增强页面缓存实例
export const pdfPageCache = new EnhancedPdfPageCache(50)

/**
 * 计算可视区域的页面范围
 */
export function calculateVisibleRange(
  container: HTMLElement,
  pageHeight: number,
  totalPages: number,
  buffer: number = 2
): { start: number; end: number } {
  const scrollTop = container.scrollTop
  const containerHeight = container.clientHeight

  // 计算可视区域的起始和结束页面
  const startPage = Math.max(1, Math.floor(scrollTop / pageHeight) + 1 - buffer)
  const endPage = Math.min(totalPages, Math.ceil((scrollTop + containerHeight) / pageHeight) + buffer)

  return { start: startPage, end: endPage }
}

/**
 * 节流函数，用于优化滚动性能
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null
  let lastExecTime = 0

  return (...args: Parameters<T>) => {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      func(...args)
      lastExecTime = currentTime
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = setTimeout(() => {
        func(...args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

/**
 * 防抖函数，用于优化渲染性能
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * 内存使用监控
 */
export function getMemoryUsage(): { used: number; total: number } | null {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    return {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024) // MB
    }
  }
  return null
}
