// HTTP SSE 接口请求
// 请求地址：****
// 请求方式：POST

// import { v4 as uuidv4 } from 'uuid'
import { MESSAGE_TYPE } from '@/constants'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { arrayUnique } from '@/utils/util'
import { eventHub } from './EventHub'
import { getAccessToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'
import moment from 'moment'
import { getChatUrl } from '@/utils/url'
const baseUrl = getChatUrl()

// 将时区格式的日期转换成时间戳
const parseToTimestamp = dateString => {
  // 使用 moment 解析 ISO 8601 格式的日期字符串（包含时区信息）
  // 例如: '2025-08-11T14:49:40.281199+08:00' -> 1723359580281
  return moment(dateString).valueOf() // valueOf() 返回时间戳（毫秒）
}

// 示例用法：
// const timestamp = parseToTimestamp('2025-08-11T14:49:40.281199+08:00')
// console.log(timestamp) // 输出: 1723359580281

// 格式化时间戳为可读格式
const formatTime = timestamp => {
  // 如果传入的是 ISO 8601 格式的字符串，先转换为时间戳
  if (typeof timestamp === 'string' && timestamp.includes('T')) {
    timestamp = parseToTimestamp(timestamp)
  }
  return moment(timestamp).format('YYYY-MM-DD HH:mm')
}

// 比较两个日期（支持时间戳和 ISO 8601 格式）
const compareDates = (date1, date2) => {
  const timestamp1 = typeof date1 === 'string' && date1.includes('T') ? parseToTimestamp(date1) : Number(date1)
  const timestamp2 = typeof date2 === 'string' && date2.includes('T') ? parseToTimestamp(date2) : Number(date2)

  return timestamp1 - timestamp2
}
interface Cache {
  chat_id: string
  configInfo: BotInfo | null
  chatsContent: ChatMessage[]
  systemEvents: any[]
  transferInfo: {
    transferStatus: boolean
    transferAvatar: string
  }
}

interface BotInfo {
  name: string
  avatar: string
  is_available: boolean
  bot_biz_id: string
  chat_id?: string
  code?: number
  data?: any
}

interface ChatMessage {
  answer_text?: string
  answer_text_list?: string[]
  answer_tokens?: number
  chat_id: string
  const?: number
  create_time?: string
  dataset_list?: any[]
  execution_details?: {
    name: string
    type: string
    id: string
  }[]
  improve_paragraph_id_list?: any[]
  index?: number
  message_tokens?: number
  padding_problem_text?: string | null
  paragraph_list?: any[]
  problem_text?: string
  run_time?: number
  update_time?: string
  is_end?: boolean
  is_loading?: boolean
}

interface SseParams {
  chat_id?: string
  chat_record_id?: string
  audio_list: any[]
  document_list: any[]
  form_data: {}
  image_list: any[]
  message: string
  other_list: any[]
  re_chat: boolean
  video_list: any[]
}

interface ErrorResponse {
  error?: {
    message?: string
  }
}

const $e: any = eventHub
const origin: string = baseUrl //import.meta.env.VITE_APP_AI_BASE_API
const path: string = '/chat_message'
const sseUrl: string = origin + path

let cache: Cache | null = null
let timeoutTasks: { [key: string]: NodeJS.Timeout } = {}
const msgSendTimeout: number = 2 * 60 * 1000
let sseObj: any = null

/**
 * 适配新的ChatMessage格式到旧格式，保持向后兼容
 */
function adaptChatMessage(newMsg: ChatMessage): ChatMessage {
  return {
    ...newMsg,
    answer_text: newMsg.answer_text || newMsg.content || '',
    chat_id: newMsg.chat_id,
    is_loading: newMsg.is_loading ?? false
  }
}

/**
 * 智能去重函数，支持新旧字段名
 */
function uniqueChatMessages(messages: ChatMessage[]): ChatMessage[] {
  const seen = new Set<string>()
  const result: ChatMessage[] = []

  for (const msg of messages) {
    // 优先使用 id
    const id = msg.id

    if (!id) {
      // 如果没有ID，生成一个基于内容和时间的唯一标识
      const contentId = `${msg.problem_text || ''}_${msg.answer_text || ''}_${msg.update_time || Date.now()}`
      if (!seen.has(contentId)) {
        seen.add(contentId)
        result.push(msg)
      }
    } else {
      if (!seen.has(id)) {
        seen.add(id)
        result.push(msg)
      }
    }
  }

  console.log('去重前消息数量:', messages.length, '去重后消息数量:', result.length)
  return result
}

class SseCls {
  constructor() {
    cache = {
      chat_id: 'chat_id',
      configInfo: null,
      chatsContent: [],
      systemEvents: [],
      transferInfo: {
        transferStatus: false,
        transferAvatar: ''
      }
    }
  }

  // 暴露cache作为实例属性
  get cache(): Cache {
    return this.ensureCache()
  }

  // 确保cache已初始化的方法
  private ensureCache(): Cache {
    if (!cache) {
      cache = {
        chat_id: 'chat_id',
        configInfo: null,
        chatsContent: [],
        systemEvents: [],
        transferInfo: {
          transferStatus: false,
          transferAvatar: ''
        }
      }
    }
    return cache
  }

  init(): void {
    this.sseQueryConfigInfo()
  }

  async sseQueryConfigInfo(): Promise<void> {
    try {
      const sessionId: string = localStorage.getItem('appSessionId')
      if (cache) {
        cache.chat_id = sessionId

        const botInfo: BotInfo = {
          code: 0,
          data: {
            name: '测试机器人',
            avatar: 'https://qbot-1251316161.cos.ap-nanjing.myqcloud.com/avatar.png',
            is_available: true,
            bot_biz_id: '1664519736704069632'
          }
        }
        cache.configInfo = botInfo.data
        cache.configInfo.chat_id = sessionId
        console.log('【sse】init config info:', cache.configInfo)
        $e.emit('client_configChange', cache.configInfo)
      }
    } catch (e) {
      ElMessage.error('获取会话信息失败，请刷新页面重试！')
      console.error('SSE config init error:', e)
    }
  }

  getConfigInfo(): BotInfo | null {
    return cache?.configInfo || null
  }

  getChatsContent(): ChatMessage[] {
    return cache?.chatsContent || []
  }

  getMsgById(msgId: string): ChatMessage | undefined {
    return cache?.chatsContent.find(r => r.id === msgId || r.chat_id === msgId)
  }

  getQmsgById(msgId: string): ChatMessage | undefined {
    const findedMsg = this.getMsgById(msgId)
    return findedMsg ? cache?.chatsContent.find(r => r.id === findedMsg.id) : undefined
  }

  modifyMsgContent(msgId: string): void {
    const findedMsg = this.getMsgById(msgId)

    if (findedMsg) {
      findedMsg.is_end = true
      findedMsg.content = findedMsg.answer_text.concat(`<span class="stop-ws">| 已停止生成</span>`)

      $e.emit('client_msgContentChange', {
        chatsContent: cache?.chatsContent,
        type: MESSAGE_TYPE.STOP
      })
    }
  }

  async getQASse(data: string, requestId: string): Promise<void> {
    console.log('【sse】getQASse', data, requestId)
    const params: SseParams = {
      audio_list: [],
      document_list: [],
      form_data: {},
      image_list: [],
      message: data,
      other_list: [],
      re_chat: false,
      video_list: [],
      is_end: false
    }

    try {
      sseObj = await fetchEventSource(`${sseUrl}/${requestId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: '*/*',
          Authorization: getAccessToken()
        },
        body: JSON.stringify(params),
        openWhenHidden: true,
        onopen: (response: Response): void => {
          console.log('【sse】Connection opened:', response)
        },
        onmessage: (rsp: MessageEvent): void => {
          console.log('【sse】Message from server:', rsp)

          const event = JSON.parse(rsp.data)

          if (event) {
            const rawData: ChatMessage = event
            const data = adaptChatMessage(rawData)
            console.log(data.chat_id)
            console.log(cache.chat_id)

            if (!cache || data.chat_id !== cache.chat_id) return

            const findedMsg = this.getMsgById(data.chat_id!)
            console.log('【sse】findedMsg:', findedMsg)

            if (findedMsg && findedMsg.is_end) return

            this.assembleMsgContent(data, MESSAGE_TYPE.ANSWER)
          }

          if (event.type === 'error') {
            const rep: ErrorResponse = event.payload || event
            if (rep?.error) {
              ElMessage.error(rep.error.message || '服务出错了！')
            } else {
              ElMessage.error('服务出错了！')
            }
          }
        },
        onerror: (error: Error): void => {
          console.error('【sse】Error:', error)
        }
      })
    } catch (error) {
      console.error('【sse】Error:', error)
      ElMessage.error('连接服务器失败，请重试！')
    }
  }

  async triggerSendMsg(msg: string): Promise<void> {
    const requestId: string = localStorage.getItem('appSessionId')
    cache.chat_id = requestId
    const msgContent: ChatMessage = {
      // 新格式字段
      chat_id: requestId || '',
      problem_text: msg,
      update_time: new Date().toISOString(),
      is_loading: false
    }

    this.assembleMsgContent(msgContent, MESSAGE_TYPE.QUESTION)

    timeoutTasks[msgContent.chat_id!] = setTimeout(() => {
      this.assembleMsgContent(
        {
          ...msgContent,
          failed: true
        },
        MESSAGE_TYPE.ANSWER
      )
    }, msgSendTimeout)

    await this.getQASse(msg, requestId)
  }

  assembleMsgContent(msgList: ChatMessage | ChatMessage[], type: string): void {
    const currentCache = this.ensureCache()

    if (type === MESSAGE_TYPE.QUESTION) {
      currentCache.chatsContent.push(msgList as ChatMessage)
    } else if (type === MESSAGE_TYPE.ANSWER) {
      this.processAnswerMessage(msgList as ChatMessage)
    } else if (type === MESSAGE_TYPE.HISTORY) {
      this.processHistoryMessages(msgList)
    }

    currentCache.chatsContent = uniqueChatMessages(currentCache.chatsContent)

    $e.emit('client_msgContentChange', {
      chatsContent: currentCache.chatsContent,
      type
    })
  }

  private processAnswerMessage(newMsg: ChatMessage): void {
    if (!cache) return
    cache.chatsContent = cache.chatsContent.map(r => ({
      ...r,
      isTyped: false
    }))

    if (cache.chatsContent.length < 1) {
      cache.chatsContent.push(newMsg)
      return
    }

    let currentList = cache.chatsContent

    if (newMsg.chat_id) {
      const timeout = timeoutTasks[newMsg.chat_id]
      timeout && clearTimeout(timeout)
    }

    // if (currentList.length === 2 && newMsg.can_rating) {
    //   currentList[0].transferRobot = true
    // }

    if (newMsg.chat_id && newMsg.chat_record_id) {
      // currentList.pop()
      // currentList[currentList.length - 1].loading_message = false
      const answer = currentList[currentList.length - 1].answer_text ? currentList[currentList.length - 1].answer_text : ''

      currentList[currentList.length - 1] = {
        ...newMsg,
        ...currentList[currentList.length - 1],
        answer_text: newMsg.answer_text ? answer.concat('', newMsg.answer_text) : answer,
        loading_message: answer ? false : true
      }
    } else {
      this.insertAnswerMessage(currentList, newMsg)
    }
  }

  private insertAnswerMessage(currentList: ChatMessage[], newMsg: ChatMessage): void {
    for (let i = currentList.length - 1; i >= 0; i--) {
      const { transfer, quit, transferRobot } = currentList[i]
      const tmp = {
        ...newMsg,
        transfer,
        quit,
        transferRobot
      }

      const newMsgId = newMsg.id || newMsg.chat_id
      const currentMsgId = currentList[i].id || currentList[i].chat_id
      if (newMsgId && newMsgId === currentMsgId) {
        currentList[i] = tmp
        break
      }

      if (newMsg.chat_id && newMsg.chat_id === currentList[i].chat_id) {
        newMsg.is_loading = false
        currentList[i] = tmp

        if (!newMsg.is_evil && cache && !cache.transferInfo.transferStatus) {
          currentList.push({
            // 新格式字段
            answer_text: '',
            answer_text_list: [],
            answer_tokens: 0,
            chat_id: '',
            const: 0,
            create_time: new Date().toISOString(),
            dataset_list: [],
            execution_details: [],
            improve_paragraph_id_list: [],
            index: 0,
            message_tokens: 0,
            padding_problem_text: null,
            paragraph_list: [],
            problem_text: '',
            run_time: 0,
            update_time: new Date().toISOString(),
            loading_message: true,
            content: '',
            fromAvatar: cache.configInfo?.avatar
          })
        }
        break
      }

      if (compareDates(newMsg.update_time, currentList[i].update_time) >= 0) {
        if (currentList[i].loading_message) {
          currentList[currentList.length - 1] = newMsg
        } else {
          currentList.splice(i + 1, 0, newMsg)
        }
        break
      }

      if (i === 0 && compareDates(newMsg.update_time, currentList[i].update_time) < 0) {
        currentList.splice(0, 0, newMsg)
      }
    }
  }

  private processHistoryMessages(msgList: ChatMessage | ChatMessage[]): void {
    if (!cache) return

    // 如果传入空数组，清空聊天内容
    if (Array.isArray(msgList) && msgList.length === 0) {
      cache.chatsContent = []
      // 触发更新事件
      $e.emit('client_msgContentChange', {
        chatsContent: cache.chatsContent,
        type: MESSAGE_TYPE.HISTORY
      })
      return
    }

    const messages = Array.isArray(msgList) ? msgList : [msgList]
    const historyMessages = messages.map(r => ({
      ...r,
      is_history: true,

      is_loading: false
    }))

    console.log('处理历史消息:', historyMessages)

    // 处理历史消息
    if (cache.chatsContent.length === 0) {
      cache.chatsContent = [...historyMessages]
    } else {
      cache.chatsContent = cache.chatsContent.map(r => ({
        ...r,
        isTyped: false
      }))
      const oldMsgCurrent = cache.chatsContent[0]
      const newMsgHistory = historyMessages[historyMessages.length - 1]
      if (compareDates(newMsgHistory.update_time, oldMsgCurrent.update_time) < 0) {
        cache.chatsContent = [...historyMessages, ...cache.chatsContent]
      } else {
        historyMessages.reverse().forEach(msg => {
          this.insertHistoryMessage(msg)
        })
      }
    }

    // 去重处理
    cache.chatsContent = uniqueChatMessages(cache.chatsContent)

    console.log('最终的聊天内容:', cache.chatsContent)

    // 触发更新事件，通知前端组件
    $e.emit('client_msgContentChange', {
      chatsContent: cache.chatsContent,
      type: MESSAGE_TYPE.HISTORY
    })
  }

  private insertHistoryMessage(msg: ChatMessage): void {
    if (!cache) return

    // 如果没有现有消息，直接添加
    if (cache.chatsContent.length === 0) {
      cache.chatsContent.push(msg)
      return
    }

    let inserted = false
    for (let i = 0; i < cache.chatsContent.length; i++) {
      const currentMsg = cache.chatsContent[i]
      const msgId = msg.id || msg.chat_id
      const currentId = currentMsg.id || currentMsg.chat_id

      // 如果找到相同ID的消息，替换它
      if (msgId && msgId === currentId) {
        cache.chatsContent[i] = msg
        inserted = true
        break
      }
      // 如果新消息的时间早于或等于当前消息，插入到当前位置
      else if (compareDates(msg.update_time, currentMsg.update_time) <= 0) {
        cache.chatsContent.splice(i, 0, msg)
        inserted = true
        break
      }
    }

    // 如果没有插入，说明新消息时间最晚，添加到末尾
    if (!inserted) {
      cache.chatsContent.push(msg)
    }

    console.log('插入历史消息完成:', msg.id || msg.chat_id)
  }

  stopGeneration(): void {
    const ongoingMsg = cache?.chatsContent.find(r => r.is_end === false)

    if (!ongoingMsg) return

    if (sseObj) {
      sseObj.close()
    }
    const msgId = ongoingMsg.chat_id
    if (msgId) {
      this.modifyMsgContent(msgId)
    }
  }
}

const sseCls = new SseCls()

export default {
  install: (app: any): void => {
    app.provide('SseCls', sseCls)
    app.config.globalProperties.$SseCls = sseCls
  }
}

// 导出主要的 SSE 类和工具函数
export { sseCls, parseToTimestamp, formatTime, compareDates }
