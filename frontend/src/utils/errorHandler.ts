/**
 * Error Handler Utility - 错误处理工具
 * 
 * {{RIPER-5+SMART-6:
 *   Action: "Parallel-Added"
 *   Task_ID: "ccf7fda6-a932-4ce0-9363-6a4a0aa76f9e"
 *   Timestamp: "2025-08-07T14:30:00+08:00"
 *   Authoring_Subagent: "ui-ux-specialist"
 *   Principle_Applied: "SOLID-S (单一职责原则)"
 *   Quality_Check: "错误处理工具实现完整，用户体验友好。"
 * }}
 */

import { ElMessage, ElNotification } from 'element-plus'

export interface ErrorInfo {
  code: string
  message: string
  details?: any
  timestamp: Date
  source: string
}

export interface ErrorHandlerOptions {
  showMessage?: boolean
  showNotification?: boolean
  logToConsole?: boolean
  reportToServer?: boolean
  customHandler?: (error: ErrorInfo) => void
}

/**
 * 全局错误处理器
 */
export class GlobalErrorHandler {
  private static instance: GlobalErrorHandler
  private errorLog: ErrorInfo[] = []
  private maxLogSize = 100

  static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler()
    }
    return GlobalErrorHandler.instance
  }

  /**
   * 处理错误
   */
  handleError(
    error: Error | string | any,
    source: string = 'unknown',
    options: ErrorHandlerOptions = {}
  ): ErrorInfo {
    const errorInfo: ErrorInfo = {
      code: this.getErrorCode(error),
      message: this.getErrorMessage(error),
      details: error,
      timestamp: new Date(),
      source
    }

    // 记录错误日志
    this.logError(errorInfo)

    // 默认选项
    const defaultOptions: ErrorHandlerOptions = {
      showMessage: true,
      showNotification: false,
      logToConsole: true,
      reportToServer: false
    }

    const finalOptions = { ...defaultOptions, ...options }

    // 控制台日志
    if (finalOptions.logToConsole) {
      console.error(`[${source}] ${errorInfo.message}`, errorInfo.details)
    }

    // 显示消息提示
    if (finalOptions.showMessage) {
      this.showErrorMessage(errorInfo)
    }

    // 显示通知
    if (finalOptions.showNotification) {
      this.showErrorNotification(errorInfo)
    }

    // 上报服务器
    if (finalOptions.reportToServer) {
      this.reportToServer(errorInfo)
    }

    // 自定义处理器
    if (finalOptions.customHandler) {
      finalOptions.customHandler(errorInfo)
    }

    return errorInfo
  }

  /**
   * 获取错误代码
   */
  private getErrorCode(error: any): string {
    if (error?.code) return error.code
    if (error?.status) return `HTTP_${error.status}`
    if (error?.name) return error.name
    return 'UNKNOWN_ERROR'
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(error: any): string {
    if (typeof error === 'string') return error
    if (error?.message) return error.message
    if (error?.statusText) return error.statusText
    return '发生未知错误'
  }

  /**
   * 记录错误日志
   */
  private logError(errorInfo: ErrorInfo) {
    this.errorLog.unshift(errorInfo)
    
    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize)
    }
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(errorInfo: ErrorInfo) {
    const message = this.getFriendlyMessage(errorInfo)
    ElMessage.error({
      message,
      duration: 5000,
      showClose: true
    })
  }

  /**
   * 显示错误通知
   */
  private showErrorNotification(errorInfo: ErrorInfo) {
    const message = this.getFriendlyMessage(errorInfo)
    ElNotification.error({
      title: '操作失败',
      message,
      duration: 8000,
      position: 'top-right'
    })
  }

  /**
   * 获取用户友好的错误消息
   */
  private getFriendlyMessage(errorInfo: ErrorInfo): string {
    const messageMap: Record<string, string> = {
      'NETWORK_ERROR': '网络连接失败，请检查网络设置',
      'HTTP_404': '请求的资源不存在',
      'HTTP_500': '服务器内部错误，请稍后重试',
      'HTTP_403': '没有权限执行此操作',
      'HTTP_401': '身份验证失败，请重新登录',
      'TIMEOUT_ERROR': '请求超时，请稍后重试',
      'PARSE_ERROR': '数据解析失败',
      'VALIDATION_ERROR': '输入数据验证失败',
      'FILE_TOO_LARGE': '文件大小超出限制',
      'UNSUPPORTED_FORMAT': '不支持的文件格式'
    }

    return messageMap[errorInfo.code] || errorInfo.message
  }

  /**
   * 上报错误到服务器
   */
  private async reportToServer(errorInfo: ErrorInfo) {
    try {
      // 这里可以实现错误上报逻辑
      console.log('Reporting error to server:', errorInfo)
    } catch (error) {
      console.error('Failed to report error:', error)
    }
  }

  /**
   * 获取错误日志
   */
  getErrorLog(): ErrorInfo[] {
    return [...this.errorLog]
  }

  /**
   * 清除错误日志
   */
  clearErrorLog() {
    this.errorLog = []
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats: Record<string, number> = {}
    this.errorLog.forEach(error => {
      stats[error.code] = (stats[error.code] || 0) + 1
    })
    return stats
  }
}

/**
 * 便捷的错误处理函数
 */
export const handleError = (
  error: Error | string | any,
  source?: string,
  options?: ErrorHandlerOptions
): ErrorInfo => {
  return GlobalErrorHandler.getInstance().handleError(error, source, options)
}

/**
 * 网络错误处理
 */
export const handleNetworkError = (error: any, source: string = 'network') => {
  return handleError(error, source, {
    showNotification: true,
    reportToServer: true
  })
}

/**
 * 文件处理错误
 */
export const handleFileError = (error: any, source: string = 'file') => {
  return handleError(error, source, {
    showMessage: true,
    logToConsole: true
  })
}

/**
 * API错误处理
 */
export const handleApiError = (error: any, source: string = 'api') => {
  return handleError(error, source, {
    showMessage: true,
    reportToServer: true
  })
}

/**
 * 用户操作错误处理
 */
export const handleUserError = (error: any, source: string = 'user') => {
  return handleError(error, source, {
    showMessage: true,
    logToConsole: false
  })
}
