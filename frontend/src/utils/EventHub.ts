import mitt from 'mitt'
import type { App } from 'vue'

// 定义事件处理函数类型
type EventHandler<T = any> = (event: T) => void

// 定义事件类型
interface Events {
  client_configChange: any
  client_msgContentChange: {
    chatsContent: any[]
    type: string
  }
  [key: string]: any
}

// 定义 Emitter 接口
interface Emitter<T> {
  all: Map<string, EventHandler[]>
  on(type: string, handler: EventHandler): void
  off(type: string, handler: EventHandler): void
  emit(type: string, evt?: T): void
}

// 创建事件总线实例
const emitter: Emitter<Events> = mitt<Events>()

// 创建 EventHub 类封装 mitt
class EventHub {
  private emitter: Emitter<Events>

  constructor() {
    this.emitter = emitter
  }

  /**
   * 发送事件
   * @param type 事件类型
   * @param args 事件参数
   */
  emit(type: string, ...args: any[]): void {
    this.emitter.emit(type, args[0])
  }

  /**
   * 监听事件
   * @param type 事件类型
   * @param handler 事件处理函数
   */
  on(type: string, handler: EventHandler): void {
    this.emitter.on(type, handler)
  }

  /**
   * 移除事件监听
   * @param type 事件类型
   * @param handler 事件处理函数
   */
  off(type: string, handler: EventHandler): void {
    this.emitter.off(type, handler)
  }
}

// 导出单例实例
export const eventHub = new EventHub()

// Vue 插件类型定义
interface EventHubPlugin {
  install: (app: App) => void
}

// Vue 插件
const plugin: EventHubPlugin = {
  install: (app: App): void => {
    app.config.globalProperties.$eventHub = eventHub
    app.provide('eventHub', eventHub)
  }
}

export default plugin
