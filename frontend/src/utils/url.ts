export const getUrl = () => {
  const environment = import.meta.env.VITE_APP_ENV
  const ip = window.location.origin
  const baseUrl = environment == 'development' ? import.meta.env.VITE_APP_BASE_API : ip + '/api'
  return baseUrl
}
export const getChatUrl = () => {
  const environment = import.meta.env.VITE_APP_ENV
  const ip = window.location.origin
  const baseUrl = environment == 'development' ? import.meta.env.VITE_APP_AI_BASE_API : ip + '/qa-api/api/application'
  return baseUrl
}
