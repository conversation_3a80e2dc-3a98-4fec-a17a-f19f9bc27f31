

import type { PositionMapping } from './positionMapper'

export interface HighlightStyle {
  backgroundColor: string
  borderColor?: string
  borderWidth?: string
  borderStyle?: string
  opacity?: number
  zIndex?: number
  animation?: string
}

export interface HighlightMarker {
  id: string
  type: 'extract' | 'detect'
  position: PositionMapping
  style: HighlightStyle
  data: any
  isActive: boolean
  createdAt: Date
}

export interface HighlightOptions {
  duration?: number
  animation?: boolean
  persistent?: boolean
  zIndex?: number
}

/**
 * 高亮管理器
 */
export class HighlightManager {
  private container: HTMLElement | null = null
  private highlightLayer: HTMLElement | null = null
  private activeHighlights: Map<string, HighlightMarker> = new Map()
  private styleSheet: CSSStyleSheet | null = null

  constructor(container?: HTMLElement) {
    if (container) {
      this.setContainer(container)
    }
    this.initializeStyles()
  }

  /**
   * 设置容器
   */
  setContainer(container: HTMLElement) {
    this.container = container
    this.createHighlightLayer()
  }

  /**
   * 创建高亮层
   */
  private createHighlightLayer() {
    if (!this.container) return

    // 移除已存在的高亮层
    const existingLayer = this.container.querySelector('.highlight-layer')
    if (existingLayer) {
      existingLayer.remove()
    }

    // 创建新的高亮层
    this.highlightLayer = document.createElement('div')
    this.highlightLayer.className = 'highlight-layer'
    this.highlightLayer.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 10;
    `

    this.container.style.position = 'relative'
    this.container.appendChild(this.highlightLayer)
  }

  /**
   * 初始化样式
   */
  private initializeStyles() {
    const styleId = 'highlight-styles'
    let styleElement = document.getElementById(styleId) as HTMLStyleElement
    
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = styleId
      document.head.appendChild(styleElement)
    }

    const css = `
      .highlight-marker {
        position: absolute;
        pointer-events: auto;
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 4px;
        box-sizing: border-box;
      }

      .highlight-marker.extract {
        background-color: rgba(82, 196, 26, 0.2);
        border: 2px solid #52c41a;
      }

      .highlight-marker.detect {
        background-color: rgba(255, 77, 79, 0.2);
        border: 2px solid #ff4d4f;
      }

      .highlight-marker.active {
        box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
        border-color: #1890ff;
        z-index: 20;
      }

      .highlight-marker:hover {
        opacity: 0.8;
        transform: scale(1.02);
      }

      @keyframes highlight-pulse {
        0% { opacity: 0.3; transform: scale(0.95); }
        50% { opacity: 0.7; transform: scale(1.05); }
        100% { opacity: 0.5; transform: scale(1); }
      }

      .highlight-marker.pulse {
        animation: highlight-pulse 1s ease-in-out;
      }

      @keyframes highlight-fade-in {
        from { opacity: 0; transform: scale(0.8); }
        to { opacity: 0.5; transform: scale(1); }
      }

      .highlight-marker.fade-in {
        animation: highlight-fade-in 0.5s ease-out;
      }

      .highlight-tooltip {
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 30;
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .highlight-tooltip.visible {
        opacity: 1;
      }
    `

    styleElement.textContent = css
  }

  /**
   * 添加高亮标记
   */
  addHighlight(
    id: string,
    type: 'extract' | 'detect',
    position: PositionMapping,
    data: any,
    options: HighlightOptions = {}
  ): HighlightMarker {
    if (!this.highlightLayer) {
      throw new Error('Highlight layer not initialized')
    }

    // 移除已存在的同ID高亮
    this.removeHighlight(id)

    const style = this.getHighlightStyle(type, options)
    const marker: HighlightMarker = {
      id,
      type,
      position,
      style,
      data,
      isActive: false,
      createdAt: new Date()
    }

    const element = this.createHighlightElement(marker, options)
    this.highlightLayer.appendChild(element)
    this.activeHighlights.set(id, marker)

    // 自动移除（如果设置了持续时间）
    if (options.duration && options.duration > 0) {
      setTimeout(() => {
        this.removeHighlight(id)
      }, options.duration)
    }

    return marker
  }

  /**
   * 创建高亮元素
   */
  private createHighlightElement(marker: HighlightMarker, options: HighlightOptions): HTMLElement {
    const element = document.createElement('div')
    element.className = `highlight-marker ${marker.type}`
    element.dataset.highlightId = marker.id
    
    const pos = marker.position.highlightPosition
    element.style.cssText = `
      left: ${pos.x}px;
      top: ${pos.y}px;
      width: ${pos.width}px;
      height: ${pos.height}px;
      background-color: ${marker.style.backgroundColor};
      border-color: ${marker.style.borderColor || ''};
      border-width: ${marker.style.borderWidth || '2px'};
      border-style: ${marker.style.borderStyle || 'solid'};
      opacity: ${marker.style.opacity || 0.5};
      z-index: ${marker.style.zIndex || options.zIndex || 10};
    `

    // 添加动画
    if (options.animation !== false) {
      element.classList.add('fade-in')
    }

    // 添加事件监听
    element.addEventListener('click', (e) => {
      e.stopPropagation()
      this.handleHighlightClick(marker)
    })

    element.addEventListener('mouseenter', (e) => {
      this.showTooltip(e, marker)
    })

    element.addEventListener('mouseleave', () => {
      this.hideTooltip()
    })

    return element
  }

  /**
   * 获取高亮样式
   */
  private getHighlightStyle(type: 'extract' | 'detect', options: HighlightOptions): HighlightStyle {
    const baseStyles = {
      extract: {
        backgroundColor: 'rgba(82, 196, 26, 0.2)',
        borderColor: '#52c41a'
      },
      detect: {
        backgroundColor: 'rgba(255, 77, 79, 0.2)',
        borderColor: '#ff4d4f'
      }
    }

    return {
      ...baseStyles[type],
      borderWidth: '2px',
      borderStyle: 'solid',
      opacity: 0.5,
      zIndex: options.zIndex || 10
    }
  }

  /**
   * 移除高亮标记
   */
  removeHighlight(id: string): boolean {
    const marker = this.activeHighlights.get(id)
    if (!marker) return false

    const element = this.highlightLayer?.querySelector(`[data-highlight-id="${id}"]`)
    if (element) {
      element.remove()
    }

    this.activeHighlights.delete(id)
    return true
  }

  /**
   * 清除所有高亮
   */
  clearAllHighlights() {
    if (this.highlightLayer) {
      this.highlightLayer.innerHTML = ''
    }
    this.activeHighlights.clear()
  }

  /**
   * 设置活跃高亮
   */
  setActiveHighlight(id: string) {
    // 清除之前的活跃状态
    this.highlightLayer?.querySelectorAll('.highlight-marker.active').forEach(el => {
      el.classList.remove('active')
    })

    // 设置新的活跃状态
    const element = this.highlightLayer?.querySelector(`[data-highlight-id="${id}"]`)
    if (element) {
      element.classList.add('active')
      
      // 添加脉冲动画
      element.classList.add('pulse')
      setTimeout(() => {
        element.classList.remove('pulse')
      }, 1000)
    }

    // 更新标记状态
    this.activeHighlights.forEach((marker, markerId) => {
      marker.isActive = markerId === id
    })
  }

  /**
   * 获取高亮标记
   */
  getHighlight(id: string): HighlightMarker | undefined {
    return this.activeHighlights.get(id)
  }

  /**
   * 获取所有高亮标记
   */
  getAllHighlights(): HighlightMarker[] {
    return Array.from(this.activeHighlights.values())
  }

  /**
   * 按类型获取高亮标记
   */
  getHighlightsByType(type: 'extract' | 'detect'): HighlightMarker[] {
    return this.getAllHighlights().filter(marker => marker.type === type)
  }

  /**
   * 处理高亮点击
   */
  private handleHighlightClick(marker: HighlightMarker) {
    this.setActiveHighlight(marker.id)
    
    // 触发自定义事件
    const event = new CustomEvent('highlight-click', {
      detail: { marker }
    })
    this.container?.dispatchEvent(event)
  }

  /**
   * 显示工具提示
   */
  private showTooltip(event: MouseEvent, marker: HighlightMarker) {
    this.hideTooltip()

    const tooltip = document.createElement('div')
    tooltip.className = 'highlight-tooltip'
    tooltip.textContent = this.getTooltipText(marker)
    
    document.body.appendChild(tooltip)

    // 定位工具提示
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    tooltip.style.left = `${rect.left + rect.width / 2}px`
    tooltip.style.top = `${rect.top - tooltip.offsetHeight - 8}px`
    
    // 显示工具提示
    setTimeout(() => {
      tooltip.classList.add('visible')
    }, 100)

    // 存储引用以便清理
    this.currentTooltip = tooltip
  }

  private currentTooltip: HTMLElement | null = null

  /**
   * 隐藏工具提示
   */
  private hideTooltip() {
    if (this.currentTooltip) {
      this.currentTooltip.remove()
      this.currentTooltip = null
    }
  }

  /**
   * 获取工具提示文本
   */
  private getTooltipText(marker: HighlightMarker): string {
    const typeText = marker.type === 'extract' ? '提取结果' : '检测问题'
    const confidence = marker.position.confidence
    return `${typeText} (置信度: ${Math.round(confidence * 100)}%)`
  }

  /**
   * 更新高亮位置
   */
  updateHighlightPositions() {
    this.activeHighlights.forEach((marker, id) => {
      const element = this.highlightLayer?.querySelector(`[data-highlight-id="${id}"]`) as HTMLElement
      if (element) {
        const pos = marker.position.highlightPosition
        element.style.left = `${pos.x}px`
        element.style.top = `${pos.y}px`
        element.style.width = `${pos.width}px`
        element.style.height = `${pos.height}px`
      }
    })
  }

  /**
   * 销毁高亮管理器
   */
  destroy() {
    this.clearAllHighlights()
    this.hideTooltip()
    
    if (this.highlightLayer) {
      this.highlightLayer.remove()
      this.highlightLayer = null
    }
  }
}

/**
 * 创建高亮管理器实例
 */
export function createHighlightManager(container?: HTMLElement): HighlightManager {
  return new HighlightManager(container)
}

/**
 * 预定义高亮样式
 */
export const HIGHLIGHT_STYLES = {
  extract: {
    backgroundColor: 'rgba(82, 196, 26, 0.2)',
    borderColor: '#52c41a'
  },
  detect: {
    backgroundColor: 'rgba(255, 77, 79, 0.2)',
    borderColor: '#ff4d4f'
  },
  warning: {
    backgroundColor: 'rgba(250, 173, 20, 0.2)',
    borderColor: '#faad14'
  },
  info: {
    backgroundColor: 'rgba(24, 144, 255, 0.2)',
    borderColor: '#1890ff'
  }
} as const
