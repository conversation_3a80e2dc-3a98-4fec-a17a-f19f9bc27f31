

export interface FilterOptions {
  searchText?: string
  agentType?: 'extract' | 'detect' | ''
  severity?: 'low' | 'medium' | 'high' | 'critical' | ''
  type?: string
  confidence?: { min: number; max: number }
  dateRange?: { start: Date; end: Date }
  page?: number
}

export interface ResultItem {
  id: string
  type: 'extract' | 'detect'
  title: string
  content: string
  description?: string
  severity?: string
  confidence?: number
  position?: {
    page: number
    paragraphIndex: number
    start: number
    end: number
    elementId: string
  }
  suggestion?: string
  createdTime: string
  attributes?: Record<string, any>
}

/**
 * 高效的结果过滤器
 */
export class ResultFilter {
  private items: ResultItem[] = []
  private filteredItems: ResultItem[] = []
  private searchIndex: Map<string, Set<number>> = new Map()

  constructor(items: ResultItem[] = []) {
    this.setItems(items)
  }

  /**
   * 设置数据项
   */
  setItems(items: ResultItem[]) {
    this.items = items
    this.buildSearchIndex()
    this.filteredItems = [...items]
  }

  /**
   * 构建搜索索引
   */
  private buildSearchIndex() {
    this.searchIndex.clear()
    
    this.items.forEach((item, index) => {
      const words = this.extractWords(item)
      words.forEach(word => {
        if (!this.searchIndex.has(word)) {
          this.searchIndex.set(word, new Set())
        }
        this.searchIndex.get(word)!.add(index)
      })
    })
  }

  /**
   * 提取搜索关键词
   */
  private extractWords(item: ResultItem): string[] {
    const text = [
      item.title,
      item.content,
      item.description || '',
      item.suggestion || ''
    ].join(' ').toLowerCase()

    // 分词：支持中文和英文
    const words: string[] = []
    
    // 英文单词
    const englishWords = text.match(/[a-z]+/g) || []
    words.push(...englishWords)
    
    // 中文字符（2-4个字符的组合）
    for (let i = 0; i < text.length; i++) {
      for (let len = 2; len <= 4 && i + len <= text.length; len++) {
        const substr = text.substring(i, i + len)
        if (/[\u4e00-\u9fa5]/.test(substr)) {
          words.push(substr)
        }
      }
    }
    
    return words
  }

  /**
   * 应用过滤器
   */
  filter(options: FilterOptions): ResultItem[] {
    let indices = new Set(this.items.map((_, index) => index))

    // 搜索文本过滤
    if (options.searchText?.trim()) {
      indices = this.filterBySearch(options.searchText.trim().toLowerCase(), indices)
    }

    // 智能体类型过滤
    if (options.agentType) {
      indices = this.filterByAgentType(options.agentType, indices)
    }

    // 严重程度过滤
    if (options.severity) {
      indices = this.filterBySeverity(options.severity, indices)
    }

    // 类型过滤
    if (options.type) {
      indices = this.filterByType(options.type, indices)
    }

    // 置信度过滤
    if (options.confidence) {
      indices = this.filterByConfidence(options.confidence, indices)
    }

    // 日期范围过滤
    if (options.dateRange) {
      indices = this.filterByDateRange(options.dateRange, indices)
    }

    // 转换为结果数组
    this.filteredItems = Array.from(indices).map(index => this.items[index])
    
    return this.filteredItems
  }

  /**
   * 搜索文本过滤
   */
  private filterBySearch(searchText: string, indices: Set<number>): Set<number> {
    const matchedIndices = new Set<number>()
    
    // 分解搜索词
    const searchWords = searchText.split(/\s+/).filter(word => word.length > 0)
    
    searchWords.forEach(word => {
      // 精确匹配
      if (this.searchIndex.has(word)) {
        this.searchIndex.get(word)!.forEach(index => {
          if (indices.has(index)) {
            matchedIndices.add(index)
          }
        })
      }
      
      // 模糊匹配
      this.searchIndex.forEach((itemIndices, indexWord) => {
        if (indexWord.includes(word)) {
          itemIndices.forEach(index => {
            if (indices.has(index)) {
              matchedIndices.add(index)
            }
          })
        }
      })
    })

    return matchedIndices
  }

  /**
   * 智能体类型过滤
   */
  private filterByAgentType(agentType: string, indices: Set<number>): Set<number> {
    const filtered = new Set<number>()
    indices.forEach(index => {
      if (this.items[index].type === agentType) {
        filtered.add(index)
      }
    })
    return filtered
  }

  /**
   * 严重程度过滤
   */
  private filterBySeverity(severity: string, indices: Set<number>): Set<number> {
    const filtered = new Set<number>()
    indices.forEach(index => {
      if (this.items[index].severity === severity) {
        filtered.add(index)
      }
    })
    return filtered
  }

  /**
   * 类型过滤
   */
  private filterByType(type: string, indices: Set<number>): Set<number> {
    const filtered = new Set<number>()
    indices.forEach(index => {
      const item = this.items[index]
      if (item.attributes?.type === type) {
        filtered.add(index)
      }
    })
    return filtered
  }

  /**
   * 置信度过滤
   */
  private filterByConfidence(range: { min: number; max: number }, indices: Set<number>): Set<number> {
    const filtered = new Set<number>()
    indices.forEach(index => {
      const confidence = this.items[index].confidence
      if (confidence !== undefined && confidence >= range.min && confidence <= range.max) {
        filtered.add(index)
      }
    })
    return filtered
  }

  /**
   * 日期范围过滤
   */
  private filterByDateRange(range: { start: Date; end: Date }, indices: Set<number>): Set<number> {
    const filtered = new Set<number>()
    indices.forEach(index => {
      const itemDate = new Date(this.items[index].createdTime)
      if (itemDate >= range.start && itemDate <= range.end) {
        filtered.add(index)
      }
    })
    return filtered
  }

  /**
   * 获取过滤后的结果
   */
  getFilteredResults(): ResultItem[] {
    return this.filteredItems
  }

  /**
   * 获取过滤统计信息
   */
  getFilterStats(): {
    total: number
    filtered: number
    extractCount: number
    detectCount: number
    severityStats: Record<string, number>
  } {
    const severityStats: Record<string, number> = {}
    let extractCount = 0
    let detectCount = 0

    this.filteredItems.forEach(item => {
      if (item.type === 'extract') {
        extractCount++
      } else if (item.type === 'detect') {
        detectCount++
      }

      if (item.severity) {
        severityStats[item.severity] = (severityStats[item.severity] || 0) + 1
      }
    })

    return {
      total: this.items.length,
      filtered: this.filteredItems.length,
      extractCount,
      detectCount,
      severityStats
    }
  }

  /**
   * 排序结果
   */
  sort(field: keyof ResultItem, order: 'asc' | 'desc' = 'asc'): ResultItem[] {
    this.filteredItems.sort((a, b) => {
      const aValue = a[field]
      const bValue = b[field]

      if (aValue === undefined && bValue === undefined) return 0
      if (aValue === undefined) return order === 'asc' ? 1 : -1
      if (bValue === undefined) return order === 'asc' ? -1 : 1

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return order === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return order === 'asc' ? aValue - bValue : bValue - aValue
      }

      return 0
    })

    return this.filteredItems
  }

  /**
   * 分页
   */
  paginate(page: number, pageSize: number): {
    items: ResultItem[]
    total: number
    hasMore: boolean
  } {
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const items = this.filteredItems.slice(start, end)

    return {
      items,
      total: this.filteredItems.length,
      hasMore: end < this.filteredItems.length
    }
  }
}

/**
 * 创建结果过滤器实例
 */
export function createResultFilter(items: ResultItem[] = []): ResultFilter {
  return new ResultFilter(items)
}

/**
 * 高亮搜索关键词
 */
export function highlightSearchText(text: string, searchText: string): string {
  if (!searchText.trim()) return text

  const keywords = searchText.trim().split(/\s+/)
  let highlightedText = text

  keywords.forEach(keyword => {
    const regex = new RegExp(`(${keyword})`, 'gi')
    highlightedText = highlightedText.replace(regex, '<mark>$1</mark>')
  })

  return highlightedText
}

/**
 * 计算搜索相关性得分
 */
export function calculateRelevanceScore(item: ResultItem, searchText: string): number {
  if (!searchText.trim()) return 0

  const keywords = searchText.toLowerCase().split(/\s+/)
  const text = [item.title, item.content, item.description || ''].join(' ').toLowerCase()
  
  let score = 0
  keywords.forEach(keyword => {
    const titleMatches = (item.title.toLowerCase().match(new RegExp(keyword, 'g')) || []).length
    const contentMatches = (item.content.toLowerCase().match(new RegExp(keyword, 'g')) || []).length
    
    // 标题匹配权重更高
    score += titleMatches * 3 + contentMatches
  })

  return score
}
