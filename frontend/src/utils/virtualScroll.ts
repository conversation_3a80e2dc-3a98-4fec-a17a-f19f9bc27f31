

export interface VirtualScrollOptions {
  container: HTMLElement
  itemHeight: number
  bufferSize?: number
  threshold?: number
}

export interface VirtualScrollItem {
  index: number
  top: number
  height: number
  element?: HTMLElement
}

export class VirtualScrollManager {
  private container: HTMLElement
  private itemHeight: number
  private bufferSize: number
  private threshold: number
  private items: VirtualScrollItem[] = []
  private visibleItems: Set<number> = new Set()
  private scrollTop = 0
  private containerHeight = 0
  private totalHeight = 0
  private isScrolling = false
  private scrollTimer: number | null = null

  constructor(options: VirtualScrollOptions) {
    this.container = options.container
    this.itemHeight = options.itemHeight
    this.bufferSize = options.bufferSize || 5
    this.threshold = options.threshold || 100

    this.init()
  }

  private init() {
    this.containerHeight = this.container.clientHeight
    this.setupScrollListener()
    this.setupResizeObserver()
  }

  private setupScrollListener() {
    this.container.addEventListener('scroll', this.handleScroll.bind(this), { passive: true })
  }

  private setupResizeObserver() {
    if (typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(() => {
        this.containerHeight = this.container.clientHeight
        this.updateVisibleItems()
      })
      resizeObserver.observe(this.container)
    }
  }

  private handleScroll() {
    this.scrollTop = this.container.scrollTop
    this.isScrolling = true

    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
    }

    this.scrollTimer = window.setTimeout(() => {
      this.isScrolling = false
    }, 150)

    this.updateVisibleItems()
  }

  private updateVisibleItems() {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight)
    const endIndex = Math.min(
      this.items.length - 1,
      Math.ceil((this.scrollTop + this.containerHeight) / this.itemHeight)
    )

    // 添加缓冲区
    const bufferedStartIndex = Math.max(0, startIndex - this.bufferSize)
    const bufferedEndIndex = Math.min(this.items.length - 1, endIndex + this.bufferSize)

    // 隐藏不在可视区域的元素
    this.visibleItems.forEach(index => {
      if (index < bufferedStartIndex || index > bufferedEndIndex) {
        this.hideItem(index)
        this.visibleItems.delete(index)
      }
    })

    // 显示在可视区域的元素
    for (let i = bufferedStartIndex; i <= bufferedEndIndex; i++) {
      if (!this.visibleItems.has(i)) {
        this.showItem(i)
        this.visibleItems.add(i)
      }
    }
  }

  private showItem(index: number) {
    const item = this.items[index]
    if (!item || item.element) return

    // 创建或显示元素
    const element = this.createElement(item)
    if (element) {
      item.element = element
      element.style.position = 'absolute'
      element.style.top = `${item.top}px`
      element.style.height = `${item.height}px`
      element.style.width = '100%'
      this.container.appendChild(element)
    }
  }

  private hideItem(index: number) {
    const item = this.items[index]
    if (item && item.element) {
      this.container.removeChild(item.element)
      item.element = undefined
    }
  }

  private createElement(item: VirtualScrollItem): HTMLElement | null {
    // 这个方法应该由使用者重写
    const element = document.createElement('div')
    element.textContent = `Item ${item.index}`
    element.className = 'virtual-scroll-item'
    return element
  }

  /**
   * 设置数据项
   */
  setItems(count: number, itemHeight?: number) {
    if (itemHeight) {
      this.itemHeight = itemHeight
    }

    this.items = Array.from({ length: count }, (_, index) => ({
      index,
      top: index * this.itemHeight,
      height: this.itemHeight
    }))

    this.totalHeight = count * this.itemHeight
    this.container.style.height = `${this.totalHeight}px`
    this.container.style.position = 'relative'

    this.updateVisibleItems()
  }

  /**
   * 滚动到指定项
   */
  scrollToItem(index: number, behavior: ScrollBehavior = 'smooth') {
    if (index < 0 || index >= this.items.length) return

    const targetTop = index * this.itemHeight
    this.container.scrollTo({
      top: targetTop,
      behavior
    })
  }

  /**
   * 获取当前可见的项索引范围
   */
  getVisibleRange(): { start: number; end: number } {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight)
    const endIndex = Math.min(
      this.items.length - 1,
      Math.ceil((this.scrollTop + this.containerHeight) / this.itemHeight)
    )

    return { start: startIndex, end: endIndex }
  }

  /**
   * 更新项高度
   */
  updateItemHeight(index: number, height: number) {
    if (index < 0 || index >= this.items.length) return

    const item = this.items[index]
    const heightDiff = height - item.height
    item.height = height

    // 更新后续项的位置
    for (let i = index + 1; i < this.items.length; i++) {
      this.items[i].top += heightDiff
    }

    this.totalHeight += heightDiff
    this.container.style.height = `${this.totalHeight}px`

    // 如果项在可视区域内，更新其样式
    if (item.element) {
      item.element.style.height = `${height}px`
    }

    this.updateVisibleItems()
  }

  /**
   * 销毁虚拟滚动
   */
  destroy() {
    this.container.removeEventListener('scroll', this.handleScroll.bind(this))
    
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
    }

    // 清理所有元素
    this.visibleItems.forEach(index => {
      this.hideItem(index)
    })

    this.visibleItems.clear()
    this.items = []
  }

  /**
   * 获取滚动状态
   */
  getScrollInfo() {
    return {
      scrollTop: this.scrollTop,
      containerHeight: this.containerHeight,
      totalHeight: this.totalHeight,
      itemCount: this.items.length,
      visibleCount: this.visibleItems.size,
      isScrolling: this.isScrolling
    }
  }
}

/**
 * 创建虚拟滚动实例
 */
export function createVirtualScroll(options: VirtualScrollOptions): VirtualScrollManager {
  return new VirtualScrollManager(options)
}

/**
 * 文档虚拟滚动适配器
 */
export class DocumentVirtualScroll extends VirtualScrollManager {
  private documentElements: HTMLElement[] = []
  private onItemCreate?: (index: number, element: HTMLElement) => void

  constructor(
    options: VirtualScrollOptions & {
      onItemCreate?: (index: number, element: HTMLElement) => void
    }
  ) {
    super(options)
    this.onItemCreate = options.onItemCreate
  }

  /**
   * 设置文档元素
   */
  setDocumentElements(elements: HTMLElement[]) {
    this.documentElements = elements
    
    // 计算每个元素的实际高度
    const items = elements.map((element, index) => {
      const rect = element.getBoundingClientRect()
      return {
        index,
        top: index > 0 ? this.documentElements.slice(0, index).reduce((sum, el) => {
          return sum + el.getBoundingClientRect().height
        }, 0) : 0,
        height: rect.height || this.itemHeight,
        element: undefined
      }
    })

    this.items = items
    this.totalHeight = items.reduce((sum, item) => sum + item.height, 0)
    this.container.style.height = `${this.totalHeight}px`
    
    this.updateVisibleItems()
  }

  protected createElement(item: VirtualScrollItem): HTMLElement | null {
    const originalElement = this.documentElements[item.index]
    if (!originalElement) return null

    // 克隆原始元素
    const clonedElement = originalElement.cloneNode(true) as HTMLElement
    clonedElement.className += ' virtual-scroll-item'

    if (this.onItemCreate) {
      this.onItemCreate(item.index, clonedElement)
    }

    return clonedElement
  }
}

/**
 * 检测是否需要虚拟滚动
 */
export function shouldUseVirtualScroll(itemCount: number, threshold = 1000): boolean {
  return itemCount > threshold
}

/**
 * 估算元素高度
 */
export function estimateItemHeight(container: HTMLElement, sampleSize = 10): number {
  const children = Array.from(container.children) as HTMLElement[]
  if (children.length === 0) return 100 // 默认高度

  const sampleElements = children.slice(0, Math.min(sampleSize, children.length))
  const totalHeight = sampleElements.reduce((sum, element) => {
    return sum + element.getBoundingClientRect().height
  }, 0)

  return Math.ceil(totalHeight / sampleElements.length)
}
