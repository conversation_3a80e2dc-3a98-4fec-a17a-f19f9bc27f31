import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'virtual:windi.css'
import EventHub from '@/utils/EventHub'

import App from './App.vue'
import router from './router'



const app = createApp(App)

// Register Pinia store
app.use(createPinia())

// Register Vue Router
app.use(router)

// Register Element Plus
app.use(ElementPlus)

app.use(EventHub)

// Register Element Plus Icons
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.mount('#app')
