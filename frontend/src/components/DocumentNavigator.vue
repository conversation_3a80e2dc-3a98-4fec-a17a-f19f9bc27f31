<template>
  <div class="document-navigator">
    <!-- 搜索框 -->
    <div class="navigator-search">
      <el-input
        v-model="searchText"
        placeholder="搜索文档内容..."
        :prefix-icon="Search"
        clearable
        @input="handleSearch"
        @keyup.enter="searchNext"
      />
      <div v-if="searchResults.length > 0" class="search-info">
        <span>{{ currentSearchIndex + 1 }} / {{ searchResults.length }}</span>
        <el-button-group size="small">
          <el-button :icon="ArrowUp" @click="searchPrevious" :disabled="searchResults.length === 0" />
          <el-button :icon="ArrowDown" @click="searchNext" :disabled="searchResults.length === 0" />
        </el-button-group>
      </div>
    </div>

    <!-- 文档大纲 -->
    <div class="navigator-outline">
      <div class="outline-header">
        <h4>文档大纲</h4>
        <el-button
          text
          :icon="Refresh"
          @click="refreshOutline"
          size="small"
        />
      </div>
      
      <el-tree
        ref="outlineTreeRef"
        :data="outlineTree"
        :props="treeProps"
        node-key="id"
        :current-node-key="currentNodeKey"
        @node-click="handleNodeClick"
        :expand-on-click-node="false"
        :highlight-current="true"
        class="outline-tree"
      >
        <template #default="{ node, data }">
          <div class="outline-node">
            <span class="node-label">{{ data.label }}</span>
            <span class="node-info">
              <el-tag size="small" type="info">{{ data.type }}</el-tag>
              <span class="node-page">第{{ data.page }}页</span>
            </span>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 缩略图导航 -->
    <div class="navigator-thumbnails" v-if="showThumbnails">
      <div class="thumbnails-header">
        <h4>页面缩略图</h4>
        <el-switch
          v-model="showThumbnails"
          size="small"
          @change="toggleThumbnails"
        />
      </div>
      
      <div class="thumbnails-grid">
        <div
          v-for="(thumbnail, index) in thumbnails"
          :key="index"
          class="thumbnail-item"
          :class="{ active: currentPage === index + 1 }"
          @click="goToPage(index + 1)"
        >
          <div class="thumbnail-preview">
            <img
              v-if="thumbnail.image"
              :src="thumbnail.image"
              :alt="`第${index + 1}页`"
              loading="lazy"
            />
            <div v-else class="thumbnail-placeholder">
              <el-icon><Document /></el-icon>
            </div>
          </div>
          <div class="thumbnail-label">第{{ index + 1 }}页</div>
        </div>
      </div>
    </div>

    <!-- 书签 -->
    <div class="navigator-bookmarks">
      <div class="bookmarks-header">
        <h4>书签</h4>
        <el-button
          text
          :icon="Plus"
          @click="addBookmark"
          size="small"
          title="添加书签"
        />
      </div>
      
      <div class="bookmarks-list">
        <div
          v-for="bookmark in bookmarks"
          :key="bookmark.id"
          class="bookmark-item"
          @click="goToBookmark(bookmark)"
        >
          <div class="bookmark-content">
            <span class="bookmark-title">{{ bookmark.title }}</span>
            <span class="bookmark-time">{{ formatTime(bookmark.createdAt) }}</span>
          </div>
          <el-button
            text
            :icon="Delete"
            @click.stop="removeBookmark(bookmark.id)"
            size="small"
            type="danger"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  ArrowUp,
  ArrowDown,
  Refresh,
  Document,
  Plus,
  Delete
} from '@element-plus/icons-vue'



// Props
interface Props {
  documentContent?: string
  currentPage?: number
  totalPages?: number
}

const props = withDefaults(defineProps<Props>(), {
  currentPage: 1,
  totalPages: 1
})

// Emits
const emit = defineEmits<{
  'go-to-element': [elementId: string]
  'go-to-page': [page: number]
  'search-highlight': [text: string, index: number]
  'bookmark-added': [bookmark: Bookmark]
}>()

// Types
interface OutlineNode {
  id: string
  label: string
  type: string
  page: number
  elementId: string
  level: number
  children?: OutlineNode[]
}

interface SearchResult {
  text: string
  elementId: string
  index: number
  position: { start: number; end: number }
}

interface Thumbnail {
  page: number
  image?: string
  width: number
  height: number
}

interface Bookmark {
  id: string
  title: string
  elementId: string
  page: number
  createdAt: Date
}

// Refs
const outlineTreeRef = ref()

// State
const searchText = ref('')
const searchResults = ref<SearchResult[]>([])
const currentSearchIndex = ref(-1)
const outlineTree = ref<OutlineNode[]>([])
const currentNodeKey = ref('')
const showThumbnails = ref(false)
const thumbnails = ref<Thumbnail[]>([])
const bookmarks = ref<Bookmark[]>([])

// Tree props
const treeProps = {
  children: 'children',
  label: 'label'
}

// Computed
const currentPage = computed(() => props.currentPage)

// Methods
const handleSearch = (value: string) => {
  if (!value.trim()) {
    clearSearch()
    return
  }

  searchInDocument(value)
}

const searchInDocument = (text: string) => {
  // 模拟搜索功能
  // 实际实现中应该在文档内容中搜索
  searchResults.value = []
  currentSearchIndex.value = -1

  if (props.documentContent) {
    const regex = new RegExp(text, 'gi')
    let match
    let index = 0

    while ((match = regex.exec(props.documentContent)) !== null) {
      searchResults.value.push({
        text: match[0],
        elementId: `search-result-${index}`,
        index,
        position: {
          start: match.index,
          end: match.index + match[0].length
        }
      })
      index++
    }

    if (searchResults.value.length > 0) {
      currentSearchIndex.value = 0
      highlightSearchResult(0)
    }
  }
}

const searchNext = () => {
  if (searchResults.value.length === 0) return

  currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
  highlightSearchResult(currentSearchIndex.value)
}

const searchPrevious = () => {
  if (searchResults.value.length === 0) return

  currentSearchIndex.value = currentSearchIndex.value === 0 
    ? searchResults.value.length - 1 
    : currentSearchIndex.value - 1
  highlightSearchResult(currentSearchIndex.value)
}

const highlightSearchResult = (index: number) => {
  const result = searchResults.value[index]
  if (result) {
    emit('search-highlight', result.text, index)
    emit('go-to-element', result.elementId)
  }
}

const clearSearch = () => {
  searchResults.value = []
  currentSearchIndex.value = -1
}

const refreshOutline = () => {
  generateOutline()
}

const generateOutline = () => {
  // 模拟生成文档大纲
  // 实际实现中应该解析文档内容生成大纲
  outlineTree.value = [
    {
      id: 'section-1',
      label: '第一章 概述',
      type: 'chapter',
      page: 1,
      elementId: 'chapter-1',
      level: 1,
      children: [
        {
          id: 'section-1-1',
          label: '1.1 背景介绍',
          type: 'section',
          page: 1,
          elementId: 'section-1-1',
          level: 2
        },
        {
          id: 'section-1-2',
          label: '1.2 目标与范围',
          type: 'section',
          page: 2,
          elementId: 'section-1-2',
          level: 2
        }
      ]
    },
    {
      id: 'section-2',
      label: '第二章 详细内容',
      type: 'chapter',
      page: 3,
      elementId: 'chapter-2',
      level: 1,
      children: [
        {
          id: 'section-2-1',
          label: '2.1 技术方案',
          type: 'section',
          page: 3,
          elementId: 'section-2-1',
          level: 2
        }
      ]
    }
  ]
}

const handleNodeClick = (data: OutlineNode) => {
  currentNodeKey.value = data.id
  emit('go-to-element', data.elementId)
  emit('go-to-page', data.page)
}

const toggleThumbnails = (show: boolean) => {
  if (show) {
    generateThumbnails()
  }
}

const generateThumbnails = () => {
  // 模拟生成缩略图
  thumbnails.value = Array.from({ length: props.totalPages }, (_, index) => ({
    page: index + 1,
    width: 150,
    height: 200
  }))
}

const goToPage = (page: number) => {
  emit('go-to-page', page)
}

const addBookmark = () => {
  const bookmark: Bookmark = {
    id: `bookmark-${Date.now()}`,
    title: `书签 ${bookmarks.value.length + 1}`,
    elementId: `current-position`,
    page: currentPage.value,
    createdAt: new Date()
  }

  bookmarks.value.push(bookmark)
  emit('bookmark-added', bookmark)
  ElMessage.success('书签添加成功')
}

const removeBookmark = (id: string) => {
  const index = bookmarks.value.findIndex(b => b.id === id)
  if (index > -1) {
    bookmarks.value.splice(index, 1)
    ElMessage.success('书签删除成功')
  }
}

const goToBookmark = (bookmark: Bookmark) => {
  emit('go-to-element', bookmark.elementId)
  emit('go-to-page', bookmark.page)
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Watch
watch(() => props.documentContent, () => {
  nextTick(() => {
    generateOutline()
  })
}, { immediate: true })

// 初始化
generateOutline()
</script>

<style scoped>
.document-navigator {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-right: 1px solid #e4e7ed;
}

.navigator-search {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.search-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.navigator-outline {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.outline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.outline-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.outline-tree {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.outline-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.node-label {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.node-page {
  font-size: 11px;
  color: #999;
}

.navigator-thumbnails {
  border-top: 1px solid #f0f0f0;
  max-height: 300px;
  overflow-y: auto;
}

.thumbnails-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.thumbnails-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.thumbnails-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  padding: 12px;
}

.thumbnail-item {
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.2s;
}

.thumbnail-item:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.thumbnail-item.active {
  border: 2px solid #409eff;
}

.thumbnail-preview {
  aspect-ratio: 3/4;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  color: #ccc;
  font-size: 24px;
}

.thumbnail-label {
  padding: 4px;
  text-align: center;
  font-size: 11px;
  color: #666;
  background: white;
}

.navigator-bookmarks {
  border-top: 1px solid #f0f0f0;
  max-height: 200px;
  overflow-y: auto;
}

.bookmarks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.bookmarks-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.bookmarks-list {
  padding: 8px;
}

.bookmark-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bookmark-item:hover {
  background-color: #f5f7fa;
}

.bookmark-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.bookmark-title {
  font-size: 13px;
  line-height: 1.4;
}

.bookmark-time {
  font-size: 11px;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .thumbnails-grid {
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 6px;
    padding: 8px;
  }
  
  .navigator-search {
    padding: 12px;
  }
  
  .outline-header,
  .thumbnails-header,
  .bookmarks-header {
    padding: 8px 12px;
  }
}
</style>
