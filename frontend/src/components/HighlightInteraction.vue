<template>
  <div class="highlight-interaction" ref="interactionRef">
    <!-- 高亮控制面板 -->
    <div v-if="showControls" class="highlight-controls">
      <div class="controls-header">
        <h4>高亮控制</h4>
        <el-button
          :icon="Close"
          @click="hideControls"
          size="small"
          text
        />
      </div>
      
      <div class="controls-content">
        <div class="highlight-stats">
          <el-statistic title="总高亮数" :value="totalHighlights" />
          <el-statistic title="提取结果" :value="extractHighlights" />
          <el-statistic title="检测问题" :value="detectHighlights" />
          <el-statistic title="准确率" :value="accuracyRate" suffix="%" />
        </div>
        
        <div class="highlight-actions">
          <el-button @click="clearAllHighlights" size="small">
            清除所有高亮
          </el-button>
          <el-button @click="refreshHighlights" size="small" type="primary">
            刷新高亮
          </el-button>
        </div>
        
        <div class="highlight-settings">
          <el-form label-position="top" size="small">
            <el-form-item label="高亮透明度">
              <el-slider
                v-model="highlightOpacity"
                :min="0.1"
                :max="1"
                :step="0.1"
                @change="updateHighlightOpacity"
              />
            </el-form-item>
            
            <el-form-item label="动画效果">
              <el-switch
                v-model="enableAnimation"
                @change="updateAnimationSetting"
              />
            </el-form-item>
            
            <el-form-item label="显示工具提示">
              <el-switch
                v-model="showTooltips"
                @change="updateTooltipSetting"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- 高亮状态指示器 -->
    <div class="highlight-indicator" v-if="activeHighlight">
      <div class="indicator-content">
        <el-tag :type="activeHighlight.type === 'extract' ? 'success' : 'warning'">
          {{ activeHighlight.type === 'extract' ? '提取结果' : '检测问题' }}
        </el-tag>
        <span class="indicator-text">
          置信度: {{ Math.round(activeHighlight.position.confidence * 100) }}%
        </span>
        <el-button
          :icon="Close"
          @click="clearActiveHighlight"
          size="small"
          text
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { createPositionMapper, scrollToPosition } from '@/utils/positionMapper'
import { createHighlightManager } from '@/utils/highlight'
import type { PositionMapper, PositionMapping } from '@/utils/positionMapper'
import type { HighlightManager, HighlightMarker } from '@/utils/highlight'



// Props
interface Props {
  documentContainer?: HTMLElement
  autoHighlight?: boolean
  showControls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoHighlight: true,
  showControls: false
})

// Emits
const emit = defineEmits<{
  'highlight-click': [marker: HighlightMarker]
  'highlight-added': [marker: HighlightMarker]
  'highlight-removed': [id: string]
  'position-mapped': [mapping: PositionMapping]
}>()

// Refs
const interactionRef = ref<HTMLElement>()

// State
const positionMapper = ref<PositionMapper | null>(null)
const highlightManager = ref<HighlightManager | null>(null)
const activeHighlight = ref<HighlightMarker | null>(null)
const highlightOpacity = ref(0.5)
const enableAnimation = ref(true)
const showTooltips = ref(true)

// Computed
const totalHighlights = computed(() => 
  highlightManager.value?.getAllHighlights().length || 0
)

const extractHighlights = computed(() => 
  highlightManager.value?.getHighlightsByType('extract').length || 0
)

const detectHighlights = computed(() => 
  highlightManager.value?.getHighlightsByType('detect').length || 0
)

const accuracyRate = computed(() => {
  if (!positionMapper.value) return 0
  const stats = positionMapper.value.getStats()
  return Math.round(stats.accuracyRate * 100)
})

// Methods
const initializeManagers = () => {
  if (props.documentContainer) {
    positionMapper.value = createPositionMapper(props.documentContainer)
    highlightManager.value = createHighlightManager(props.documentContainer)
    
    // 监听高亮点击事件
    props.documentContainer.addEventListener('highlight-click', handleHighlightClick)
  }
}

const handleHighlightClick = (event: CustomEvent) => {
  const marker = event.detail.marker as HighlightMarker
  activeHighlight.value = marker
  emit('highlight-click', marker)
}

const addHighlight = (
  id: string,
  type: 'extract' | 'detect',
  documentPosition: any,
  data: any
) => {
  if (!positionMapper.value || !highlightManager.value) {
    ElMessage.error('高亮管理器未初始化')
    return null
  }

  // 查找DOM位置
  const mapping = positionMapper.value.findDOMPosition(documentPosition)
  if (!mapping) {
    ElMessage.warning('无法找到对应的文档位置')
    return null
  }

  emit('position-mapped', mapping)

  // 添加高亮标记
  const marker = highlightManager.value.addHighlight(
    id,
    type,
    mapping,
    data,
    {
      animation: enableAnimation.value,
      persistent: true
    }
  )

  emit('highlight-added', marker)
  return marker
}

const removeHighlight = (id: string) => {
  if (highlightManager.value?.removeHighlight(id)) {
    emit('highlight-removed', id)
    
    if (activeHighlight.value?.id === id) {
      activeHighlight.value = null
    }
  }
}

const clearAllHighlights = () => {
  highlightManager.value?.clearAllHighlights()
  activeHighlight.value = null
  ElMessage.success('已清除所有高亮')
}

const clearActiveHighlight = () => {
  activeHighlight.value = null
}

const refreshHighlights = () => {
  if (!highlightManager.value) return
  
  // 重新计算所有高亮位置
  highlightManager.value.updateHighlightPositions()
  ElMessage.success('高亮位置已刷新')
}

const updateHighlightOpacity = (value: number) => {
  // 更新所有高亮的透明度
  const highlights = highlightManager.value?.getAllHighlights() || []
  highlights.forEach(marker => {
    const element = document.querySelector(`[data-highlight-id="${marker.id}"]`) as HTMLElement
    if (element) {
      element.style.opacity = value.toString()
    }
  })
}

const updateAnimationSetting = (enabled: boolean) => {
  // 更新动画设置
  enableAnimation.value = enabled
}

const updateTooltipSetting = (enabled: boolean) => {
  // 更新工具提示设置
  showTooltips.value = enabled
}

const hideControls = () => {
  // 隐藏控制面板
}

const scrollToHighlight = (id: string) => {
  const marker = highlightManager.value?.getHighlight(id)
  if (marker) {
    scrollToPosition(marker.position)
    highlightManager.value?.setActiveHighlight(id)
    activeHighlight.value = marker
  }
}

const highlightResultItem = (item: any) => {
  if (!item.position) {
    ElMessage.warning('该结果项没有位置信息')
    return
  }

  const id = `result-${item.id}`
  const existingMarker = highlightManager.value?.getHighlight(id)
  
  if (existingMarker) {
    // 如果已存在，则滚动到该位置并激活
    scrollToHighlight(id)
  } else {
    // 创建新的高亮
    const marker = addHighlight(id, item.type, item.position, item)
    if (marker) {
      scrollToPosition(marker.position)
      highlightManager.value?.setActiveHighlight(id)
      activeHighlight.value = marker
    }
  }
}

// 监听文档容器变化
watch(() => props.documentContainer, (newContainer) => {
  if (newContainer) {
    initializeManagers()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.documentContainer) {
    initializeManagers()
  }
})

onUnmounted(() => {
  if (props.documentContainer) {
    props.documentContainer.removeEventListener('highlight-click', handleHighlightClick)
  }
  
  highlightManager.value?.destroy()
  positionMapper.value?.clearCache()
})

// 暴露方法
defineExpose({
  addHighlight,
  removeHighlight,
  clearAllHighlights,
  scrollToHighlight,
  highlightResultItem,
  getHighlightStats: () => positionMapper.value?.getStats(),
  getAllHighlights: () => highlightManager.value?.getAllHighlights() || []
})
</script>

<style scoped>
.highlight-interaction {
  position: relative;
}

.highlight-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.controls-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.controls-content {
  padding: 16px;
}

.highlight-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.highlight-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.highlight-actions .el-button {
  flex: 1;
}

.highlight-settings {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.highlight-indicator {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 12px 16px;
}

.indicator-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.indicator-text {
  font-size: 14px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .highlight-controls {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    border-radius: 0;
    max-height: 50vh;
  }
  
  .highlight-stats {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .highlight-actions {
    flex-direction: column;
  }
  
  .highlight-indicator {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
  }
  
  .indicator-content {
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>
