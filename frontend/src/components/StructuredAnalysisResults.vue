<template>
  <div class="structured-analysis-results">
    <!-- 头部统计信息 -->
    <div class="results-header">
      <div class="statistics-summary">
        <div class="stat-item">
          <span class="stat-label">提取项</span>
          <span class="stat-count">{{ statistics.totalExtractItems }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">检测项</span>
          <span class="stat-count">{{ statistics.totalDetectItems }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">异常检测</span>
          <span class="stat-count error">{{ statistics.abnormalDetects }}</span>
        </div>
      </div>

      <el-button :icon="Refresh" @click="refreshResults" :loading="loading" size="small" text />
    </div>

    <!-- 搜索和过滤 -->
    <div class="results-filters">
      <el-input v-model="searchText" placeholder="搜索结果..." :prefix-icon="Search" clearable @input="handleSearch" size="small" style="width: 200px" />

      <el-select v-model="selectedSeverity" placeholder="严重程度" clearable size="small" style="width: 120px" @change="handleFilter">
        <el-option label="高" value="high" />
        <el-option label="中" value="medium" />
        <el-option label="低" value="low" />
      </el-select>
    </div>

    <!-- 结果分类Tab -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="results-tabs">
      <el-tab-pane label="提取结果" name="extract">
        <template #label>
          <span class="tab-label">
            <el-icon><DocumentCopy /></el-icon>
            提取结果
            <el-badge :value="statistics.totalExtractItems" :max="99" type="success" />
          </span>
        </template>
      </el-tab-pane>

      <el-tab-pane label="检测问题" name="detect">
        <template #label>
          <span class="tab-label">
            <el-icon><Warning /></el-icon>
            检测问题
            <el-badge :value="statistics.totalDetectItems" :max="99" type="warning" />
          </span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 结果内容 -->
    <div class="results-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 空状态 -->
      <div v-else-if="filteredResults.length === 0" class="empty-container">
        <el-empty :description="getEmptyDescription()">
          <template v-if="!searchText.trim() && props.documentId" #default>
            <el-button type="primary" :icon="Search" @click="manualTriggerAnalysis" :loading="loading"> 开始AI分析 </el-button>
          </template>
        </el-empty>
      </div>

      <!-- 提取结果列表 -->
      <div v-else-if="activeTab === 'extract'" class="extract-results">
        <div v-for="item in filteredExtractItems" :key="item.id" class="extract-item" :class="{ active: selectedItem?.id === item.id }" @click="handleItemClick(item)">
          <div class="item-header">
            <h4 class="item-title">{{ item.title }}</h4>
            <div class="item-actions">
              <el-tag :type="getExtractStatusType(item.status)" size="small">
                {{ item.status }}
              </el-tag>
              <el-button :icon="More" @click.stop="showItemDetail(item)" size="small" text title="查看详情" />
            </div>
          </div>

          <div class="item-content">
            <div class="sources-summary">
              <el-icon><Document /></el-icon>
              <span>{{ item.sources.length }} 处原文引用</span>
            </div>

            <!-- 多原文引用轮播展示 -->
            <div v-if="item.sources.length > 0" class="sources-carousel">
              <div class="carousel-container">
                <!-- 当前显示的引用 -->
                <div class="current-source">
                  <div class="source-item clickable" @click.stop="handleSourceClick(getCurrentSource(item), item)">
                    <div class="source-header">
                      <el-tag size="small" type="info">{{ getCurrentSource(item).referenceIndex }}</el-tag>
                      <span class="source-location">{{ getCurrentSource(item).location }}</span>
                      <el-icon class="source-jump-icon"><Position /></el-icon>
                    </div>
                    <div class="source-content">{{ getSourceContentPreview(getCurrentSource(item).content) }}</div>
                  </div>
                </div>

                <!-- 多引用控制器 -->
                <div v-if="item.sources.length > 1" class="carousel-controls">
                  <el-button :icon="ArrowLeft" @click.stop="previousSource(item)" size="small" circle :disabled="getCurrentSourceIndex(item) === 0" />

                  <div class="source-indicator">
                    <span class="current-index">{{ getCurrentSourceIndex(item) + 1 }}</span>
                    <span class="separator">/</span>
                    <span class="total-count">{{ item.sources.length }}</span>
                  </div>

                  <el-button :icon="ArrowRight" @click.stop="nextSource(item)" size="small" circle :disabled="getCurrentSourceIndex(item) === item.sources.length - 1" />
                </div>
              </div>

              <!-- 查看全部按钮 -->
              <div class="view-all-sources">
                <el-button text size="small" @click.stop="showItemDetail(item)"> 查看全部 {{ item.sources.length }} 处引用 </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 检测结果列表 -->
      <div v-else-if="activeTab === 'detect'" class="detect-results">
        <div
          v-for="item in filteredDetectItems"
          :key="item.id"
          class="detect-item"
          :class="{
            active: selectedItem?.id === item.id,
            'severity-high': item.severity === 'high',
            'severity-medium': item.severity === 'medium',
            'severity-low': item.severity === 'low'
          }"
          @click="handleItemClick(item)"
        >
          <div class="item-header">
            <h4 class="item-title">{{ item.title }}</h4>
            <div class="item-actions">
              <el-tag :color="getDetectResultColor(item.result)" size="small" effect="dark">
                {{ item.result }}
              </el-tag>
              <el-tag :type="getSeverityType(item.severity)" size="small">
                {{ item.severity }}
              </el-tag>
              <el-button :icon="More" @click.stop="showItemDetail(item)" size="small" text title="查看详情" />
            </div>
          </div>

          <div class="item-content">
            <div class="detect-reason">
              <el-icon><InfoFilled /></el-icon>
              <span>{{ item.reason }}</span>
            </div>

            <div class="sources-summary">
              <el-icon><Document /></el-icon>
              <span>{{ item.sources.length }} 处原文引用</span>
            </div>

            <!-- 多原文引用轮播展示 -->
            <div v-if="item.sources.length > 0" class="sources-carousel">
              <div class="carousel-container">
                <!-- 当前显示的引用 -->
                <div class="current-source">
                  <div class="source-item clickable" @click.stop="handleSourceClick(getCurrentSource(item), item)">
                    <div class="source-header">
                      <el-tag size="small" type="info">{{ getCurrentSource(item).referenceIndex }}</el-tag>
                      <span class="source-location">{{ getCurrentSource(item).location }}</span>
                      <el-icon class="source-jump-icon"><Position /></el-icon>
                    </div>
                    <div class="source-content">{{ getSourceContentPreview(getCurrentSource(item).content) }}</div>
                  </div>
                </div>

                <!-- 多引用控制器 -->
                <div v-if="item.sources.length > 1" class="carousel-controls">
                  <el-button :icon="ArrowLeft" @click.stop="previousSource(item)" size="small" circle :disabled="getCurrentSourceIndex(item) === 0" />

                  <div class="source-indicator">
                    <span class="current-index">{{ getCurrentSourceIndex(item) + 1 }}</span>
                    <span class="separator">/</span>
                    <span class="total-count">{{ item.sources.length }}</span>
                  </div>

                  <el-button :icon="ArrowRight" @click.stop="nextSource(item)" size="small" circle :disabled="getCurrentSourceIndex(item) === item.sources.length - 1" />
                </div>
              </div>

              <!-- 查看全部按钮 -->
              <div class="view-all-sources">
                <el-button text size="small" @click.stop="showItemDetail(item)"> 查看全部 {{ item.sources.length }} 处引用 </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer v-model="showDetail" :title="selectedItem?.title || '详情'" direction="rtl" size="500px">
      <div v-if="selectedItem" class="detail-content">
        <!-- 提取项详情 -->
        <div v-if="selectedItem.type === 'extract'" class="extract-detail">
          <div class="detail-header">
            <el-tag :type="getExtractStatusType(selectedItem.status)">
              {{ selectedItem.status }}
            </el-tag>
            <span class="importance">重要性: {{ selectedItem.importance }}</span>
          </div>

          <div class="sources-detail">
            <h5>原文引用 ({{ selectedItem.sources.length }})</h5>

            <!-- 详情页面的引用轮播 -->
            <div class="detail-sources-carousel">
              <div class="detail-carousel-container">
                <!-- 当前显示的引用 -->
                <div class="detail-current-source">
                  <div class="source-detail-item clickable" @click="handleSourceClick(getCurrentDetailSource(), selectedItem)">
                    <div class="source-detail-header">
                      <el-tag size="small" type="info">{{ getCurrentDetailSource().referenceIndex }}</el-tag>
                      <span class="source-location">{{ getCurrentDetailSource().location }}</span>
                      <el-icon class="source-jump-icon"><Position /></el-icon>
                    </div>
                    <div class="source-detail-content">{{ getCurrentDetailSource().content }}</div>
                  </div>
                </div>

                <!-- 详情页面的控制器 -->
                <div v-if="selectedItem.sources.length > 1" class="detail-carousel-controls">
                  <el-button :icon="ArrowLeft" @click="previousDetailSource()" size="small" :disabled="currentDetailSourceIndex === 0"> 上一个 </el-button>

                  <div class="detail-source-indicator">
                    <span class="current-index">{{ currentDetailSourceIndex + 1 }}</span>
                    <span class="separator"> / </span>
                    <span class="total-count">{{ selectedItem.sources.length }}</span>
                  </div>

                  <el-button :icon="ArrowRight" @click="nextDetailSource()" size="small" :disabled="currentDetailSourceIndex === selectedItem.sources.length - 1"> 下一个 </el-button>
                </div>
              </div>

              <!-- 引用索引指示器 -->
              <div v-if="selectedItem.sources.length > 1" class="source-dots">
                <span v-for="(source, index) in selectedItem.sources" :key="index" class="source-dot" :class="{ active: index === currentDetailSourceIndex }" @click="setDetailSourceIndex(index)">
                  {{ index + 1 }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 检测项详情 -->
        <div v-else-if="selectedItem.type === 'detect'" class="detect-detail">
          <div class="detail-header">
            <el-tag :color="getDetectResultColor(selectedItem.result)" effect="dark">
              {{ selectedItem.result }}
            </el-tag>
            <el-tag :type="getSeverityType(selectedItem.severity)">
              {{ selectedItem.severity }}
            </el-tag>
          </div>

          <div class="detect-reason-detail">
            <h5>判断原因</h5>
            <p>{{ selectedItem.reason }}</p>
          </div>

          <div class="sources-detail">
            <h5>原文引用 ({{ selectedItem.sources.length }})</h5>

            <!-- 详情页面的引用轮播 -->
            <div class="detail-sources-carousel">
              <div class="detail-carousel-container">
                <!-- 当前显示的引用 -->
                <div class="detail-current-source">
                  <div class="source-detail-item clickable" @click="handleSourceClick(getCurrentDetailSource(), selectedItem)">
                    <div class="source-detail-header">
                      <el-tag size="small" type="info">{{ getCurrentDetailSource().referenceIndex }}</el-tag>
                      <span class="source-location">{{ getCurrentDetailSource().location }}</span>
                      <el-icon class="source-jump-icon"><Position /></el-icon>
                    </div>
                    <div class="source-detail-content">{{ getCurrentDetailSource().content }}</div>
                  </div>
                </div>

                <!-- 详情页面的控制器 -->
                <div v-if="selectedItem.sources.length > 1" class="detail-carousel-controls">
                  <el-button :icon="ArrowLeft" @click="previousDetailSource()" size="small" :disabled="currentDetailSourceIndex === 0"> 上一个 </el-button>

                  <div class="detail-source-indicator">
                    <span class="current-index">{{ currentDetailSourceIndex + 1 }}</span>
                    <span class="separator"> / </span>
                    <span class="total-count">{{ selectedItem.sources.length }}</span>
                  </div>

                  <el-button :icon="ArrowRight" @click="nextDetailSource()" size="small" :disabled="currentDetailSourceIndex === selectedItem.sources.length - 1"> 下一个 </el-button>
                </div>
              </div>

              <!-- 引用索引指示器 -->
              <div v-if="selectedItem.sources.length > 1" class="source-dots">
                <span v-for="(source, index) in selectedItem.sources" :key="index" class="source-dot" :class="{ active: index === currentDetailSourceIndex }" @click="setDetailSourceIndex(index)">
                  {{ index + 1 }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, DocumentCopy, Warning, More, InfoFilled, Document, Position, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { getStructuredAnalysisResults, analyzeDocument, getAnalysisStatus, getDetectItemResultColor, getExtractItemStatusColor } from '@/api/analysis'
import type { StructuredAnalysisResult, ExtractItem, DetectItem, AnalysisStatistics, SourceReference } from '@/types'

/**
 * Structured Analysis Results Component
 *
 * {{RIPER-5+SMART-6:
 *   Action: "Parallel-Added"
 *   Task_ID: "new-structured-component"
 *   Timestamp: "2025-08-07T16:00:00+08:00"
 *   Authoring_Subagent: "vue-frontend-expert"
 *   Principle_Applied: "SOLID-S (单一职责原则)"
 *   Quality_Check: "结构化分析结果展示组件，支持多原文引用。"
 * }}
 */

// Props
interface Props {
  documentId?: number
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

// Emits
const emit = defineEmits<{
  'item-click': [item: ExtractItem | DetectItem]
  'locate-request': [item: ExtractItem | DetectItem]
  'item-select': [item: ExtractItem | DetectItem | null]
  'source-click': [source: SourceReference, item: ExtractItem | DetectItem]
}>()

// State
const loading = ref(false)
const activeTab = ref<'extract' | 'detect'>('extract')
const searchText = ref('')
const selectedSeverity = ref('')
const selectedItem = ref<((ExtractItem | DetectItem) & { type: string }) | null>(null)
const showDetail = ref(false)

// 引用切换状态
const currentSourceIndexMap = ref<Record<string, number>>({})
const currentDetailSourceIndex = ref(0)

// 数据
const structuredResult = ref<StructuredAnalysisResult | null>(null)
const statistics = ref<AnalysisStatistics>({
  totalExtractItems: 0,
  totalDetectItems: 0,
  successfulExtracts: 0,
  abnormalDetects: 0,
  normalDetects: 0,
  highSeverityDetects: 0,
  mediumSeverityDetects: 0,
  lowSeverityDetects: 0,
  processingStatus: '',
  completionPercentage: 0
})

// Computed
const extractItems = computed(() => structuredResult.value?.extractItems || [])
const detectItems = computed(() => structuredResult.value?.detectItems || [])

const filteredExtractItems = computed(() => {
  let items = extractItems.value

  if (searchText.value.trim()) {
    const keyword = searchText.value.toLowerCase()
    items = items.filter(
      item =>
        item.title.toLowerCase().includes(keyword) ||
        item.content.toLowerCase().includes(keyword) ||
        item.sources.some(source => source.content.toLowerCase().includes(keyword) || source.location.toLowerCase().includes(keyword))
    )
  }

  return items
})

const filteredDetectItems = computed(() => {
  let items = detectItems.value

  if (searchText.value.trim()) {
    const keyword = searchText.value.toLowerCase()
    items = items.filter(
      item =>
        item.title.toLowerCase().includes(keyword) ||
        item.reason.toLowerCase().includes(keyword) ||
        item.sources.some(source => source.content.toLowerCase().includes(keyword) || source.location.toLowerCase().includes(keyword))
    )
  }

  if (selectedSeverity.value) {
    items = items.filter(item => item.severity === selectedSeverity.value)
  }

  return items
})

const filteredResults = computed(() => {
  return activeTab.value === 'extract' ? filteredExtractItems.value : filteredDetectItems.value
})

// Methods
const loadResults = async () => {
  if (!props.documentId) return

  loading.value = true
  try {
    const response = await getStructuredAnalysisResults(props.documentId)

    if (response.success) {
      structuredResult.value = response.data
      statistics.value = response.data.statistics

      // 初始化引用索引
      initializeSourceIndexes()

      ElMessage.success('分析结果加载完成')
    } else {
      // 如果没有结构化结果，尝试触发分析
      await triggerAnalysis()
    }
  } catch (error: any) {
    console.error('加载结构化分析结果失败:', error)
    ElMessage.error(`加载失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 初始化引用索引
const initializeSourceIndexes = () => {
  currentSourceIndexMap.value = {}

  // 为所有项目初始化引用索引为0
  const allItems = [...extractItems.value, ...detectItems.value]
  allItems.forEach(item => {
    if (item.sources && item.sources.length > 0) {
      currentSourceIndexMap.value[item.id] = 0
    }
  })
}

// 获取当前显示的引用
const getCurrentSource = (item: ExtractItem | DetectItem): SourceReference => {
  const index = currentSourceIndexMap.value[item.id] || 0
  return item.sources[index] || item.sources[0]
}

// 获取当前引用索引
const getCurrentSourceIndex = (item: ExtractItem | DetectItem): number => {
  return currentSourceIndexMap.value[item.id] || 0
}

// 切换到下一个引用
const nextSource = (item: ExtractItem | DetectItem) => {
  const currentIndex = getCurrentSourceIndex(item)
  if (currentIndex < item.sources.length - 1) {
    currentSourceIndexMap.value[item.id] = currentIndex + 1
  }
}

// 切换到上一个引用
const previousSource = (item: ExtractItem | DetectItem) => {
  const currentIndex = getCurrentSourceIndex(item)
  if (currentIndex > 0) {
    currentSourceIndexMap.value[item.id] = currentIndex - 1
  }
}

// 详情页面的引用切换
const getCurrentDetailSource = (): SourceReference => {
  if (!selectedItem.value || !selectedItem.value.sources) {
    return { referenceIndex: '', location: '', content: '' }
  }
  return selectedItem.value.sources[currentDetailSourceIndex.value] || selectedItem.value.sources[0]
}

const nextDetailSource = () => {
  if (selectedItem.value && currentDetailSourceIndex.value < selectedItem.value.sources.length - 1) {
    currentDetailSourceIndex.value++
  }
}

const previousDetailSource = () => {
  if (currentDetailSourceIndex.value > 0) {
    currentDetailSourceIndex.value--
  }
}

const setDetailSourceIndex = (index: number) => {
  if (selectedItem.value && index >= 0 && index < selectedItem.value.sources.length) {
    currentDetailSourceIndex.value = index
  }
}

const triggerAnalysis = async () => {
  try {
    const response = await analyzeDocument(props.documentId!)

    if (response.success) {
      ElMessage.success('开始分析文档，请稍候...')
      await pollAnalysisStatus()
    } else {
      throw new Error(response.message || '触发分析失败')
    }
  } catch (error: any) {
    console.error('触发分析失败:', error)
    ElMessage.error('触发分析失败')
    loading.value = false
  }
}

const pollAnalysisStatus = async () => {
  const maxAttempts = 30
  let attempts = 0

  const poll = async (): Promise<void> => {
    try {
      attempts++
      const statusResponse = await getAnalysisStatus(props.documentId!)

      if (statusResponse.success) {
        const { isCompleted } = statusResponse.data

        if (isCompleted) {
          await loadResults()
          return
        }

        if (attempts >= maxAttempts) {
          throw new Error('分析超时，请稍后重试')
        }

        setTimeout(poll, 10000)
      } else {
        throw new Error('获取分析状态失败')
      }
    } catch (error: any) {
      console.error('轮询分析状态失败:', error)
      ElMessage.error('获取分析状态失败')
      loading.value = false
    }
  }

  setTimeout(poll, 2000)
}

const refreshResults = () => {
  loadResults()
}

const manualTriggerAnalysis = async () => {
  if (!props.documentId) return

  loading.value = true
  try {
    await triggerAnalysis()
  } catch (error) {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

const handleFilter = () => {
  // 过滤逻辑已在computed中处理
}

const handleTabChange = (tab: string) => {
  activeTab.value = tab as 'extract' | 'detect'
  selectedItem.value = null
}

const handleItemClick = (item: ExtractItem | DetectItem) => {
  const itemWithType = { ...item, type: activeTab.value }
  selectedItem.value = itemWithType
  emit('item-click', item)
  emit('item-select', item)
}

const showItemDetail = (item: ExtractItem | DetectItem) => {
  const itemWithType = { ...item, type: activeTab.value }
  selectedItem.value = itemWithType
  currentDetailSourceIndex.value = 0 // 重置详情页面的引用索引
  showDetail.value = true
}

// 处理原文引用点击事件
const handleSourceClick = (source: SourceReference, item: ExtractItem | DetectItem) => {
  console.log('Source clicked:', source, item)
  emit('source-click', source, item)
}

// 工具方法
const getExtractStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    success: 'success',
    warning: 'warning',
    error: 'danger',
    normal: 'info'
  }
  return typeMap[status.toLowerCase()] || 'info'
}

const getSeverityType = (severity: string) => {
  const typeMap: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'success'
  }
  return typeMap[severity.toLowerCase()] || 'info'
}

const getDetectResultColor = (result: string): string => {
  return getDetectItemResultColor(result)
}

const getSourceContentPreview = (content: string): string => {
  // 移除引用序号和位置信息，只显示实际内容
  const match = content.match(/^第.+处（.+）：(.+)$/)
  if (match) {
    return match[1].length > 100 ? match[1].substring(0, 100) + '...' : match[1]
  }
  return content.length > 100 ? content.substring(0, 100) + '...' : content
}

const getEmptyDescription = () => {
  if (searchText.value.trim()) {
    return '未找到匹配的结果'
  }

  if (!props.documentId) {
    return '请先上传文档'
  }

  const baseMessage = activeTab.value === 'extract' ? '暂无提取结果' : '暂无检测问题'
  return `${baseMessage}，点击下方按钮开始AI分析`
}

// 监听
watch(
  () => props.documentId,
  newId => {
    if (newId && props.autoLoad) {
      loadResults()
    }
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  if (props.documentId && props.autoLoad) {
    loadResults()
  }
})

// 暴露方法
defineExpose({
  loadResults,
  refreshResults,
  manualTriggerAnalysis,
  selectItem: (item: ExtractItem | DetectItem | null) => {
    selectedItem.value = item ? { ...item, type: activeTab.value } : null
  }
})
</script>

<style scoped>
.structured-analysis-results {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  overflow: hidden;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  margin-bottom: 16px;
}

.statistics-summary {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.stat-count {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.stat-count.error {
  color: #f56c6c;
}

.results-filters {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.results-tabs {
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.results-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading-container,
.empty-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.extract-results,
.detect-results {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.extract-item,
.detect-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e0e0e0;
}

.extract-item {
  border-left-color: #67c23a;
}

.detect-item {
  border-left-color: #e6a23c;
}

.detect-item.severity-high {
  border-left-color: #f56c6c;
}

.detect-item.severity-medium {
  border-left-color: #e6a23c;
}

.detect-item.severity-low {
  border-left-color: #67c23a;
}

.extract-item:hover,
.detect-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.extract-item.active,
.detect-item.active {
  border: 2px solid #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.item-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.item-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.item-content {
  margin-bottom: 8px;
}

.sources-summary {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #666;
  margin-bottom: 12px;
}

.detect-reason {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.sources-preview {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  overflow: hidden;
}

.source-item {
  padding: 12px;
  border-bottom: 1px solid #f8f8f8;
  transition: all 0.2s ease;
}

.source-item:last-child {
  border-bottom: none;
}

.source-item.clickable {
  cursor: pointer;
  border-radius: 4px;
}

.source-item.clickable:hover {
  background-color: #f0f9ff;
  border-color: #409eff;
  transform: translateX(2px);
}

.source-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  position: relative;
}

.source-jump-icon {
  margin-left: auto;
  color: #409eff;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.source-item.clickable:hover .source-jump-icon {
  opacity: 1;
}

.source-location {
  font-size: 12px;
  color: #666;
}

.source-content {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
}

.more-sources {
  padding: 8px 12px;
  background: #f8f9fa;
  text-align: center;
}

.detail-content {
  padding: 16px;
}

.detail-header {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 16px;
}

.importance {
  font-size: 12px;
  color: #666;
}

.sources-detail h5,
.detect-reason-detail h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.detect-reason-detail p {
  margin: 0 0 16px 0;
  line-height: 1.5;
  color: #666;
}

.source-detail-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
}

.source-detail-item.clickable {
  cursor: pointer;
}

.source-detail-item.clickable:hover {
  background: #e6f7ff;
  border: 1px solid #409eff;
  transform: translateX(2px);
}

.source-detail-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.source-detail-content {
  font-size: 13px;
  line-height: 1.4;
  color: #333;
  white-space: pre-wrap;
}

/* 多引用轮播样式 */
.sources-carousel {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  background: #fafafa;
}

.carousel-container {
  position: relative;
}

.current-source {
  background: white;
}

/* 轮播控制器样式 */
.carousel-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.source-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 13px;
  color: #666;
  min-width: 40px;
  justify-content: center;
  background: white;
  border-radius: 12px;
  padding: 4px 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.current-index {
  font-weight: 500;
  color: #409eff;
}

.separator {
  color: #999;
}

.total-count {
  color: #666;
}

.view-all-sources {
  padding: 8px 12px;
  background: #f8f9fa;
  text-align: center;
  border-top: 1px solid #e9ecef;
}

/* 详情页面轮播样式 */
.detail-sources-carousel {
  margin-top: 12px;
}

.detail-carousel-container {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.detail-current-source {
  background: white;
}

/* 详情页面控制器 */
.detail-carousel-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.detail-source-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
  background: white;
  border-radius: 8px;
  padding: 6px 12px;
  border: 1px solid #e9ecef;
}

.detail-source-indicator .current-index {
  color: #409eff;
  font-weight: 600;
}

/* 引用点指示器 */
.source-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.source-dot {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e9ecef;
  color: #666;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.source-dot:hover {
  background: #d1ecf1;
  color: #0c5460;
}

.source-dot.active {
  background: #409eff;
  color: white;
  transform: scale(1.1);
}

/* 动画效果 */
.current-source {
  animation: fadeIn 0.3s ease-in-out;
}

.detail-current-source {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statistics-summary {
    gap: 16px;
  }

  .results-filters {
    flex-direction: column;
    gap: 8px;
  }

  .extract-item,
  .detect-item {
    padding: 12px;
  }

  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-actions {
    align-self: flex-end;
  }

  .carousel-controls {
    padding: 6px 8px;
    gap: 8px;
  }

  .source-indicator {
    font-size: 12px;
    min-width: 35px;
  }

  .detail-carousel-controls {
    flex-direction: column;
    gap: 8px;
    padding: 8px 12px;
  }

  .detail-carousel-controls .el-button {
    width: 100%;
  }

  .source-dots {
    gap: 6px;
    padding: 8px;
  }

  .source-dot {
    width: 20px;
    height: 20px;
    font-size: 11px;
  }
}
</style>
