<template>
  <div class="document-upload">
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      drag
      :action="uploadUrl"
      :before-upload="beforeUpload"
      :on-change="onChange"
      :on-progress="onProgress"
      :on-success="onSuccess"
      :on-error="onError"
      :file-list="fileList"
      :limit="1"
      :accept="acceptTypes"
      :auto-upload="false"
      :show-file-list="true"
    >
      <el-icon class="el-icon--upload">
        <upload-filled />
      </el-icon>
      <div class="el-upload__text">
        将DOCX文件拖拽到此处，或<em>点击上传</em>
      </div>
      <template #tip>
        <div class="el-upload__tip">
          仅支持DOCX格式文件，文件大小不超过{{ maxFileSizeText }}
        </div>
      </template>
    </el-upload>

    <!-- 上传控制按钮 -->
    <div class="upload-actions" v-if="fileList.length > 0">
      <el-button 
        type="primary" 
        :loading="uploading"
        :disabled="!canUpload"
        @click="handleUpload"
      >
        {{ uploading ? '上传中...' : '开始上传' }}
      </el-button>
      <el-button @click="clearFiles" :disabled="uploading">
        清空文件
      </el-button>
    </div>

    <!-- 上传进度 -->
    <div class="upload-progress" v-if="uploading">
      <el-progress 
        :percentage="uploadProgress" 
        :status="progressStatus"
        :stroke-width="8"
      />
      <p class="progress-text">{{ progressText }}</p>
    </div>

    <!-- 上传结果 -->
    <div class="upload-result" v-if="uploadResult">
      <el-alert
        :title="uploadResult.title"
        :type="uploadResult.type"
        :description="uploadResult.description"
        :closable="false"
        show-icon
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { useDocumentStore } from '@/stores/document'
import request from '@/api/request'



// 组件引用
const uploadRef = ref<UploadInstance>()
const documentStore = useDocumentStore()

// 响应式数据
const fileList = ref<any[]>([])
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadResult = ref<any>(null)
const uploadConfig = ref<any>({})

// 上传配置
const uploadUrl = '/api/documents/upload'
const acceptTypes = '.docx'
const maxFileSize = 50 * 1024 * 1024 // 50MB
const maxFileSizeText = '50MB'

// 计算属性
const canUpload = computed(() => {
  return fileList.value.length > 0 && !uploading.value
})

const progressStatus = computed(() => {
  if (uploadProgress.value === 100) {
    return 'success'
  } else if (uploadResult.value?.type === 'error') {
    return 'exception'
  }
  return undefined
})

const progressText = computed(() => {
  if (uploading.value) {
    return `上传进度: ${uploadProgress.value}%`
  } else if (uploadResult.value?.type === 'success') {
    return '上传完成'
  } else if (uploadResult.value?.type === 'error') {
    return '上传失败'
  }
  return ''
})

// 生命周期
onMounted(async () => {
  await loadUploadConfig()
})

// 方法
const loadUploadConfig = async () => {
  try {
    const response = await request.get('/documents/upload-config')
    if (response.success) {
      uploadConfig.value = response.data
    }
  } catch (error) {
    console.error('加载上传配置失败:', error)
  }
}

const onChange: UploadProps['onChange'] = (file, uploadFileList) => {
  // 更新文件列表
  fileList.value = uploadFileList
  console.log('文件选择变化:', file, uploadFileList)
}

const beforeUpload: UploadProps['beforeUpload'] = (rawFile: UploadRawFile) => {
  // 检查文件类型
  if (!rawFile.name.toLowerCase().endsWith('.docx')) {
    ElMessage.error('只能上传DOCX格式的文件!')
    return false
  }

  // 检查文件大小
  if (rawFile.size > maxFileSize) {
    ElMessage.error(`文件大小不能超过 ${maxFileSizeText}!`)
    return false
  }

  // 检查MIME类型
  const allowedMimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  if (rawFile.type && rawFile.type !== allowedMimeType) {
    ElMessage.error('文件格式不正确，请选择有效的DOCX文件!')
    return false
  }

  // 重置状态
  uploadResult.value = null
  uploadProgress.value = 0

  return true
}

const onProgress: UploadProps['onProgress'] = (evt, file) => {
  uploading.value = true
  uploadProgress.value = Math.round(evt.percent || 0)
  documentStore.updateUploadProgress(uploadProgress.value)
}

const onSuccess: UploadProps['onSuccess'] = (response, file) => {
  uploading.value = false
  uploadProgress.value = 100
  
  if (response.success) {
    uploadResult.value = {
      type: 'success',
      title: '上传成功',
      description: `文件 "${file.name}" 上传成功，文档ID: ${response.data.id}`
    }
    
    // 更新文档状态
    documentStore.setCurrentDocument(response.data)
    documentStore.setParseStatus('idle')
    
    ElMessage.success('文档上传成功!')
    
    // 触发上传成功事件
    emit('upload-success', response.data)
  } else {
    onError(new Error(response.message), file)
  }
}

const onError: UploadProps['onError'] = (error, file) => {
  uploading.value = false
  uploadProgress.value = 0
  
  const errorMessage = error.message || '上传失败'
  uploadResult.value = {
    type: 'error',
    title: '上传失败',
    description: errorMessage
  }
  
  documentStore.setParseStatus('error')
  ElMessage.error(`文件上传失败: ${errorMessage}`)
  
  // 触发上传失败事件
  emit('upload-error', error)
}

const handleUpload = () => {
  if (!uploadRef.value) return
  
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件')
    return
  }
  
  uploadRef.value.submit()
}

const clearFiles = () => {
  if (uploading.value) {
    ElMessageBox.confirm('正在上传中，确定要取消吗？', '确认', {
      type: 'warning'
    }).then(() => {
      uploadRef.value?.abort()
      resetUpload()
    }).catch(() => {
      // 用户取消
    })
  } else {
    resetUpload()
  }
}

const resetUpload = () => {
  uploadRef.value?.clearFiles()
  fileList.value = []
  uploading.value = false
  uploadProgress.value = 0
  uploadResult.value = null
  documentStore.resetDocument()
}

// 事件定义
const emit = defineEmits<{
  'upload-success': [data: any]
  'upload-error': [error: Error]
}>()
</script>

<style scoped>
.document-upload {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.upload-demo {
  margin-bottom: 20px;
}

.upload-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.upload-progress {
  margin-bottom: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.upload-result {
  margin-top: 20px;
}

.el-upload__tip {
  color: #606266;
  font-size: 12px;
  line-height: 1.5;
}
</style>
