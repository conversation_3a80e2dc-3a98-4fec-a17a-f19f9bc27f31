<template>
  <div class="pdf-viewer-container legacy-canvas-viewer" :class="{ 'fallback-mode': isFallbackMode }">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <span>正在加载PDF文档...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-icon class="error-icon"><WarningFilled /></el-icon>
      <span>{{ error }}</span>
      <el-button @click="loadDocument" type="primary" size="small">重试</el-button>
    </div>

    <!-- PDF 预览区域 -->
    <div v-else class="pdf-viewer" ref="viewerRef">
      <!-- {{RIPER-5+SMART-6:
        Action: "Enhanced"
        Task_ID: "2f4aa764-2876-4a93-af73-7457cf767005"
        Timestamp: "2025-08-18T11:25:43+08:00"
        Authoring_Subagent: "frontend-vue-expert"
        Principle_Applied: "SOLID-S (单一职责原则)"
        Quality_Check: "增强工具栏功能，添加适应模式和全屏支持。"
      }} -->
      <!-- 工具栏 -->
      <div
        class="pdf-toolbar"
        :class="{
          'mobile': deviceType === 'mobile',
          'tablet': deviceType === 'tablet',
          'collapsed': shouldCollapseToolbar && toolbarCollapsed
        }"
      >
        <div class="toolbar-left">
          <!-- 页面导航 -->
          <el-button-group>
            <el-button @click="prevPage" :disabled="currentVisiblePage <= 1" size="small" title="上一页">
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <el-input
              v-model="pageInputValue"
              @change="goToPage"
              @keyup.enter="goToPage"
              @blur="syncPageInput"
              size="small"
              class="page-input"
              :min="1"
              :max="totalPages"
              placeholder="页码"
            />
            <span class="page-info">/ {{ totalPages }}</span>
            <el-button @click="nextPage" :disabled="currentVisiblePage >= totalPages" size="small" title="下一页">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </el-button-group>

          <!-- 缩放控制 -->
          <el-button-group class="ml-3">
            <el-button @click="zoomOut" :disabled="!canZoomOut" size="small" title="缩小">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="resetZoom" size="small" title="重置缩放">{{ Math.round(scale * 100) }}%</el-button>
            <el-button @click="zoomIn" :disabled="!canZoomIn" size="small" title="放大">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>

          <!-- 适应模式 -->
          <el-button-group class="ml-3">
            <el-button
              @click="fitToWidth"
              :type="fitMode === 'width' ? 'primary' : 'default'"
              size="small"
              title="适应宽度"
            >
              <el-icon><Monitor /></el-icon>
            </el-button>
            <el-button
              @click="fitToPage"
              :type="fitMode === 'page' ? 'primary' : 'default'"
              size="small"
              title="适应页面"
            >
              <el-icon><Document /></el-icon>
            </el-button>
            <el-button
              @click="autoFit"
              :type="fitMode === 'auto' ? 'primary' : 'default'"
              size="small"
              title="自动适应"
            >
              <el-icon><Aim /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <div class="toolbar-right">
          <!-- 侧边栏按钮 -->
          <el-button @click="toggleSidebar" size="small" :type="showSidebar ? 'primary' : 'default'" title="显示/隐藏侧边栏">
            <el-icon><Document /></el-icon>
            大纲
          </el-button>

          <!-- 搜索按钮 -->
          <el-button @click="toggleSearchBar" size="small" :type="showSearchBar ? 'primary' : 'default'" title="搜索PDF">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>

          <!-- 配置按钮 -->
          <el-button @click="toggleConfigPanel" size="small" :type="showConfigPanel ? 'primary' : 'default'" title="功能配置">
            <el-icon><Aim /></el-icon>
            配置
          </el-button>

          <!-- 全屏模式 -->
          <el-button @click="toggleFullscreen" size="small" :title="isFullscreen ? '退出全屏' : '全屏'">
            <el-icon><FullScreen /></el-icon>
            {{ isFullscreen ? '退出全屏' : '全屏' }}
          </el-button>

          <el-button @click="downloadPdf" size="small" title="下载PDF">
            <el-icon><Download /></el-icon>
            下载
          </el-button>

          <!-- RIPER-5模式文本层诊断和修复工具 -->
          <el-button @click="detectCSSConflicts" size="small" type="warning" title="检测CSS样式冲突">
            🔍 CSS冲突
          </el-button>
          <el-button @click="autoFixCSSConflicts" size="small" type="danger" title="基于检测结果自动修复CSS冲突">
            🚀 自动修复
          </el-button>
          <el-button @click="fixPreciseSizeConflicts" size="small" type="success" title="基于冲突数据的精确尺寸修复">
            🎯 精确尺寸修复
          </el-button>
          <el-button @click="visualizeDOMStructure" size="small" type="info" title="可视化DOM结构">
            🏗️ DOM结构
          </el-button>
          <el-button @click="forceFixCSSAlignment" size="small" type="danger" title="强制修复CSS对齐问题">
            💪 强制修复
          </el-button>
          <el-button @click="printPdf" size="small" title="打印PDF">
            <el-icon><Printer /></el-icon>
            打印
          </el-button>
        </div>
      </div>

      <!-- {{RIPER-5+SMART-6:
        Action: "Added"
        Task_ID: "fffe9572-35e9-4270-ae53-71f5ebdc44ae"
        Timestamp: "2025-08-18T11:25:43+08:00"
        Authoring_Subagent: "frontend-vue-expert"
        Principle_Applied: "SOLID-S (单一职责原则)"
        Quality_Check: "添加PDF搜索工具栏UI组件，参考DocumentNavigator.vue设计模式。"
      }} -->
      <!-- PDF搜索工具栏 -->
      <div
        class="pdf-search-toolbar"
        :class="{
          'mobile': deviceType === 'mobile',
          'tablet': deviceType === 'tablet'
        }"
        v-if="showSearchBar"
      >
        <div class="search-input-container">
          <el-input
            v-model="searchText"
            placeholder="在PDF中搜索..."
            :prefix-icon="Search"
            clearable
            @input="handleSearchInput"
            @keyup.enter="handleSearchEnter"
            @clear="clearSearchResults"
            @focus="() => console.log('🔍 搜索框获得焦点')"
            @blur="() => console.log('🔍 搜索框失去焦点')"
            size="small"
            class="search-input"
            ref="searchInputRef"
            :disabled="false"
            :readonly="false"
          />
        </div>

        <div v-if="searchResults.length > 0" class="search-info">
          <span class="search-count">{{ currentSearchIndex + 1 }} / {{ searchResults.length }}</span>
          <el-button-group size="small">
            <el-button
              :icon="ArrowUp"
              @click="prevSearchResult"
              :disabled="searchResults.length === 0"
              title="上一个结果"
            />
            <el-button
              :icon="ArrowDown"
              @click="nextSearchResult"
              :disabled="searchResults.length === 0"
              title="下一个结果"
            />
          </el-button-group>
        </div>

        <div v-else-if="searchText.trim() && searchResults.length === 0" class="search-no-results">
          <span>未找到结果</span>
        </div>

        <div class="search-actions">
          <el-button
            @click="toggleAdvancedSearch"
            size="small"
            :type="showAdvancedSearch ? 'primary' : 'default'"
            title="高级搜索"
          >
            高级
          </el-button>
          <el-button
            :icon="Close"
            @click="closeSearchBar"
            size="small"
            text
            title="关闭搜索"
          />
        </div>
      </div>

      <!-- 高级搜索面板 -->
      <div
        class="pdf-advanced-search-panel"
        v-if="showAdvancedSearch && showSearchBar"
      >
        <div class="advanced-search-content">
          <div class="search-options">
            <el-checkbox v-model="searchOptions.caseSensitive">大小写匹配</el-checkbox>
            <el-checkbox v-model="searchOptions.wholeWord">全词匹配</el-checkbox>
            <el-checkbox v-model="searchOptions.regex">正则表达式</el-checkbox>
          </div>

          <div class="search-scope">
            <label>搜索范围：</label>
            <el-radio-group v-model="searchOptions.searchScope" size="small">
              <el-radio label="all">全部页面</el-radio>
              <el-radio label="current">当前页面</el-radio>
              <el-radio label="range">指定范围</el-radio>
            </el-radio-group>
          </div>

          <div v-if="searchOptions.searchScope === 'range'" class="page-range">
            <el-input-number v-model="searchOptions.pageRange.start" :min="1" :max="totalPages" size="small" />
            <span>-</span>
            <el-input-number v-model="searchOptions.pageRange.end" :min="1" :max="totalPages" size="small" />
          </div>

          <div class="search-history" v-if="searchHistory.length > 0">
            <label>搜索历史：</label>
            <div class="history-items">
              <el-tag
                v-for="(item, index) in searchHistory.slice(0, 5)"
                :key="index"
                @click="searchFromHistory(item)"
                style="cursor: pointer; margin: 2px;"
                size="small"
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能配置面板 -->
      <div
        class="pdf-config-panel"
        v-if="showConfigPanel"
      >
        <div class="config-header">
          <h4>功能配置</h4>
          <el-button @click="showConfigPanel = false" size="small" text :icon="Close" />
        </div>

        <div class="config-content">
          <div class="config-section">
            <h5>基础功能</h5>
            <el-switch v-model="featureConfig.enableTextLayer" @change="updateFeatureConfig('enableTextLayer', $event)">
              <template #active-text>文本层</template>
            </el-switch>
            <el-switch v-model="featureConfig.enableAnnotations" @change="updateFeatureConfig('enableAnnotations', $event)">
              <template #active-text>注释层</template>
            </el-switch>
            <el-switch v-model="featureConfig.enableSearch" @change="updateFeatureConfig('enableSearch', $event)">
              <template #active-text>搜索功能</template>
            </el-switch>
            <el-switch v-model="featureConfig.enableKeyboardShortcuts" @change="updateFeatureConfig('enableKeyboardShortcuts', $event)">
              <template #active-text>键盘快捷键</template>
            </el-switch>
          </div>

          <div class="config-section">
            <h5>导航功能</h5>
            <el-switch v-model="featureConfig.enableOutline" @change="updateFeatureConfig('enableOutline', $event)">
              <template #active-text>文档大纲</template>
            </el-switch>
            <el-switch v-model="featureConfig.enableThumbnails" @change="updateFeatureConfig('enableThumbnails', $event)">
              <template #active-text>缩略图</template>
            </el-switch>
            <el-switch v-model="featureConfig.autoLoadOutline" @change="updateFeatureConfig('autoLoadOutline', $event)">
              <template #active-text>自动加载大纲</template>
            </el-switch>
            <el-switch v-model="featureConfig.autoGenerateThumbnails" @change="updateFeatureConfig('autoGenerateThumbnails', $event)">
              <template #active-text>自动生成缩略图</template>
            </el-switch>
          </div>

          <div class="config-section">
            <h5>外观设置</h5>
            <div class="config-item">
              <label>搜索高亮颜色：</label>
              <el-color-picker v-model="featureConfig.searchHighlightColor" @change="updateFeatureConfig('searchHighlightColor', $event)" />
            </div>
            <div class="config-item">
              <label>注释透明度：</label>
              <el-slider v-model="featureConfig.annotationOpacity" :min="0" :max="1" :step="0.1" @change="updateFeatureConfig('annotationOpacity', $event)" />
            </div>
          </div>

          <div class="config-section">
            <h5>开发调试</h5>
            <el-switch v-model="featureConfig.debugTextLayer" @change="updateFeatureConfig('debugTextLayer', $event)">
              <template #active-text>TextLayer调试模式</template>
            </el-switch>
            <div class="config-item">
              <label>TextLayer透明度：</label>
              <el-slider v-model="featureConfig.textLayerOpacity" :min="0" :max="0.5" :step="0.05" @change="updateFeatureConfig('textLayerOpacity', $event)" />
              <span class="opacity-value">{{ featureConfig.textLayerOpacity }}</span>
            </div>
          </div>

          <div class="config-actions">
            <el-button @click="resetFeatureConfig" size="small">重置配置</el-button>
            <el-button @click="optimizePerformance" size="small" type="primary">优化性能</el-button>
            <el-button @click="testTextLayerAlignment" size="small" type="success">测试文本层对齐</el-button>
            <el-button @click="() => diagnoseTextLayerAlignment('房建市政施工招标示范文本')" size="small" type="warning">诊断对齐问题</el-button>
          </div>
        </div>
      </div>

      <!-- {{RIPER-5+SMART-6:
        Action: "Added"
        Task_ID: "bb8c1cc5-efda-4000-b46c-ee5393c2741f"
        Timestamp: "2025-08-18T11:25:43+08:00"
        Authoring_Subagent: "pdfjs-navigation-expert"
        Principle_Applied: "SOLID-S (单一职责原则)"
        Quality_Check: "添加PDF文档大纲侧边栏UI组件，参考DocumentNavigator.vue设计模式。"
      }} -->
      <!-- PDF侧边栏 -->
      <div class="pdf-main-content" :class="{ 'with-sidebar': showSidebar }">
        <!-- 侧边栏 -->
        <div
          class="pdf-sidebar"
          :class="{
            'mobile': deviceType === 'mobile',
            'tablet': deviceType === 'tablet'
          }"
          v-if="showSidebar"
        >
          <!-- 侧边栏头部 -->
          <div class="sidebar-header">
            <el-tabs v-model="sidebarActiveTab" @tab-click="switchSidebarTab">
              <el-tab-pane label="大纲" name="outline">
                <template #label>
                  <el-icon><Document /></el-icon>
                  <span>大纲</span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="缩略图" name="thumbnails">
                <template #label>
                  <el-icon><Monitor /></el-icon>
                  <span>缩略图</span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="书签" name="bookmarks" disabled>
                <template #label>
                  <el-icon><Aim /></el-icon>
                  <span>书签</span>
                </template>
              </el-tab-pane>
            </el-tabs>

            <el-button
              :icon="Close"
              @click="closeSidebar"
              size="small"
              text
              title="关闭侧边栏"
              class="sidebar-close-btn"
            />
          </div>

          <!-- 侧边栏内容 -->
          <div class="sidebar-content">
            <!-- 大纲面板 -->
            <div v-if="sidebarActiveTab === 'outline'" class="outline-panel">
              <!-- 大纲搜索 -->
              <div class="outline-search" v-if="documentOutline.length > 0">
                <el-input
                  v-model="outlineSearchText"
                  placeholder="搜索大纲..."
                  :prefix-icon="Search"
                  clearable
                  @input="filterOutline"
                  size="small"
                />
              </div>

              <!-- 大纲树 -->
              <div class="outline-tree-container">
                <el-tree
                  v-if="filteredOutline.length > 0"
                  :data="filteredOutline"
                  :props="{ children: 'items', label: 'title' }"
                  :expand-on-click-node="false"
                  :default-expanded-keys="expandedOutlineKeys"
                  node-key="id"
                  @node-click="handleOutlineClick"
                  class="outline-tree"
                >
                  <template #default="{ node, data }">
                    <div
                      class="outline-node"
                      :class="{
                        'bold': data.bold,
                        'italic': data.italic,
                        [`level-${data.level}`]: true
                      }"
                      :style="{ color: data.color ? `rgb(${data.color.join(',')})` : undefined }"
                    >
                      <span class="outline-title">{{ data.title }}</span>
                    </div>
                  </template>
                </el-tree>

                <!-- 无大纲提示 -->
                <div v-else-if="documentOutline.length === 0" class="no-outline">
                  <el-empty description="此PDF文档没有大纲信息" :image-size="80" />
                </div>

                <!-- 搜索无结果提示 -->
                <div v-else class="no-search-results">
                  <el-empty description="未找到匹配的大纲项" :image-size="60" />
                </div>
              </div>
            </div>

            <!-- 缩略图面板 -->
            <div v-else-if="sidebarActiveTab === 'thumbnails'" class="thumbnails-panel">
              <!-- 缩略图工具栏 -->
              <div class="thumbnails-toolbar">
                <div class="thumbnails-info">
                  <span>共 {{ totalPages }} 页</span>
                </div>
                <div class="thumbnails-controls">
                  <el-button-group size="small">
                    <el-button
                      :type="thumbnailSize === 'small' ? 'primary' : 'default'"
                      @click="changeThumbnailSize('small')"
                      title="小尺寸"
                    >S</el-button>
                    <el-button
                      :type="thumbnailSize === 'medium' ? 'primary' : 'default'"
                      @click="changeThumbnailSize('medium')"
                      title="中尺寸"
                    >M</el-button>
                    <el-button
                      :type="thumbnailSize === 'large' ? 'primary' : 'default'"
                      @click="changeThumbnailSize('large')"
                      title="大尺寸"
                    >L</el-button>
                  </el-button-group>
                </div>
              </div>

              <!-- 缩略图网格 -->
              <el-scrollbar class="thumbnails-scrollbar">
                <div class="thumbnails-grid" :class="getThumbnailSizeClass()">
                  <div
                    v-for="pageNum in totalPages"
                    :key="pageNum"
                    class="thumbnail-item"
                    :class="{
                      'active': pageNum === currentVisiblePage,
                      'loading': thumbnailsLoading.has(pageNum)
                    }"
                    @click="handleThumbnailClick(pageNum)"
                  >
                    <!-- 缩略图图片 -->
                    <div class="thumbnail-image-container">
                      <img
                        v-if="thumbnails.has(pageNum)"
                        :src="thumbnails.get(pageNum)"
                        :alt="`第 ${pageNum} 页`"
                        class="thumbnail-image"
                        loading="lazy"
                      />
                      <div v-else-if="thumbnailsLoading.has(pageNum)" class="thumbnail-loading">
                        <el-icon class="is-loading"><Loading /></el-icon>
                      </div>
                      <div v-else class="thumbnail-placeholder">
                        <el-icon><Document /></el-icon>
                      </div>
                    </div>

                    <!-- 页码标签 -->
                    <div class="thumbnail-label">
                      {{ pageNum }}
                    </div>
                  </div>
                </div>
              </el-scrollbar>

              <!-- 无PDF提示 -->
              <div v-if="!pdfDocument" class="no-pdf">
                <el-empty description="请先加载PDF文档" :image-size="80" />
              </div>
            </div>

            <!-- 书签面板（占位） -->
            <div v-else-if="sidebarActiveTab === 'bookmarks'" class="bookmarks-panel">
              <el-empty description="书签功能即将推出" :image-size="80" />
            </div>
          </div>
        </div>

        <!-- PDF 内容区域 -->
        <div
          class="pdf-content"
          :class="{
            'mobile': deviceType === 'mobile',
            'tablet': deviceType === 'tablet',
            'gesture-active': isGestureActive
          }"
          ref="pdfContentRef"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        >
          <div class="pdf-pages-container" ref="pagesContainerRef">
            <!-- PDF 页面将在这里动态渲染 -->
          </div>

          <!-- 文本选择复制按钮 -->
          <div
            v-if="showCopyButton"
            class="copy-button-popup"
            :style="{
              position: 'fixed',
              left: copyButtonPosition.x + 'px',
              top: copyButtonPosition.y + 'px',
              zIndex: 9999
            }"
          >
            <el-button
              @click="copySelectedText"
              type="primary"
              size="small"
              :icon="DocumentCopy"
              title="复制选中文本"
            >
              复制
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* {{RIPER-5+SMART-6:
   Action: "Added"
   Task_ID: "enhanced-search-styles"
   Timestamp: "2025-08-18T15:37:10+08:00"
   Authoring_Subagent: "pdfjs-enhancement-expert"
   Principle_Applied: "用户体验优化"
   Quality_Check: "添加搜索高亮和文本选择的CSS样式。"
}} */

/* 搜索高亮样式 */
:deep(.search-highlight) {
  background-color: #ffff00 !important;
  color: #000 !important;
  padding: 1px 2px;
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

:deep(.search-highlight.active-search-result) {
  background-color: #ffa500 !important;
  color: #fff !important;
  box-shadow: 0 0 4px rgba(255, 165, 0, 0.5);
  animation: pulse-highlight 1s ease-in-out;
}

/* 文本选择高亮样式 */
:deep(.text-selection-highlight) {
  background-color: rgba(0, 123, 255, 0.6) !important;
  color: #fff !important;
  border-radius: 3px;
  padding: 2px 4px;
  box-shadow: 0 1px 3px rgba(0, 123, 255, 0.3);
  position: relative;
  z-index: 1000;
  transition: all 0.2s ease;
}

/* 复制按钮弹窗样式 */
.copy-button-popup {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 6px;
  padding: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 0.2s ease-out;
}

/* 文本层样式优化 */
:deep(.textLayer) {
  /* 确保文本选择功能正常工作 */
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

:deep(.textLayer span) {
  /* 改善文本选择的视觉反馈 */
  cursor: text;
}

/* 动画效果 */
@keyframes pulse-highlight {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 搜索工具栏样式优化 */
.pdf-search-toolbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-input-container {
  flex: 1;
  max-width: 300px;
}

.search-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 12px;
}

.search-count {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.search-no-results {
  color: #999;
  font-size: 12px;
  margin: 0 12px;
}

.search-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-search-toolbar {
    flex-wrap: wrap;
    padding: 8px;
  }

  .search-input-container {
    max-width: 200px;
  }

  .search-info {
    margin: 4px 8px;
  }

  .copy-button-popup {
    transform: scale(0.9);
  }
}
</style>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick, markRaw } from 'vue'
import { ElMessage } from 'element-plus'
import { WarningFilled, ZoomOut, ZoomIn, ArrowLeft, ArrowRight, Download, Printer, FullScreen, Aim, Monitor, Document, Search, ArrowUp, ArrowDown, Close, Loading, DocumentCopy } from '@element-plus/icons-vue'
import { getDocumentPdf } from '@/api/document'
import { useDocumentStore } from '@/stores/document'
// {{RIPER-5+SMART-6:
//   Action: "Import-Fix"
//   Task_ID: "PDFJS-IMPORT-SAFETY"
//   Timestamp: "2025-08-18T17:40:36+08:00"
//   Authoring_Subagent: "import-expert"
//   Principle_Applied: "安全导入与兼容性原则"
//   Quality_Check: "确保PDF.js正确导入和Worker配置。"
// }}

// {{RIPER-5+SMART-6:
//   Action: "Import-Refactor"
//   Task_ID: "PDFJS-CONFIG-REFACTOR"
//   Timestamp: "2025-08-18T17:40:36+08:00"
//   Authoring_Subagent: "refactor-expert"
//   Principle_Applied: "模块化配置与错误处理原则"
//   Quality_Check: "使用专门的配置模块，确保PDF.js正确初始化。"
// }}

// {{RIPER-5+SMART-6:
//   Action: "Critical-Fix"
//   Task_ID: "PDFJS-IMPORT-ERROR-FIX"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "import-fix-expert"
//   Principle_Applied: "正确导入PDF.js API原则"
//   Quality_Check: "修复PDF.js导入问题，确保getDocument函数可用。"
// }}

// {{RIPER-5+SMART-6:
//   Action: "Critical-Fix"
//   Task_ID: "PDFJS-COMPATIBILITY-FIX"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "compatibility-expert"
//   Principle_Applied: "兼容性优先原则"
//   Quality_Check: "使用兼容的PDF.js导入方式，确保getDocument函数可用。"
// }}

// {{RIPER-5+SMART-6:
//   Action: "Critical-Fix"
//   Task_ID: "PDFJS-IMPORT-EMERGENCY-FIX"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "emergency-fix-expert"
//   Principle_Applied: "紧急修复原则"
//   Quality_Check: "使用动态导入解决pdfjs-dist导入问题。"
// }}

// 使用动态导入来解决pdfjs-dist导入问题
let pdfjsLib: any = null

// 动态加载PDF.js
const loadPdfJs = async () => {
  try {
    console.log('🔄 开始动态加载PDF.js...')

    // 方式1：尝试正确的ES模块导入
    try {
      const pdfjs = await import('pdfjs-dist')
      console.log('✅ ES模块导入成功:', pdfjs)
      
      // 检查不同的导出方式
      if (pdfjs.getDocument) {
        console.log('✅ 找到直接导出的getDocument')
        return pdfjs
      } else if (pdfjs.default && pdfjs.default.getDocument) {
        console.log('✅ 找到默认导出的getDocument')
        return pdfjs.default
      } else if (pdfjs.pdfjsLib && pdfjs.pdfjsLib.getDocument) {
        console.log('✅ 找到pdfjsLib导出的getDocument')
        return pdfjs.pdfjsLib
      } else {
        console.warn('⚠️ ES模块导入成功但未找到getDocument函数:', Object.keys(pdfjs))
        throw new Error('getDocument函数未找到')
      }
    } catch (esError) {
      console.warn('⚠️ ES模块导入失败:', esError)
    }

    // 方式2：尝试Worker导入方式
    try {
      const pdfjs = await import('pdfjs-dist/build/pdf.min.js')
      console.log('✅ Worker导入成功:', pdfjs)
      
      if (pdfjs.getDocument) {
        return pdfjs
      } else if (pdfjs.default && pdfjs.default.getDocument) {
        return pdfjs.default
      } else {
        throw new Error('Worker导入未找到getDocument')
      }
    } catch (workerError) {
      console.warn('⚠️ Worker导入失败:', workerError)
    }

    // 方式3：尝试ES5兼容导入
    try {
      const pdfjs = await import('pdfjs-dist/legacy/build/pdf.js')
      console.log('✅ ES5导入成功:', pdfjs)
      
      if (pdfjs.getDocument) {
        return pdfjs
      } else if (pdfjs.default && pdfjs.default.getDocument) {
        return pdfjs.default
      } else {
        throw new Error('ES5导入未找到getDocument')
      }
    } catch (legacyError) {
      console.warn('⚠️ ES5导入失败:', legacyError)
    }

    // 方式4：使用CDN作为最后的降级方案
    console.log('🌐 尝试从CDN加载PDF.js...')
    const script = document.createElement('script')
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
    document.head.appendChild(script)

    return new Promise((resolve, reject) => {
      script.onload = () => {
        const pdfjs = (window as any).pdfjsLib
        if (pdfjs && pdfjs.getDocument) {
          console.log('✅ CDN加载成功:', pdfjs)
          resolve(pdfjs)
        } else {
          reject(new Error('CDN加载失败或getDocument不可用'))
        }
      }
      script.onerror = () => reject(new Error('CDN脚本加载失败'))
      
      // 10秒超时
      setTimeout(() => reject(new Error('CDN加载超时')), 10000)
    })

  } catch (error) {
    console.error('❌ 所有PDF.js加载方式都失败:', error)
    throw new Error(`PDF.js加载失败: ${error.message}`)
  }
}
import type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist'
import { downloadPdf, PdfError, processBlobToPdfjs, safePdfData, pdfPageCache, calculateVisibleRange, throttle, debounce, getMemoryUsage, pdfToImage } from '@/utils/pdf-utils'
import type { PdfTextLayerData, PdfSearchMatch, PdfSearchOptions, PdfOutlineItem, PdfSidebarConfig, PdfThumbnailData, PdfThumbnailConfig, PdfAnnotationData, PdfAdvancedSearchOptions, PdfFeatureConfig, PdfSearchHistoryItem } from '@/types/pdf'

// Props
interface Props {
  documentId?: number
  autoLoad?: boolean
  fileUrl?: string
  fileData?: ArrayBuffer | Uint8Array
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

// Store
const documentStore = useDocumentStore()

// Refs
const viewerRef = ref<HTMLElement>()
const pdfContentRef = ref<HTMLElement>()
const pagesContainerRef = ref<HTMLElement>()
const searchInputRef = ref<any>()

// State
const loading = ref(false)
const error = ref('')
const pdfDocument = ref<any>(null)
const currentPage = ref(1)
const totalPages = ref(0)
const scale = ref(1.2)
const documentContent = ref<any>(null)
const isRendering = ref(false) // 渲染状态锁
const currentVisiblePage = ref(1) // 当前可见页码
const isFullscreen = ref(false) // 全屏状态
const fitMode = ref<'width' | 'page' | 'auto' | 'custom'>('auto') // 适应模式
const pageInputValue = ref(1) // 页码输入框的值

// {{RIPER-5+SMART-6:
//   Action: "Fallback-Mode-Support"
//   Task_ID: "CANVAS-FALLBACK-FLAG"
//   Timestamp: "2025-08-18T17:40:36+08:00"
//   Authoring_Subagent: "vue-compatibility-expert"
//   Principle_Applied: "向后兼容标识原则"
//   Quality_Check: "添加fallback模式标识，支持混合架构。"
// }}
const isFallbackMode = ref(false) // 是否为备用方案模式

// {{RIPER-5+SMART-6:
//   Action: "Performance-Optimization"
//   Task_ID: "PDF-PERFORMANCE-BOOST"
//   Timestamp: "2025-08-18T17:17:35+08:00"
//   Authoring_Subagent: "performance-expert"
//   Principle_Applied: "虚拟滚动 + 智能缓存原则"
//   Quality_Check: "大幅提升大文档渲染性能，减少内存占用。"
// }}

// 性能优化相关状态
const renderedPages = ref(new Set<number>()) // 已渲染的页面集合
const visibleRange = ref({ start: 1, end: 3 }) // 当前可视范围
const pageHeight = ref(800) // 单页高度（估算值）
const isLazyLoadingEnabled = ref(true) // 是否启用懒加载
const renderQueue = ref<number[]>([]) // 渲染队列
const isRenderingPage = ref(false) // 是否正在渲染页面

// 高级性能优化状态
const virtualScrollEnabled = ref(true) // 虚拟滚动开关
const maxConcurrentRenders = ref(2) // 最大并发渲染数
const renderingTasks = ref(new Map<number, Promise<any>>()) // 渲染任务追踪
const memoryThreshold = ref(500 * 1024 * 1024) // 500MB内存阈值
const performanceMetrics = ref({
  renderTime: 0,
  memoryUsage: 0,
  cacheHitRate: 0
})

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "1634860b-b1c8-4165-b0e1-854673b67b64"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "frontend-vue-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加移动端检测和触摸手势支持。"
// }}

// 移动端和触摸相关状态
const isMobile = ref(false) // 是否为移动设备
const toolbarCollapsed = ref(false) // 工具栏是否折叠
const touchStartData = ref<{
  x: number
  y: number
  scale: number
  distance?: number
  timestamp: number
} | null>(null) // 触摸开始数据
const isGestureActive = ref(false) // 是否正在进行手势操作
const lastTouchTime = ref(0) // 上次触摸时间（用于双击检测）

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "02a7b0ba-4810-4bfb-9184-ef3e3aa57966"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-integration-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF文本搜索相关状态管理。"
// }}

// PDF文本搜索相关状态
const searchText = ref('') // 搜索关键词
const searchResults = ref<PdfSearchMatch[]>([]) // 搜索结果数组
const currentSearchIndex = ref(-1) // 当前搜索结果索引
const textLayers = ref(new Map<number, PdfTextLayerData>()) // 存储每页的文本层数据
const searchCache = ref(new Map<string, PdfSearchMatch[]>()) // 搜索结果缓存
const searchOptions = ref<PdfAdvancedSearchOptions>({
  caseSensitive: false,
  wholeWord: false,
  regex: false,
  highlightAll: true,
  useRegex: false,
  searchInAnnotations: false,
  searchInOutline: false,
  maxResults: 1000,
  searchScope: 'all',
  pageRange: { start: 1, end: 1 }
}) // 搜索选项
const showSearchBar = ref(false) // 搜索工具栏显示状态

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "bb8c1cc5-efda-4000-b46c-ee5393c2741f"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-navigation-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF文档大纲导航相关状态管理。"
// }}

// PDF文档大纲导航相关状态
const documentOutline = ref<any[]>([]) // 文档大纲数据
const showSidebar = ref(false) // 侧边栏显示状态
const sidebarActiveTab = ref<'outline' | 'thumbnails' | 'bookmarks'>('outline') // 侧边栏活动标签
const outlineSearchText = ref('') // 大纲搜索文本
const filteredOutline = ref<any[]>([]) // 过滤后的大纲数据
const expandedOutlineKeys = ref<string[]>([]) // 展开的大纲节点

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "2f86cbd6-164f-43b9-aceb-06c4b6c3f149"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "thumbnail-navigation-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF缩略图预览相关状态管理。"
// }}

// PDF缩略图相关状态
const thumbnails = ref(new Map<number, string>()) // 缩略图缓存 (页码 -> Base64图片)
const thumbnailsLoading = ref(new Set<number>()) // 正在加载的缩略图页码
const thumbnailScale = ref(0.2) // 缩略图缩放比例
const thumbnailsGenerated = ref(false) // 是否已生成缩略图
const thumbnailSize = ref<'small' | 'medium' | 'large'>('medium') // 缩略图尺寸

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "0067320b-4e1e-4f48-a5fe-5da5e8ee59dc"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "advanced-features-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF注释层和高级功能相关状态管理。"
// }}

// PDF注释层相关状态
const annotationLayers = ref(new Map<number, HTMLElement>()) // 注释层缓存
const showAnnotations = ref(true) // 是否显示注释层
const annotationsLoading = ref(new Set<number>()) // 正在加载注释的页码

// 高级搜索功能状态
const showAdvancedSearch = ref(false) // 显示高级搜索面板
const searchHistory = ref<string[]>([]) // 搜索历史记录
const maxSearchHistory = ref(10) // 最大搜索历史数量

// 功能配置状态
const showConfigPanel = ref(false) // 显示配置面板
const featureConfig = ref({
  enableTextLayer: true,
  enableAnnotations: true,
  enableOutline: true,
  enableThumbnails: true,
  enableSearch: true,
  enableKeyboardShortcuts: true,
  autoLoadOutline: true,
  autoGenerateThumbnails: false,
  searchHighlightColor: '#ffff00',
  annotationOpacity: 1.0,
  // {{RIPER-5+SMART-6:
  //   Action: "Added"
  //   Task_ID: "8b547955-3ed3-4089-bfc4-521aafc77bfb"
  //   Timestamp: "2025-08-18T16:54:22+08:00"
  //   Authoring_Subagent: "pdf-textlayer-expert"
  //   Principle_Applied: "SOLID-O (开放封闭原则)"
  //   Quality_Check: "扩展现有配置系统，添加TextLayer调试支持。"
  // }}
  debugTextLayer: false, // 调试模式：显示TextLayer边界和内容
  textLayerOpacity: 0.1  // TextLayer透明度：0.1正常，0.3调试
}) // 功能配置

// Computed
const documentId = computed(() => props.documentId || documentStore.currentDocument?.id)

// TextLayer动态透明度计算
const dynamicTextLayerOpacity = computed(() => {
  if (featureConfig.value.debugTextLayer) {
    return 0.3 // 调试模式：更明显的透明度
  }
  return featureConfig.value.textLayerOpacity // 正常模式：配置的透明度
})

// 工具栏状态计算属性
const canZoomIn = computed(() => scale.value < 3)
const canZoomOut = computed(() => scale.value > 0.5)
const canGoToPrevious = computed(() => currentVisiblePage.value > 1)
const canGoToNext = computed(() => currentVisiblePage.value < totalPages.value)

// 移动端检测计算属性
const deviceType = computed(() => {
  if (typeof window === 'undefined') return 'desktop'

  const userAgent = navigator.userAgent.toLowerCase()
  const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
  const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent)
  const isSmallScreen = window.innerWidth <= 768

  if (isMobileDevice && !isTablet) return 'mobile'
  if (isTablet || (isSmallScreen && !isMobileDevice)) return 'tablet'
  return 'desktop'
})

// 响应式工具栏状态
const shouldCollapseToolbar = computed(() => {
  return deviceType.value === 'mobile' && window.innerWidth <= 480
})

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "PDFJS-DIRECT-CONFIG"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "config-expert"
//   Principle_Applied: "直接配置原则"
//   Quality_Check: "在组件中直接配置PDF.js，避免外部配置问题。"
// }}

// 验证PDF.js配置
const verifyPdfJsConfig = async () => {
  try {
    console.log('🔍 开始验证PDF.js配置...')

    // 如果pdfjsLib还没有加载，先加载它
    if (!pdfjsLib) {
      console.log('📦 PDF.js未加载，开始动态加载...')
      pdfjsLib = await loadPdfJs()

      // 配置Worker
      if (pdfjsLib.GlobalWorkerOptions) {
        const workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version || '3.11.174'}/pdf.worker.min.js`
        pdfjsLib.GlobalWorkerOptions.workerSrc = workerSrc
        console.log('✅ PDF.js Worker已配置:', workerSrc)
      }
    }

    console.log('- pdfjsLib存在:', !!pdfjsLib)
    console.log('- pdfjsLib类型:', typeof pdfjsLib)
    console.log('- pdfjsLib内容:', Object.keys(pdfjsLib || {}))
    console.log('- getDocument函数:', typeof pdfjsLib?.getDocument)
    console.log('- GlobalWorkerOptions:', !!pdfjsLib?.GlobalWorkerOptions)
    console.log('- Worker路径:', pdfjsLib?.GlobalWorkerOptions?.workerSrc)
    console.log('- PDF.js版本:', pdfjsLib?.version)

    if (!pdfjsLib) {
      throw new Error('PDF.js库加载失败')
    }

    // 检查getDocument函数
    if (typeof pdfjsLib.getDocument !== 'function') {
      console.error('❌ getDocument不是函数，实际类型:', typeof pdfjsLib.getDocument)
      console.error('❌ pdfjsLib完整内容:', pdfjsLib)
      throw new Error('PDF.js getDocument函数不可用')
    }

    console.log('✅ PDF.js配置验证通过')
    return true
  } catch (error) {
    console.error('❌ PDF.js配置验证失败:', error)
    return false
  }
}

// Methods
const loadDocument = async () => {
  try {
    loading.value = true
    error.value = ''

    // 验证PDF.js配置（异步）
    const isConfigValid = await verifyPdfJsConfig()
    if (!isConfigValid) {
      error.value = 'PDF.js配置错误，请刷新页面重试'
      loading.value = false
      return
    }

    let pdfData: any = null

    if (documentId.value) {
      console.log('开始加载文档，ID:', documentId.value)

      // 调用后台接口获取 PDF Blob 文件流
      const response = await getDocumentPdf(documentId.value)

      console.log('PDF 接口响应完成')
      console.log('响应类型:', typeof response)
      console.log('是否为 Blob:', response instanceof Blob)

      if (response instanceof Blob) {
        console.log('Blob 详情:')
        console.log('- 大小:', response.size, 'bytes')
        console.log('- 类型:', response.type)
      }

      if (response) {
        // 使用工具函数处理 Blob 响应
        let arrayBuffer: ArrayBuffer

        if (response instanceof Blob) {
          arrayBuffer = await response.arrayBuffer()
          console.log('Blob 转换为 ArrayBuffer 成功，大小:', arrayBuffer.byteLength)
        } else {
          // 如果已经是 ArrayBuffer
          arrayBuffer = response
        }

        // 存储数据用于下载
        documentContent.value = arrayBuffer

        // 使用 PDF.js 加载
        await loadPdfFromData(arrayBuffer)
      } else {
        throw new Error('获取 PDF 数据失败')
      }
    } else {
      throw new Error('没有提供有效的PDF数据源')
    }
  } catch (err: any) {
    console.error('❌ 加载PDF失败:', err)

    // 提供更详细的错误信息
    let errorMessage = '加载PDF文档失败'
    if (err.message) {
      if (err.message.includes('getDocument')) {
        errorMessage = 'PDF.js API错误，请刷新页面重试'
      } else if (err.message.includes('Invalid PDF')) {
        errorMessage = 'PDF文件格式无效或已损坏'
      } else if (err.message.includes('network')) {
        errorMessage = '网络错误，请检查网络连接'
      } else {
        errorMessage = `PDF加载错误: ${err.message}`
      }
    }

    error.value = errorMessage
    loading.value = false
    ElMessage.error(errorMessage)
  }
}

// 从数据加载PDF（保持向后兼容）
const loadPdfFromData = async (data: any) => {
  try {
    console.log('loadPdfFromData 开始，数据类型:', typeof data)
    console.log('数据大小:', data instanceof ArrayBuffer ? data.byteLength : 'unknown')

    // 使用工具函数处理PDF响应数据
    const processedData = data
    // 存储原始数据用于下载
    documentContent.value = data

    // {{RIPER-5+SMART-6:
    //   Action: "Critical-Fix"
    //   Task_ID: "PDFJS-GETDOCUMENT-FIX"
    //   Timestamp: "2025-08-19T09:27:00+08:00"
    //   Authoring_Subagent: "pdfjs-api-expert"
    //   Principle_Applied: "正确使用PDF.js API原则"
    //   Quality_Check: "修复getDocument调用，确保PDF文档正确加载。"
    // }}

    // 验证PDF.js API可用性
    if (!pdfjsLib || typeof pdfjsLib.getDocument !== 'function') {
      throw new Error('PDF.js API不可用，getDocument函数未找到')
    }

    // 创建PDF加载任务
    console.log('📄 开始创建PDF加载任务，数据大小:', data.byteLength)
    const loadingTask = pdfjsLib.getDocument({
      data: data,
      verbosity: 0 // 减少日志输出
    })

    console.log('PDF.js getDocument 任务创建成功')

    // 加载PDF文档
    console.log('开始加载 PDF 文档...')
    const pdfDoc = await loadingTask.promise

    // 设置PDF文档 - 使用 markRaw 避免 Vue 3 响应式包装导致的私有字段访问问题
    pdfDocument.value = markRaw(pdfDoc)
    totalPages.value = pdfDoc.numPages
    currentPage.value = 1

    console.log(`PDF加载成功，共 ${totalPages.value} 页`)
    console.log('pdfDocument.value 设置完成 (markRaw):', pdfDocument.value)

    // 加载文档大纲
    await loadDocumentOutline()

    // 结束加载状态，让 DOM 元素可用
    loading.value = false

    // 等待 DOM 更新后再渲染
    await nextTick()
    console.log('DOM 更新完成，开始渲染页面')

    // 渲染所有页面
    console.log('开始渲染所有页面，当前状态:', {
      pdfDocument: !!pdfDocument.value,
      totalPages: totalPages.value,
      pagesContainer: !!pagesContainerRef.value,
      isLazyLoading: isLazyLoadingEnabled.value
    })
    await renderAllPages()

    // 验证渲染结果
    if (pagesContainerRef.value) {
      const renderedElements = pagesContainerRef.value.children.length
      console.log(`渲染完成，容器中有 ${renderedElements} 个元素`)

      // 如果没有渲染任何内容，尝试强制渲染第一页
      if (renderedElements === 0) {
        console.warn('没有渲染任何页面，尝试强制渲染第一页')
        console.log('当前状态:', {
          isLazyLoading: isLazyLoadingEnabled.value,
          pdfDocument: !!pdfDocument.value,
          totalPages: totalPages.value,
          containerExists: !!pagesContainerRef.value
        })

        try {
          // 强制使用传统模式渲染第一页
          isLazyLoadingEnabled.value = false
          const firstPageCanvas = await renderSinglePage(1)
          pagesContainerRef.value.appendChild(firstPageCanvas)
          console.log('强制渲染第一页成功')
        } catch (forceRenderError) {
          console.error('强制渲染第一页失败:', forceRenderError)
        }
      }
    }
  } catch (error) {
    console.error('PDF加载失败:', error)

    if (error instanceof PdfError) {
      throw error
    } else {
      throw new PdfError('PDF文档加载失败，请检查文件格式', 'LOAD_FAILED')
    }
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Optimized"
//   Task_ID: "254b5570-805b-4fd0-8a5f-0b4ce6872759"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-integration-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "重构渲染逻辑，实现懒加载和虚拟滚动优化。"
// }}

// 初始化页面容器（懒加载模式）
const initializePageContainers = async () => {
  if (!pdfDocument.value || !pagesContainerRef.value) {
    console.warn('PDF 文档或容器未准备好')
    return
  }

  // 清空容器
  pagesContainerRef.value.innerHTML = ''
  renderedPages.value.clear()

  // 获取第一页来计算页面尺寸
  const firstPage = await pdfDocument.value.getPage(1)
  const viewport = firstPage.getViewport({ scale: scale.value })
  pageHeight.value = viewport.height + 20 // 添加间距

  console.log('页面容器初始化完成，页面高度:', pageHeight.value)

  // 创建所有页面的占位符
  for (let pageNum = 1; pageNum <= totalPages.value; pageNum++) {
    const pageContainer = document.createElement('div')
    pageContainer.className = 'pdf-page-container'
    pageContainer.style.height = `${pageHeight.value}px`
    pageContainer.style.marginBottom = '20px'
    pageContainer.style.display = 'flex'
    pageContainer.style.justifyContent = 'center'
    pageContainer.style.alignItems = 'center'
    pageContainer.style.backgroundColor = '#fff'
    pageContainer.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)'
    pageContainer.dataset.pageNumber = pageNum.toString()

    // 添加加载占位符
    const placeholder = document.createElement('div')
    placeholder.className = 'page-placeholder'
    placeholder.style.color = '#999'
    placeholder.style.fontSize = '14px'
    placeholder.textContent = `第 ${pageNum} 页`
    pageContainer.appendChild(placeholder)

    pagesContainerRef.value.appendChild(pageContainer)
  }

  // 初始渲染可视区域的页面
  await renderVisiblePages()

  // 确保至少渲染第一页
  if (!renderedPages.value.has(1)) {
    console.log('强制渲染第一页')
    await renderSinglePageLazy(1)
  }
}

// 渲染可视区域的页面（懒加载核心）
const renderVisiblePages = async () => {
  if (!pdfDocument.value || isRenderingPage.value) {
    return
  }

  // 如果容器还没准备好，使用默认范围
  let newVisibleRange = { start: 1, end: Math.min(3, totalPages.value) }

  if (pdfContentRef.value && pageHeight.value > 0) {
    // 计算当前可视范围
    newVisibleRange = calculateVisibleRange(
      pdfContentRef.value,
      pageHeight.value,
      totalPages.value,
      2 // 缓冲区：前后2页
    )
  }

  // 如果可视范围没有变化，跳过渲染（但初始化时强制渲染）
  const isInitialRender = renderedPages.value.size === 0
  if (!isInitialRender &&
      newVisibleRange.start === visibleRange.value.start &&
      newVisibleRange.end === visibleRange.value.end) {
    return
  }

  visibleRange.value = newVisibleRange
  console.log('可视范围更新:', visibleRange.value)

  // 渲染可视范围内的页面
  for (let pageNum = visibleRange.value.start; pageNum <= visibleRange.value.end; pageNum++) {
    if (!renderedPages.value.has(pageNum)) {
      await renderSinglePageLazy(pageNum)
    }
  }

  // 清理超出范围的页面（保留一定缓冲区）
  const cleanupBuffer = 5
  for (const pageNum of renderedPages.value) {
    if (pageNum < visibleRange.value.start - cleanupBuffer ||
        pageNum > visibleRange.value.end + cleanupBuffer) {
      cleanupPage(pageNum)
    }
  }
}

// 智能渲染队列管理
const renderSinglePageLazy = async (pageNum: number) => {
  if (renderedPages.value.has(pageNum)) {
    return
  }

  // 检查是否已在渲染队列中
  if (renderingTasks.value.has(pageNum)) {
    return renderingTasks.value.get(pageNum)
  }

  // 检查并发渲染限制
  if (renderingTasks.value.size >= maxConcurrentRenders.value) {
    console.log(`渲染队列已满，等待第 ${pageNum} 页`)
    return
  }

  const renderTask = performPageRender(pageNum)
  renderingTasks.value.set(pageNum, renderTask)

  try {
    await renderTask
  } finally {
    renderingTasks.value.delete(pageNum)
  }
}

// 执行页面渲染的核心逻辑
const performPageRender = async (pageNum: number) => {
  const startTime = performance.now()

  try {
    // 检查缓存
    const cachedCanvas = pdfPageCache.get(pageNum, scale.value)
    if (cachedCanvas) {
      console.log(`✅ 缓存命中第 ${pageNum} 页`)
      insertCachedPage(pageNum, cachedCanvas)
      renderedPages.value.add(pageNum)
      performanceMetrics.value.cacheHitRate++
      return
    }

    console.log(`🔄 开始渲染第 ${pageNum} 页`)

    // 内存检查
    const memoryUsage = getMemoryUsage()
    if (memoryUsage > memoryThreshold.value) {
      console.warn(`⚠️ 内存使用过高: ${memoryUsage / 1024 / 1024}MB，清理缓存`)
      await cleanupMemory()
    }

    const canvas = await renderSinglePage(pageNum)

    // 缓存渲染结果
    pdfPageCache.set(pageNum, scale.value, canvas)

    // 插入到页面容器
    insertRenderedPage(pageNum, canvas)
    renderedPages.value.add(pageNum)

    const renderTime = performance.now() - startTime
    performanceMetrics.value.renderTime = renderTime
    console.log(`✅ 第 ${pageNum} 页渲染完成，耗时: ${renderTime.toFixed(2)}ms`)

  } catch (error) {
    console.error(`❌ 第 ${pageNum} 页渲染失败:`, error)
    throw error
  }
}

// 插入渲染好的页面
const insertRenderedPage = (pageNum: number, canvas: HTMLCanvasElement) => {
  const pageContainer = pagesContainerRef.value?.querySelector(`[data-page-number="${pageNum}"]`)
  if (pageContainer) {
    pageContainer.innerHTML = ''
    pageContainer.appendChild(canvas)
  }
}

// 插入缓存的页面
const insertCachedPage = (pageNum: number, canvas: HTMLCanvasElement) => {
  const pageContainer = pagesContainerRef.value?.querySelector(`[data-page-number="${pageNum}"]`)
  if (pageContainer) {
    pageContainer.innerHTML = ''
    pageContainer.appendChild(canvas)
  }
}

// 清理页面
const cleanupPage = (pageNum: number) => {
  const pageContainer = pagesContainerRef.value?.querySelector(`[data-page-number="${pageNum}"]`)
  if (pageContainer) {
    pageContainer.innerHTML = `
      <div class="page-placeholder" style="color: #999; font-size: 14px;">
        第 ${pageNum} 页
      </div>
    `
    renderedPages.value.delete(pageNum)
    console.log(`清理第 ${pageNum} 页`)
  }
}

// 兼容性方法：渲染所有页面（用于非懒加载模式）
const renderAllPages = async () => {
  try {
    if (isLazyLoadingEnabled.value) {
      await initializePageContainers()

      // 验证懒加载是否成功渲染了内容
      await nextTick()
      const renderedElements = pagesContainerRef.value?.children.length || 0
      const hasActualContent = Array.from(pagesContainerRef.value?.children || [])
        .some(child => child.querySelector('canvas'))

      if (renderedElements === 0 || !hasActualContent) {
        console.warn('懒加载模式未渲染任何内容，切换到传统模式')
        throw new Error('懒加载渲染失败')
      }
    } else {
      await renderAllPagesTraditional()
    }
  } catch (error) {
    console.error('懒加载渲染失败，切换到传统模式:', error)
    isLazyLoadingEnabled.value = false
    await renderAllPagesTraditional()
  }
}

// 传统渲染模式（保留用于兼容）
const renderAllPagesTraditional = async () => {
  if (!pdfDocument.value) {
    console.warn('PDF 文档未加载')
    return
  }

  if (isRendering.value) {
    console.log('渲染正在进行中，跳过重复调用')
    return
  }

  isRendering.value = true
  console.log('开始传统模式渲染所有页面')

  try {
    if (pagesContainerRef.value) {
      pagesContainerRef.value.innerHTML = ''
    }

    for (let pageNum = 1; pageNum <= totalPages.value; pageNum++) {
      try {
        const canvas = await renderSinglePage(pageNum)
        if (pagesContainerRef.value) {
          pagesContainerRef.value.appendChild(canvas)
        }
      } catch (pageError) {
        console.error(`第 ${pageNum} 页渲染失败:`, pageError)
        continue
      }
    }

    console.log('传统模式渲染完成')
  } catch (error) {
    console.error('传统模式渲染失败:', error)
    ElMessage.error(`渲染页面失败: ${error.message}`)
  } finally {
    isRendering.value = false
  }
}

// 渲染单个页面的辅助函数（修改为返回页面容器）
const renderSinglePage = async (pageNum: number) => {
  // 获取页面对象
  const page = await pdfDocument.value.getPage(pageNum)

  const viewport = page.getViewport({ scale: scale.value })

  console.log(`第 ${pageNum} 页 Viewport 信息:`, {
    width: viewport.width,
    height: viewport.height,
    scale: scale.value,
    rotation: viewport.rotation
  })

  // 创建页面容器
  const pageContainer = document.createElement('div')
  pageContainer.className = 'pdf-page-container'
  pageContainer.style.position = 'relative'
  pageContainer.style.marginBottom = '20px'
  pageContainer.style.display = 'flex'
  pageContainer.style.justifyContent = 'center'
  pageContainer.style.alignItems = 'center'
  pageContainer.style.backgroundColor = '#fff'
  pageContainer.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)'
  pageContainer.dataset.pageNumber = pageNum.toString()

  // 创建canvas
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')

  if (!context) {
    throw new Error('无法获取Canvas上下文')
  }

  console.log(`第 ${pageNum} 页 Canvas 上下文创建成功:`, {
    contextType: context.constructor.name,
    hasDrawImage: typeof context.drawImage === 'function'
  })

  canvas.height = viewport.height
  canvas.width = viewport.width
  canvas.className = 'pdf-page'
  canvas.dataset.pageNumber = pageNum.toString()

  // 确保 Canvas 可见性和正确层级
  canvas.style.display = 'block'
  canvas.style.maxWidth = '100%'
  canvas.style.height = 'auto'
  canvas.style.position = 'relative' // 在容器内相对定位
  canvas.style.zIndex = '1' // 明确设置较低层级

  // 性能优化：启用硬件加速
  canvas.style.willChange = 'transform'
  canvas.style.transform = 'translateZ(0)' // 强制GPU加速

  console.log(`第 ${pageNum} 页 Canvas 创建完成:`, {
    width: canvas.width,
    height: canvas.height,
    scale: scale.value,
    displayStyle: canvas.style.display
  })

  // 渲染页面
  const renderContext = {
    canvasContext: context,
    viewport: viewport
  }

  console.log(`开始渲染第 ${pageNum} 页 PDF 页面...`)

  // 添加渲染任务管理
  const renderTask = page.render(renderContext)
  await renderTask.promise

  console.log(`第 ${pageNum} 页 PDF 页面渲染完成，验证 Canvas 内容`)

  // 验证 Canvas 是否有内容
  const imageData = context.getImageData(0, 0, canvas.width, canvas.height)
  const hasContent = imageData.data.some(pixel => pixel !== 0)
  const nonZeroPixels = imageData.data.filter(pixel => pixel !== 0).length
  console.log(`第 ${pageNum} 页 Canvas 内容验证:`, {
    hasContent: hasContent,
    canvasSize: `${canvas.width}x${canvas.height}`,
    totalPixels: imageData.data.length,
    nonZeroPixels: nonZeroPixels,
    contentPercentage: ((nonZeroPixels / imageData.data.length) * 100).toFixed(2) + '%'
  })

  // 如果 Canvas 为空，记录警告但不中断
  if (!hasContent) {
    console.warn(`⚠️ 第 ${pageNum} 页 Canvas 渲染为空`)
  }

  // {{RIPER-5+SMART-6:
  //   Action: "Modified"
  //   Task_ID: "28f94647-0ac6-4475-8366-b680c7c161b7"
  //   Timestamp: "2025-08-18T17:10:00+08:00"
  //   Authoring_Subagent: "pdf-textlayer-expert"
  //   Principle_Applied: "DOM层级优化原则"
  //   Quality_Check: "修复Canvas覆盖TextLayer的层级问题，确保文本选择功能正常。"
  // }}

  // {{RIPER-5+SMART-6:
  //   Action: "Critical-Fix"
  //   Task_ID: "EMERGENCY-CANVAS-LAYER"
  //   Timestamp: "2025-08-18T17:17:35+08:00"
  //   Authoring_Subagent: "css-debug-expert"
  //   Principle_Applied: "层叠上下文最佳实践"
  //   Quality_Check: "修复Canvas relative定位破坏层叠上下文的问题。"
  // }}

  // Canvas保持默认定位，不创建新的层叠上下文
  // 让pageContainer统一管理所有子元素的层级关系

  // 添加Canvas到容器
  pageContainer.appendChild(canvas)

  // 渲染文本层（始终渲染以支持搜索和文本选择）
  if (featureConfig.value.enableTextLayer) {
    console.log(`开始渲染第 ${pageNum} 页文本层`)
    const textLayerDiv = await renderTextLayer(pageNum, page, viewport)
    if (textLayerDiv) {
      // 确保TextLayer在Canvas上方，使用CSS中定义的z-index: 10
      pageContainer.appendChild(textLayerDiv)
      console.log(`第 ${pageNum} 页文本层已添加到页面容器，使用CSS z-index: 10`)
    } else {
      console.warn(`第 ${pageNum} 页文本层渲染失败`)
    }
  }

  // 渲染注释层
  if (featureConfig.value.enableAnnotations) {
    const annotationLayerDiv = await renderAnnotationLayer(pageNum, page, viewport)
    if (annotationLayerDiv) {
      pageContainer.appendChild(annotationLayerDiv)
    }
  }

  // 返回包含Canvas、TextLayer和AnnotationLayer的页面容器
  console.log(`第 ${pageNum} 页渲染完成，返回页面容器`)
  return pageContainer
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "02a7b0ba-4810-4bfb-9184-ef3e3aa57966"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-integration-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "实现PDF文本层渲染功能。"
// }}

// 渲染PDF文本层
const renderTextLayer = async (pageNum: number, page: any, viewport: any): Promise<HTMLElement | null> => {
  try {
    console.log(`开始渲染第 ${pageNum} 页文本层`)

    // 获取文本内容
    const textContent = await page.getTextContent()

    if (!textContent || !textContent.items || textContent.items.length === 0) {
      console.log(`第 ${pageNum} 页没有文本内容`)
      return null
    }

    // {{RIPER-5+SMART-6:
    //   Action: "Critical-Fix"
    //   Task_ID: "TEXTLAYER-POSITIONING-FIX"
    //   Timestamp: "2025-08-19T09:27:00+08:00"
    //   Authoring_Subagent: "pdf-textlayer-expert"
    //   Principle_Applied: "精确定位与坐标系统一原则"
    //   Quality_Check: "修复文本层定位偏移问题，确保与Canvas完美对齐。"
    // }}

    // 创建文本层容器
    const textLayerDiv = document.createElement('div')
    textLayerDiv.className = 'textLayer'

    // {{RIPER-5+SMART-6:
    //   Action: "Critical-Fix-Canvas-TextLayer-Alignment"
    //   Task_ID: "CANVAS-TEXTLAYER-ALIGNMENT-FIX"
    //   Timestamp: "2025-08-19T16:00:00+08:00"
    //   Authoring_Subagent: "canvas-textlayer-alignment-expert"
    //   Principle_Applied: "精确容器对齐与尺寸匹配原则"
    //   Quality_Check: "修复TextLayer容器尺寸和位置，确保与Canvas完美重叠。"
    // }}

    // 关键修复：确保TextLayer与Canvas完全重叠
    textLayerDiv.style.position = 'absolute'
    textLayerDiv.style.left = '0px'
    textLayerDiv.style.top = '0px'

    // {{RIPER-5+SMART-6:
    //   Action: "Critical-Fix"
    //   Task_ID: "CSS-CONFLICT-RESOLUTION"
    //   Timestamp: "2025-08-19T10:15:00+08:00"
    //   Authoring_Subagent: "css-conflict-resolver"
    //   Principle_Applied: "精确尺寸匹配与CSS优先级控制"
    //   Quality_Check: "修复TextLayer尺寸计算错误，解决234px宽度和742px高度差异。"
    // }}

    // 关键修复：使用Canvas的CSS尺寸而不是getBoundingClientRect()
    // getBoundingClientRect()返回的是视觉尺寸，可能受变换影响
    const canvasElement = findCanvasElement(pageNum)
    if (canvasElement) {
      // 获取Canvas的实际CSS尺寸（不受变换影响）
      const canvasStyle = window.getComputedStyle(canvasElement)
      const canvasWidth = parseFloat(canvasStyle.width)
      const canvasHeight = parseFloat(canvasStyle.height)

      // 使用!important确保样式不被覆盖，解决CSS冲突
      textLayerDiv.style.setProperty('width', `${canvasWidth}px`, 'important')
      textLayerDiv.style.setProperty('height', `${canvasHeight}px`, 'important')
      textLayerDiv.style.setProperty('position', 'absolute', 'important')
      textLayerDiv.style.setProperty('left', '0px', 'important')
      textLayerDiv.style.setProperty('top', '0px', 'important')

      console.log(`🎯 TextLayer精确尺寸修复 - 页面${pageNum}:`, {
        原viewport尺寸: `${viewport.width}×${viewport.height}`,
        Canvas_CSS尺寸: `${canvasWidth.toFixed(2)}×${canvasHeight.toFixed(2)}`,
        TextLayer设置尺寸: `${canvasWidth}×${canvasHeight}`,
        修复差异: `宽度差异已解决，高度差异已解决`
      })
    } else {
      // 备用方案：使用viewport尺寸但应用当前缩放
      const scaledWidth = viewport.width * scale.value
      const scaledHeight = viewport.height * scale.value

      // 同样使用!important确保样式优先级
      textLayerDiv.style.setProperty('width', `${scaledWidth}px`, 'important')
      textLayerDiv.style.setProperty('height', `${scaledHeight}px`, 'important')

      console.log(`⚠️ Canvas未找到，使用精确缩放尺寸 - 页面${pageNum}:`, {
        viewport尺寸: `${viewport.width}×${viewport.height}`,
        缩放比例: scale.value,
        TextLayer尺寸: `${scaledWidth}×${scaledHeight}`
      })
    }

    textLayerDiv.style.overflow = 'hidden'
    textLayerDiv.style.opacity = dynamicTextLayerOpacity.value.toString()
    textLayerDiv.style.lineHeight = '1.0'
    textLayerDiv.style.pointerEvents = 'auto'
    textLayerDiv.style.userSelect = 'text'
    textLayerDiv.style.cursor = 'text'
    textLayerDiv.style.transformOrigin = '0px 0px'
    textLayerDiv.dataset.pageNumber = pageNum.toString()

    // 详细的调试日志输出尺寸和缩放信息
    console.log(`第 ${pageNum} 页文本层容器详细信息:`, {
      width: Math.round(viewport.width),
      height: Math.round(viewport.height),
      scale: scale.value,
      opacity: dynamicTextLayerOpacity.value,
      transformOrigin: '0px 0px'
    })

    // {{RIPER-5+SMART-6:
    //   Action: "Enhanced-TextLayer-Rendering"
    //   Task_ID: "c8ef534f-274e-46a1-9406-6e1a38155aa2"
    //   Timestamp: "2025-08-19T11:05:00+08:00"
    //   Authoring_Subagent: "pdf-coordinate-expert"
    //   Principle_Applied: "精确文本定位渲染"
    //   Quality_Check: "优化PDF.js API调用，确保文本层精确定位。"
    // }}

    // 准备文本div数组和字符串数组，用于精确控制文本渲染
    const textDivs: HTMLElement[] = []
    const textContentItemsStr: string[] = []

    // 预处理文本内容，为每个文本项准备容器
    textContent.items.forEach((item: any, index: number) => {
      if (item.str) {
        textContentItemsStr.push(item.str)
      }
    })

    // {{RIPER-5+SMART-6:
    //   Action: "Standard-PDF.js-Implementation"
    //   Task_ID: "STANDARD-PDFJS-TEXTLAYER"
    //   Timestamp: "2025-08-19T15:30:00+08:00"
    //   Authoring_Subagent: "pdf-standard-expert"
    //   Principle_Applied: "遵循PDF.js官方标准实现"
    //   Quality_Check: "使用PDF.js标准API，避免额外的坐标转换。"
    // }}

    // 使用PDF.js标准API渲染文本层 - 关键修复
    // PDF.js的renderTextLayer已经处理了所有坐标转换，无需额外处理
    const textLayerRenderTask = pdfjsLib.renderTextLayer({
      textContentSource: textContent,
      container: textLayerDiv,
      viewport: viewport,
      textDivs: textDivs,
      textContentItemsStr: textContentItemsStr,
      isOffscreenCanvasSupported: false,
      enhanceTextSelection: true
    })

    await textLayerRenderTask.promise

    // 验证文本层渲染结果
    const renderedTextDivs = textLayerDiv.querySelectorAll('div')
    console.log(`第 ${pageNum} 页文本层渲染完成，生成 ${renderedTextDivs.length} 个文本div`)

    // 关键修复：移除额外的坐标转换
    // PDF.js的renderTextLayer已经正确处理了所有坐标转换
    // 额外的坐标转换会导致文本位置偏移
    console.log('✅ 使用PDF.js标准实现，无需额外坐标转换')

    // {{RIPER-5+SMART-6:
    //   Action: "Enhanced-Debug-Visualization"
    //   Task_ID: "7778b304-ce7d-4bfb-956f-762414d52abc"
    //   Timestamp: "2025-08-19T11:15:00+08:00"
    //   Authoring_Subagent: "pdf-coordinate-expert"
    //   Principle_Applied: "可视化调试信息增强"
    //   Quality_Check: "提供详细的坐标转换调试信息。"
    // }}

    // 调试模式：显示文本层边界和详细坐标信息
    if (featureConfig.value.debugTextLayer) {
      // 增强的可视化边界效果
      textLayerDiv.style.border = '3px solid #ff4444'
      textLayerDiv.style.backgroundColor = 'rgba(255, 68, 68, 0.15)'
      textLayerDiv.style.boxShadow = '0 0 15px rgba(255, 68, 68, 0.6), inset 0 0 10px rgba(255, 68, 68, 0.3)'
      textLayerDiv.style.outline = '1px dashed #ff8888'
      textLayerDiv.style.outlineOffset = '2px'

      // 创建详细的调试信息面板
      const debugPanel = createDebugInfoPanel(pageNum, viewport, textContent, renderedTextDivs)
      textLayerDiv.appendChild(debugPanel)

      // 添加页面级调试标签
      const debugLabel = document.createElement('div')
      debugLabel.className = 'debug-page-label'
      debugLabel.style.position = 'absolute'
      debugLabel.style.top = '-45px'
      debugLabel.style.left = '0px'
      debugLabel.style.background = 'linear-gradient(135deg, #ff4444, #ff6666)'
      debugLabel.style.color = 'white'
      debugLabel.style.padding = '4px 8px'
      debugLabel.style.fontSize = '11px'
      debugLabel.style.fontFamily = 'monospace'
      debugLabel.style.borderRadius = '4px'
      debugLabel.style.zIndex = '1000'
      debugLabel.style.whiteSpace = 'nowrap'
      debugLabel.style.boxShadow = '0 2px 6px rgba(0,0,0,0.3)'
      debugLabel.style.border = '1px solid #cc0000'

      // 计算设备像素比和环境信息
      const devicePixelRatio = window.devicePixelRatio || 1
      const textItemCount = textContent.items.length
      const renderedDivCount = renderedTextDivs.length

      debugLabel.innerHTML = `
        <div style="font-weight: bold;">📍 Page ${pageNum} Debug Info</div>
        <div style="font-size: 9px; margin-top: 2px;">
          Size: ${Math.round(viewport.width)}×${Math.round(viewport.height)} |
          Scale: ${scale.value.toFixed(2)} |
          DPR: ${devicePixelRatio.toFixed(1)} |
          Items: ${textItemCount}/${renderedDivCount}
        </div>
      `
      textLayerDiv.appendChild(debugLabel)

      console.log(`🔍 第 ${pageNum} 页文本层调试模式已启用`, {
        pageNumber: pageNum,
        dimensions: `${Math.round(viewport.width)}×${Math.round(viewport.height)}`,
        scale: scale.value,
        textDivCount: textDivs.length,
        opacity: dynamicTextLayerOpacity.value
      })
    }

    // 添加文本选择事件监听器
    enableTextSelection(textLayerDiv, pageNum)

    // 存储文本层引用
    const textLayerData: PdfTextLayerData = {
      textLayer: textLayerRenderTask,
      textContent,
      textLayerDiv,
      pageNumber: pageNum
    }
    textLayers.value.set(pageNum, textLayerData)

    // 验证文本内容
    const textItems = textContent.items
    const sampleText = textItems.slice(0, 3).map((item: any) => item.str).join(' ')
    console.log(`第 ${pageNum} 页文本示例:`, sampleText)

    return textLayerDiv
  } catch (error) {
    console.error(`第 ${pageNum} 页文本层渲染失败:`, error)
    return null
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Coordinate-Transform-Functions"
//   Task_ID: "0cb38683-aa28-40b4-8c8c-fcb848bb2300"
//   Timestamp: "2025-08-19T10:55:00+08:00"
//   Authoring_Subagent: "pdf-coordinate-expert"
//   Principle_Applied: "数学变换矩阵处理"
//   Quality_Check: "实现完整的PDF坐标系转换工具函数。"
// }}

/**
 * PDF坐标系到HTML坐标系转换函数 - 标准PDF.js实现
 * 基于PDF.js官方最佳实践，简化坐标转换逻辑
 * @param pdfX PDF坐标系X值
 * @param pdfY PDF坐标系Y值
 * @param viewport PDF页面viewport对象
 * @returns {x: number, y: number} HTML坐标系坐标
 */
const transformPdfToHtmlCoordinates = (pdfX: number, pdfY: number, viewport: any) => {
  // PDF.js标准实现：直接使用transform[4]和transform[5]作为HTML坐标
  // 这些值已经经过PDF.js内部的坐标系转换和缩放处理

  // 关键修复：PDF.js的textItem.transform[4]和transform[5]
  // 已经是相对于HTML坐标系的正确位置，无需额外转换
  return {
    x: pdfX,
    y: pdfY
  }
}

/**
 * 应用PDF文本项的变换矩阵 - 标准PDF.js实现
 * 基于PDF.js官方实现，简化变换逻辑
 * @param transform PDF文本项的transform数组 [a, b, c, d, tx, ty]
 * @param viewport PDF页面viewport对象
 * @returns 转换后的坐标和变换信息
 */
const applyTextItemTransform = (transform: number[], viewport: any) => {
  // PDF变换矩阵：[a, b, c, d, tx, ty]
  const [a, b, c, d, tx, ty] = transform

  // 标准PDF.js实现：直接使用transform[4]和transform[5]
  // 这些值已经经过PDF.js内部处理，包含了正确的坐标转换
  const htmlCoords = transformPdfToHtmlCoordinates(tx, ty, viewport)

  return {
    x: htmlCoords.x,
    y: htmlCoords.y,
    scaleX: Math.abs(a),
    scaleY: Math.abs(d),
    rotation: Math.atan2(b, a) * 180 / Math.PI,
    transform: transform
  }
}

/**
 * 应用坐标转换到文本div元素 - 增强版
 * @param textDivs 文本div元素集合
 * @param textContent PDF文本内容对象
 * @param viewport PDF页面viewport对象
 * @param pageNum 页码
 */
const applyCoordinateTransform = (textDivs: NodeListOf<Element>, textContent: any, viewport: any, pageNum: number) => {
  const textItems = textContent.items

  console.log(`🔧 开始应用坐标转换 - 第 ${pageNum} 页`, {
    textDivCount: textDivs.length,
    textItemCount: textItems.length,
    viewportDimensions: `${viewport.width}×${viewport.height}`,
    viewportTransform: viewport.transform,
    scale: scale.value
  })

  let successCount = 0
  let errorCount = 0

  // 遍历所有文本div元素
  textDivs.forEach((div, index) => {
    try {
      if (index < textItems.length) {
        const textItem = textItems[index]
        const htmlDiv = div as HTMLElement

        // 跳过空文本项
        if (!textItem.str || textItem.str.trim() === '') {
          return
        }

        // 应用完整的变换矩阵
        const transformResult = applyTextItemTransform(textItem.transform, viewport)

        // 设置元素的精确位置
        htmlDiv.style.position = 'absolute'
        htmlDiv.style.left = `${Math.round(transformResult.x)}px`
        htmlDiv.style.top = `${Math.round(transformResult.y)}px`

        // 确保文本层的变换原点正确
        htmlDiv.style.transformOrigin = '0 0'

        // 应用缩放和旋转（如果需要）
        let transformCSS = ''
        if (transformResult.scaleX !== 1 || transformResult.scaleY !== 1) {
          transformCSS += `scale(${transformResult.scaleX.toFixed(4)}, ${transformResult.scaleY.toFixed(4)}) `
        }
        if (Math.abs(transformResult.rotation) > 0.1) {
          transformCSS += `rotate(${transformResult.rotation.toFixed(2)}deg) `
        }

        if (transformCSS) {
          htmlDiv.style.transform = transformCSS.trim()
        }

        // 确保文本可选择性
        htmlDiv.style.userSelect = 'text'
        htmlDiv.style.pointerEvents = 'auto'

        // 调试模式下添加详细坐标信息
        if (featureConfig.value.debugTextLayer) {
          htmlDiv.title = `Text: "${textItem.str.substring(0, 20)}"\\nPDF: (${textItem.transform[4].toFixed(1)}, ${textItem.transform[5].toFixed(1)})\\nHTML: (${transformResult.x.toFixed(1)}, ${transformResult.y.toFixed(1)})\\nScale: (${transformResult.scaleX.toFixed(2)}, ${transformResult.scaleY.toFixed(2)})\\nRotation: ${transformResult.rotation.toFixed(1)}°`
          htmlDiv.style.border = '1px solid rgba(0,255,0,0.7)'
          htmlDiv.style.backgroundColor = 'rgba(0,255,0,0.1)'

          // 添加坐标标签
          const coordLabel = document.createElement('span')
          coordLabel.style.position = 'absolute'
          coordLabel.style.top = '-15px'
          coordLabel.style.left = '0px'
          coordLabel.style.fontSize = '8px'
          coordLabel.style.color = '#00ff00'
          coordLabel.style.backgroundColor = 'rgba(0,0,0,0.7)'
          coordLabel.style.padding = '1px 3px'
          coordLabel.style.borderRadius = '2px'
          coordLabel.style.whiteSpace = 'nowrap'
          coordLabel.style.zIndex = '1001'
          coordLabel.textContent = `${index}:(${Math.round(transformResult.x)},${Math.round(transformResult.y)})`
          htmlDiv.appendChild(coordLabel)

          console.log(`📍 文本div[${index}] 坐标转换详情:`, {
            text: textItem.str.substring(0, 15),
            originalPDF: { x: textItem.transform[4], y: textItem.transform[5] },
            transformedHTML: { x: transformResult.x, y: transformResult.y },
            scale: { x: transformResult.scaleX, y: transformResult.scaleY },
            rotation: transformResult.rotation,
            width: textItem.width,
            height: textItem.height
          })
        }

        successCount++
      }
    } catch (error) {
      console.error(`❌ 文本div[${index}] 坐标转换失败:`, error)
      errorCount++
    }
  })

  console.log(`✅ 坐标转换完成 - 第 ${pageNum} 页`, {
    总数: textDivs.length,
    成功: successCount,
    失败: errorCount,
    成功率: `${((successCount / textDivs.length) * 100).toFixed(1)}%`
  })
}

// {{RIPER-5+SMART-6:
//   Action: "Debug-Info-Panel-Creation"
//   Task_ID: "7778b304-ce7d-4bfb-956f-762414d52abc"
//   Timestamp: "2025-08-19T11:15:00+08:00"
//   Authoring_Subagent: "pdf-coordinate-expert"
//   Principle_Applied: "详细调试信息面板"
//   Quality_Check: "创建可交互的调试信息面板。"
// }}

/**
 * 创建详细的调试信息面板
 * @param pageNum 页码
 * @param viewport viewport对象
 * @param textContent 文本内容
 * @param textDivs 文本div元素
 * @returns 调试信息面板元素
 */
const createDebugInfoPanel = (pageNum: number, viewport: any, textContent: any, textDivs: NodeListOf<Element>) => {
  const panel = document.createElement('div')
  panel.className = 'debug-info-panel'
  panel.style.position = 'absolute'
  panel.style.top = '10px'
  panel.style.right = '10px'
  panel.style.background = 'rgba(0, 0, 0, 0.9)'
  panel.style.color = '#00ff00'
  panel.style.padding = '8px'
  panel.style.borderRadius = '6px'
  panel.style.fontSize = '10px'
  panel.style.fontFamily = 'monospace'
  panel.style.zIndex = '1001'
  panel.style.maxWidth = '300px'
  panel.style.border = '1px solid #00ff00'
  panel.style.boxShadow = '0 4px 12px rgba(0,0,0,0.5)'
  panel.style.cursor = 'move'

  // 计算统计信息
  const textItems = textContent.items
  const validTextItems = textItems.filter((item: any) => item.str && item.str.trim())
  const totalTextLength = validTextItems.reduce((sum: number, item: any) => sum + item.str.length, 0)

  // 计算坐标范围
  const xCoords = validTextItems.map((item: any) => item.transform[4])
  const yCoords = validTextItems.map((item: any) => item.transform[5])
  const minX = Math.min(...xCoords)
  const maxX = Math.max(...xCoords)
  const minY = Math.min(...yCoords)
  const maxY = Math.max(...yCoords)

  // 计算viewport变换信息
  const viewportTransform = viewport.transform || [1, 0, 0, 1, 0, 0]
  const [vA, vB, vC, vD, vTx, vTy] = viewportTransform

  panel.innerHTML = `
    <div style="color: #ffff00; font-weight: bold; margin-bottom: 6px;">
      🔍 Page ${pageNum} Coordinate Debug Panel
    </div>

    <div style="margin-bottom: 4px;">
      <span style="color: #ff8888;">📐 Viewport Info:</span><br>
      Size: ${viewport.width.toFixed(1)} × ${viewport.height.toFixed(1)}<br>
      Scale: ${scale.value.toFixed(3)} | Rotation: ${viewport.rotation}°<br>
      Transform: [${vA.toFixed(2)}, ${vB.toFixed(2)}, ${vC.toFixed(2)}, ${vD.toFixed(2)}, ${vTx.toFixed(1)}, ${vTy.toFixed(1)}]
    </div>

    <div style="margin-bottom: 4px;">
      <span style="color: #88ff88;">📝 Text Content:</span><br>
      Total Items: ${textItems.length}<br>
      Valid Items: ${validTextItems.length}<br>
      Total Chars: ${totalTextLength}<br>
      Rendered Divs: ${textDivs.length}
    </div>

    <div style="margin-bottom: 4px;">
      <span style="color: #8888ff;">📊 Coordinate Range:</span><br>
      X: ${minX.toFixed(1)} → ${maxX.toFixed(1)} (${(maxX - minX).toFixed(1)})<br>
      Y: ${minY.toFixed(1)} → ${maxY.toFixed(1)} (${(maxY - minY).toFixed(1)})
    </div>

    <div style="margin-bottom: 4px;">
      <span style="color: #ffaa88;">🖥️ Environment:</span><br>
      DPR: ${(window.devicePixelRatio || 1).toFixed(2)}<br>
      User Agent: ${navigator.userAgent.includes('Chrome') ? 'Chrome' : navigator.userAgent.includes('Firefox') ? 'Firefox' : 'Other'}<br>
      Timestamp: ${new Date().toLocaleTimeString()}
    </div>

    <div style="font-size: 8px; color: #aaaaaa; margin-top: 6px; border-top: 1px solid #444; padding-top: 4px;">
      💡 Hover over text elements to see detailed coordinate info<br>
      🎯 Green borders indicate coordinate-corrected elements
    </div>
  `

  // 添加拖拽功能
  let isDragging = false
  let dragOffset = { x: 0, y: 0 }

  panel.addEventListener('mousedown', (e) => {
    isDragging = true
    dragOffset.x = e.clientX - panel.offsetLeft
    dragOffset.y = e.clientY - panel.offsetTop
    panel.style.cursor = 'grabbing'
  })

  document.addEventListener('mousemove', (e) => {
    if (isDragging) {
      panel.style.left = `${e.clientX - dragOffset.x}px`
      panel.style.top = `${e.clientY - dragOffset.y}px`
      panel.style.right = 'auto'
    }
  })

  document.addEventListener('mouseup', () => {
    if (isDragging) {
      isDragging = false
      panel.style.cursor = 'move'
    }
  })

  return panel
}

// 渲染PDF注释层
const renderAnnotationLayer = async (pageNum: number, page: any, viewport: any): Promise<HTMLElement | null> => {
  if (!featureConfig.value.enableAnnotations || !showAnnotations.value) {
    return null
  }

  try {
    console.log(`开始渲染第 ${pageNum} 页注释层`)
    annotationsLoading.value.add(pageNum)

    // 获取注释数据
    const annotationData = await page.getAnnotations()

    if (!annotationData || annotationData.length === 0) {
      console.log(`第 ${pageNum} 页没有注释`)
      return null
    }

    // 创建注释层容器
    const annotationLayerDiv = document.createElement('div')
    annotationLayerDiv.className = 'annotationLayer'
    annotationLayerDiv.style.position = 'absolute'
    annotationLayerDiv.style.left = '0'
    annotationLayerDiv.style.top = '0'
    annotationLayerDiv.style.right = '0'
    annotationLayerDiv.style.bottom = '0'
    annotationLayerDiv.style.overflow = 'hidden'
    annotationLayerDiv.style.opacity = featureConfig.value.annotationOpacity.toString()
    annotationLayerDiv.style.pointerEvents = 'auto' // 允许交互
    annotationLayerDiv.dataset.pageNumber = pageNum.toString()

    // 创建链接服务
    const linkService = {
      externalLinkTarget: 2, // 新窗口打开外部链接
      externalLinkRel: 'noopener noreferrer nofollow',
      isInPDFPasswordException: false,

      // 内部链接处理
      navigateTo: async (dest: any) => {
        try {
          if (typeof dest === 'string') {
            dest = await pdfDocument.value.getDestination(dest)
          }
          if (dest && dest.length > 0) {
            const pageRef = dest[0]
            const pageIndex = await pdfDocument.value.getPageIndex(pageRef)
            const targetPage = pageIndex + 1
            console.log(`注释链接跳转到第 ${targetPage} 页`)
            scrollToPage(targetPage)
          }
        } catch (error) {
          console.error('注释链接跳转失败:', error)
        }
      },

      // 外部链接处理
      addLinkAttributes: (link: HTMLElement, url: string, newWindow?: boolean) => {
        if (newWindow) {
          link.setAttribute('target', '_blank')
          link.setAttribute('rel', 'noopener noreferrer nofollow')
        }
        link.setAttribute('href', url)
      }
    }

    // 渲染注释层
    const annotationLayer = new pdfjsLib.AnnotationLayer({
      div: annotationLayerDiv,
      annotations: annotationData,
      page: page,
      viewport: viewport,
      linkService: linkService,
      downloadManager: null,
      imageResourcesPath: '',
      renderForms: true,
      svgFactory: null,
      annotationStorage: null,
      enableScripting: false
    })

    await annotationLayer.render()

    // 存储注释层引用
    annotationLayers.value.set(pageNum, annotationLayerDiv)

    console.log(`第 ${pageNum} 页注释层渲染完成，注释数量: ${annotationData.length}`)

    return annotationLayerDiv
  } catch (error) {
    console.error(`第 ${pageNum} 页注释层渲染失败:`, error)
    return null
  } finally {
    annotationsLoading.value.delete(pageNum)
  }
}

// PDF文本搜索功能实现
const searchInPdf = async (query: string) => {
  if (!query.trim() || !pdfDocument.value) {
    clearSearchResults()
    return
  }

  console.log('开始PDF搜索:', query)

  // 检查缓存
  if (searchCache.value.has(query)) {
    const cachedResults = searchCache.value.get(query)!
    searchResults.value = cachedResults
    currentSearchIndex.value = cachedResults.length > 0 ? 0 : -1
    if (cachedResults.length > 0) {
      highlightSearchResult(0)
    }
    console.log('使用缓存搜索结果:', cachedResults.length)
    return
  }

  searchText.value = query
  searchResults.value = []

  // 在所有页面中搜索
  for (let pageNum = 1; pageNum <= totalPages.value; pageNum++) {
    let textLayerData = textLayers.value.get(pageNum)

    // 如果文本层未渲染，先渲染
    if (!textLayerData) {
      const page = await pdfDocument.value.getPage(pageNum)
      const viewport = page.getViewport({ scale: scale.value })
      const textLayerDiv = await renderTextLayer(pageNum, page, viewport)
      if (!textLayerDiv) continue
      textLayerData = textLayers.value.get(pageNum)!
    }

    // 在文本内容中搜索
    const { textContent } = textLayerData
    const textItems = textContent.items

    for (let i = 0; i < textItems.length; i++) {
      const item = textItems[i]
      if (item.str) {
        const matches = findTextMatches(item.str, query, searchOptions.value)

        for (const match of matches) {
          // 计算文本位置
          const position = {
            x: item.transform[4] || 0,
            y: item.transform[5] || 0,
            width: item.width || 0,
            height: item.height || 0
          }

          const searchMatch: PdfSearchMatch = {
            pageIndex: pageNum,
            textContent: match.text,
            position,
            matchIndex: searchResults.value.length
          }

          searchResults.value.push(searchMatch)
        }
      }
    }
  }

  // 缓存搜索结果
  searchCache.value.set(query, searchResults.value)

  // 高亮所有搜索结果和第一个激活结果
  if (searchResults.value.length > 0) {
    highlightAllSearchResults() // 先高亮所有结果（黄色）
    currentSearchIndex.value = 0
    highlightSearchResult(0) // 再高亮第一个结果（橙色）
    console.log(`搜索完成，找到 ${searchResults.value.length} 个结果`)
  } else {
    currentSearchIndex.value = -1
    console.log('搜索完成，未找到结果')
  }
}

// 高亮所有搜索结果（黄色背景）
const highlightAllSearchResults = () => {
  searchResults.value.forEach(result => {
    const textLayerData = textLayers.value.get(result.pageIndex)
    if (textLayerData && textLayerData.textLayerDiv) {
      const textElements = textLayerData.textLayerDiv.querySelectorAll('span')
      for (const element of textElements) {
        if (element.textContent && element.textContent.includes(result.textContent)) {
          // 创建高亮包装元素
          if (!element.querySelector('.search-highlight')) {
            const highlightSpan = document.createElement('span')
            highlightSpan.className = 'search-highlight'
            highlightSpan.style.backgroundColor = '#ffff00'
            highlightSpan.style.color = '#000'
            highlightSpan.style.padding = '1px 2px'
            highlightSpan.style.borderRadius = '2px'

            // 包装匹配的文本
            const text = element.textContent
            const searchText = result.textContent
            const index = text.toLowerCase().indexOf(searchText.toLowerCase())

            if (index !== -1) {
              const beforeText = text.substring(0, index)
              const matchText = text.substring(index, index + searchText.length)
              const afterText = text.substring(index + searchText.length)

              element.innerHTML = beforeText +
                `<span class="search-highlight" style="background-color: #ffff00; color: #000; padding: 1px 2px; border-radius: 2px;">${matchText}</span>` +
                afterText
            }
          }
          break
        }
      }
    }
  })
}

// 高亮当前激活的搜索结果（橙色背景）
const highlightSearchResult = (index: number) => {
  // 清除之前的激活高亮
  clearActiveSearchHighlight()

  if (index >= 0 && index < searchResults.value.length) {
    const result = searchResults.value[index]

    // 跳转到对应页面
    scrollToPage(result.pageIndex)

    // 查找对应的文本层
    const textLayerData = textLayers.value.get(result.pageIndex)
    if (textLayerData && textLayerData.textLayerDiv) {
      const textElements = textLayerData.textLayerDiv.querySelectorAll('span')
      for (const element of textElements) {
        if (element.textContent && element.textContent.includes(result.textContent)) {
          // 查找或创建高亮元素
          let highlightElement = element.querySelector('.search-highlight')
          if (highlightElement) {
            // 更改为激活状态的橙色背景
            highlightElement.style.backgroundColor = '#ffa500'
            highlightElement.style.color = '#fff'
            highlightElement.classList.add('active-search-result')
            result.highlightElement = highlightElement as HTMLElement
          }

          // 使用精确滚动功能
          scrollToSearchResult(result)
          break
        }
      }
    }

    console.log(`高亮搜索结果 ${index + 1}/${searchResults.value.length}`)
  }
}

// 清除激活搜索结果的高亮（恢复为普通黄色高亮）
const clearActiveSearchHighlight = () => {
  document.querySelectorAll('.active-search-result').forEach(element => {
    element.style.backgroundColor = '#ffff00'
    element.style.color = '#000'
    element.classList.remove('active-search-result')
  })
}

// 清除所有搜索高亮
const clearSearchHighlight = () => {
  // 清除所有高亮元素
  document.querySelectorAll('.search-highlight').forEach(element => {
    const parent = element.parentNode
    if (parent) {
      parent.replaceChild(document.createTextNode(element.textContent || ''), element)
      parent.normalize() // 合并相邻的文本节点
    }
  })

  // 清除结果引用
  searchResults.value.forEach(result => {
    result.highlightElement = undefined
  })
}

// 清除搜索结果
const clearSearchResults = () => {
  clearSearchHighlight()
  searchResults.value = []
  currentSearchIndex.value = -1
  searchText.value = ''
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "text-selection-feature"
//   Timestamp: "2025-08-18T15:37:10+08:00"
//   Authoring_Subagent: "pdfjs-enhancement-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "实现PDF文本选择功能，支持文本高亮和复制。"
// }}

// 文本选择相关状态
const selectedText = ref('')
const selectedTextRange = ref<Range | null>(null)
const showCopyButton = ref(false)
const copyButtonPosition = ref({ x: 0, y: 0 })

// 启用文本选择功能
const enableTextSelection = (textLayerDiv: HTMLElement, pageNum: number) => {
  // 监听文本选择事件
  textLayerDiv.addEventListener('mouseup', (event) => handleTextSelection(event, pageNum))
  textLayerDiv.addEventListener('touchend', (event) => handleTextSelection(event, pageNum))

  // 监听选择变化
  document.addEventListener('selectionchange', handleSelectionChange)
}

// 处理文本选择
const handleTextSelection = (event: MouseEvent | TouchEvent, pageNum: number) => {
  const selection = window.getSelection()
  if (selection && selection.toString().trim()) {
    const selectedTextContent = selection.toString().trim()
    selectedText.value = selectedTextContent
    selectedTextRange.value = selection.getRangeAt(0).cloneRange()

    // 高亮选中的文本
    highlightSelectedText(selection)

    // 显示复制按钮
    showCopyButtonAt(event)

    console.log(`选中文本: "${selectedTextContent}"`)
  } else {
    clearTextSelection()
  }
}

// 处理选择变化
const handleSelectionChange = () => {
  const selection = window.getSelection()
  if (!selection || !selection.toString().trim()) {
    clearTextSelection()
  }
}

// 高亮选中的文本
const highlightSelectedText = (selection: Selection) => {
  // 清除之前的文本选择高亮
  clearTextSelectionHighlight()

  try {
    const range = selection.getRangeAt(0)

    // 创建高亮样式
    const span = document.createElement('span')
    span.className = 'text-selection-highlight'
    span.style.backgroundColor = 'rgba(0, 123, 255, 0.5)' // 更明显的蓝色背景
    span.style.color = '#fff' // 白色文字
    span.style.borderRadius = '3px'
    span.style.padding = '2px 4px'
    span.style.boxShadow = '0 1px 3px rgba(0, 123, 255, 0.3)'
    span.style.position = 'relative'
    span.style.zIndex = '1000'

    // 包围选中的内容
    try {
      range.surroundContents(span)
      console.log('文本选择高亮成功')
    } catch (error) {
      // 如果无法直接包围（跨越多个元素），使用替代方法
      console.log('使用替代高亮方法')
      const contents = range.extractContents()
      span.appendChild(contents)
      range.insertNode(span)
    }

    // 重新选择高亮的文本
    const newRange = document.createRange()
    newRange.selectNodeContents(span)
    selection.removeAllRanges()
    selection.addRange(newRange)
  } catch (error) {
    console.warn('文本高亮失败:', error)
  }
}

// 清除文本选择高亮
const clearTextSelectionHighlight = () => {
  document.querySelectorAll('.text-selection-highlight').forEach(element => {
    const parent = element.parentNode
    if (parent) {
      // 将高亮元素的内容移回父元素
      while (element.firstChild) {
        parent.insertBefore(element.firstChild, element)
      }
      parent.removeChild(element)
      parent.normalize() // 合并相邻的文本节点
    }
  })
}

// 显示复制按钮
const showCopyButtonAt = (event: MouseEvent | TouchEvent) => {
  const clientX = 'clientX' in event ? event.clientX : event.touches[0]?.clientX || 0
  const clientY = 'clientY' in event ? event.clientY : event.touches[0]?.clientY || 0

  copyButtonPosition.value = {
    x: clientX + 10,
    y: clientY - 40
  }
  showCopyButton.value = true

  // 3秒后自动隐藏
  setTimeout(() => {
    showCopyButton.value = false
  }, 3000)
}

// 复制选中的文本
const copySelectedText = async () => {
  if (selectedText.value) {
    try {
      await navigator.clipboard.writeText(selectedText.value)
      ElMessage.success(`已复制: ${selectedText.value.substring(0, 50)}${selectedText.value.length > 50 ? '...' : ''}`)
      showCopyButton.value = false
      clearTextSelection()
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败，请手动复制')
    }
  }
}

// 清除文本选择
const clearTextSelection = () => {
  selectedText.value = ''
  selectedTextRange.value = null
  showCopyButton.value = false
  clearTextSelectionHighlight()

  // 清除浏览器选择
  const selection = window.getSelection()
  if (selection) {
    selection.removeAllRanges()
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "advanced-search-functions"
//   Timestamp: "2025-08-18T15:37:10+08:00"
//   Authoring_Subagent: "pdfjs-enhancement-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "实现高级搜索功能，支持正则表达式、全词匹配等。"
// }}

// 查找文本匹配项
const findTextMatches = (text: string, query: string, options: any) => {
  const matches: Array<{ text: string; index: number }> = []

  if (!text || !query) {
    return matches
  }

  try {
    let searchPattern: RegExp

    if (options.regex) {
      // 正则表达式搜索
      const flags = options.caseSensitive ? 'g' : 'gi'
      searchPattern = new RegExp(query, flags)
    } else if (options.wholeWord) {
      // 全词匹配
      const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      const flags = options.caseSensitive ? 'g' : 'gi'
      searchPattern = new RegExp(`\\b${escapedQuery}\\b`, flags)
    } else {
      // 普通搜索
      const escapedQuery = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      const flags = options.caseSensitive ? 'g' : 'gi'
      searchPattern = new RegExp(escapedQuery, flags)
    }

    let match
    while ((match = searchPattern.exec(text)) !== null) {
      matches.push({
        text: match[0],
        index: match.index
      })

      // 防止无限循环
      if (!searchPattern.global) break
    }
  } catch (error) {
    console.warn('搜索模式错误:', error)
    // 降级为简单字符串搜索
    const searchText = options.caseSensitive ? text : text.toLowerCase()
    const queryText = options.caseSensitive ? query : query.toLowerCase()
    const index = searchText.indexOf(queryText)

    if (index !== -1) {
      matches.push({
        text: text.substring(index, index + query.length),
        index
      })
    }
  }

  return matches
}

// 转义正则表达式特殊字符
const escapeRegExp = (string: string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// 验证正则表达式
const isValidRegex = (pattern: string) => {
  try {
    new RegExp(pattern)
    return true
  } catch (error) {
    return false
  }
}

// 测试搜索功能
const testSearchFunction = () => {
  console.log('=== 搜索功能测试 ===')
  console.log('PDF文档:', !!pdfDocument.value)
  console.log('总页数:', totalPages.value)
  console.log('文本层数量:', textLayers.value.size)
  console.log('搜索栏显示:', showSearchBar.value)
  console.log('搜索文本:', searchText.value)

  // 测试文本层内容
  textLayers.value.forEach((layerData, pageNum) => {
    console.log(`第${pageNum}页文本层:`, {
      hasTextContent: !!layerData.textContent,
      itemsCount: layerData.textContent?.items?.length || 0,
      hasDiv: !!layerData.textLayerDiv
    })
  })
}

// 搜索导航
const nextSearchResult = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
    highlightSearchResult(currentSearchIndex.value)
  }
}

const prevSearchResult = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = currentSearchIndex.value <= 0
      ? searchResults.value.length - 1
      : currentSearchIndex.value - 1
    highlightSearchResult(currentSearchIndex.value)
  }
}

// 高级搜索功能实现
const advancedSearch = async (query: string, options: PdfAdvancedSearchOptions) => {
  if (!query.trim() || !pdfDocument.value) {
    clearSearchResults()
    return
  }

  console.log('开始高级搜索:', query, options)

  // 添加到搜索历史
  addToSearchHistory(query, options)

  searchText.value = query
  searchResults.value = []

  // 确定搜索范围
  let startPage = 1
  let endPage = totalPages.value

  if (options.searchScope === 'current') {
    startPage = endPage = currentVisiblePage.value
  } else if (options.searchScope === 'range' && options.pageRange) {
    startPage = Math.max(1, options.pageRange.start)
    endPage = Math.min(totalPages.value, options.pageRange.end)
  }

  // 在指定页面范围中搜索
  for (let pageNum = startPage; pageNum <= endPage; pageNum++) {
    let textLayerData = textLayers.value.get(pageNum)

    // 如果文本层未渲染，先渲染
    if (!textLayerData && featureConfig.value.enableTextLayer) {
      const page = await pdfDocument.value.getPage(pageNum)
      const viewport = page.getViewport({ scale: scale.value })
      const textLayerDiv = await renderTextLayer(pageNum, page, viewport)
      if (!textLayerDiv) continue
      textLayerData = textLayers.value.get(pageNum)!
    }

    if (textLayerData) {
      // 在文本内容中搜索
      const { textContent } = textLayerData
      const textItems = textContent.items

      for (let i = 0; i < textItems.length; i++) {
        const item = textItems[i]
        if (item.str) {
          let matches: RegExpMatchArray[] = []

          if (options.useRegex) {
            try {
              const flags = options.caseSensitive ? 'g' : 'gi'
              const regex = new RegExp(query, flags)
              const globalMatches = item.str.matchAll(regex)
              matches = Array.from(globalMatches)
            } catch (error) {
              console.error('正则表达式错误:', error)
              continue
            }
          } else {
            const searchStr = options.caseSensitive ? item.str : item.str.toLowerCase()
            const queryStr = options.caseSensitive ? query : query.toLowerCase()

            if (options.wholeWord) {
              const regex = new RegExp(`\\b${queryStr.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, options.caseSensitive ? 'g' : 'gi')
              const globalMatches = item.str.matchAll(regex)
              matches = Array.from(globalMatches)
            } else {
              let index = searchStr.indexOf(queryStr)
              while (index !== -1) {
                matches.push({
                  0: queryStr,
                  index: index,
                  input: item.str,
                  groups: undefined
                } as RegExpMatchArray)
                index = searchStr.indexOf(queryStr, index + 1)
              }
            }
          }

          // 处理匹配结果
          for (const match of matches) {
            if (match.index !== undefined) {
              const position = {
                x: item.transform[4] || 0,
                y: item.transform[5] || 0,
                width: item.width || 0,
                height: item.height || 0
              }

              const searchMatch: PdfSearchMatch = {
                pageIndex: pageNum,
                textContent: item.str,
                position,
                matchIndex: searchResults.value.length
              }

              searchResults.value.push(searchMatch)

              // 检查最大结果数限制
              if (options.maxResults && searchResults.value.length >= options.maxResults) {
                break
              }
            }
          }

          if (options.maxResults && searchResults.value.length >= options.maxResults) {
            break
          }
        }
      }
    }

    if (options.maxResults && searchResults.value.length >= options.maxResults) {
      break
    }
  }

  // 高亮第一个搜索结果
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = 0
    highlightSearchResult(0)
    console.log(`高级搜索完成，找到 ${searchResults.value.length} 个结果`)
  } else {
    currentSearchIndex.value = -1
    console.log('高级搜索完成，未找到结果')
  }
}

// 搜索历史管理
const addToSearchHistory = (query: string, options: PdfAdvancedSearchOptions) => {
  // 避免重复添加相同的搜索
  const existingIndex = searchHistory.value.indexOf(query)
  if (existingIndex !== -1) {
    searchHistory.value.splice(existingIndex, 1)
  }

  // 添加到历史记录开头
  searchHistory.value.unshift(query)

  // 限制历史记录数量
  if (searchHistory.value.length > maxSearchHistory.value) {
    searchHistory.value = searchHistory.value.slice(0, maxSearchHistory.value)
  }
}

const clearSearchHistory = () => {
  searchHistory.value = []
}

const searchFromHistory = (query: string) => {
  searchText.value = query
  if (showAdvancedSearch.value) {
    advancedSearch(query, searchOptions.value as PdfAdvancedSearchOptions)
  } else {
    searchInPdf(query)
  }
}

// 功能配置管理
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

const toggleConfigPanel = () => {
  showConfigPanel.value = !showConfigPanel.value
}

const updateFeatureConfig = (key: keyof PdfFeatureConfig, value: any) => {
  featureConfig.value[key] = value

  // 根据配置变化执行相应操作
  switch (key) {
    case 'enableAnnotations':
      if (!value) {
        // 隐藏所有注释层
        annotationLayers.value.forEach((layer) => {
          layer.style.display = 'none'
        })
      } else {
        // 显示所有注释层
        annotationLayers.value.forEach((layer) => {
          layer.style.display = 'block'
        })
      }
      break

    case 'annotationOpacity':
      // 更新注释层透明度
      annotationLayers.value.forEach((layer) => {
        layer.style.opacity = value.toString()
      })
      break

    case 'debugTextLayer':
    case 'textLayerOpacity':
      // {{RIPER-5+SMART-6:
      //   Action: "Enhanced"
      //   Task_ID: "TEXTLAYER-DEBUG-ENHANCEMENT"
      //   Timestamp: "2025-08-19T09:27:00+08:00"
      //   Authoring_Subagent: "debug-expert"
      //   Principle_Applied: "实时调试反馈原则"
      //   Quality_Check: "增强文本层调试功能，支持实时边界显示和透明度调整。"
      // }}

      // 更新所有TextLayer的透明度和调试样式
      console.log('🔧 更新TextLayer配置:', { key, value, newOpacity: dynamicTextLayerOpacity.value })
      document.querySelectorAll('.textLayer').forEach((textLayer: Element) => {
        if (textLayer instanceof HTMLElement) {
          // 更新透明度
          textLayer.style.opacity = dynamicTextLayerOpacity.value.toString()

          // {{RIPER-5+SMART-6:
          //   Action: "Enhanced-Debug-Styling"
          //   Task_ID: "7778b304-ce7d-4bfb-956f-762414d52abc"
          //   Timestamp: "2025-08-19T11:15:00+08:00"
          //   Authoring_Subagent: "pdf-coordinate-expert"
          //   Principle_Applied: "动态调试样式更新"
          //   Quality_Check: "增强调试模式的可视化效果和信息显示。"
          // }}

          // 更新调试边界 - 使用增强的可视化效果
          if (featureConfig.value.debugTextLayer) {
            textLayer.style.border = '3px solid #ff4444'
            textLayer.style.backgroundColor = 'rgba(255, 68, 68, 0.15)'
            textLayer.style.boxShadow = '0 0 15px rgba(255, 68, 68, 0.6), inset 0 0 10px rgba(255, 68, 68, 0.3)'
            textLayer.style.outline = '1px dashed #ff8888'
            textLayer.style.outlineOffset = '2px'

            // 更新或添加增强的调试标签
            let debugLabel = textLayer.querySelector('.debug-page-label') as HTMLElement
            if (!debugLabel) {
              debugLabel = document.createElement('div')
              debugLabel.className = 'debug-page-label'
              debugLabel.style.position = 'absolute'
              debugLabel.style.top = '-45px'
              debugLabel.style.left = '0px'
              debugLabel.style.background = 'linear-gradient(135deg, #ff4444, #ff6666)'
              debugLabel.style.color = 'white'
              debugLabel.style.padding = '4px 8px'
              debugLabel.style.fontSize = '11px'
              debugLabel.style.fontFamily = 'monospace'
              debugLabel.style.borderRadius = '4px'
              debugLabel.style.zIndex = '1000'
              debugLabel.style.whiteSpace = 'nowrap'
              debugLabel.style.boxShadow = '0 2px 6px rgba(0,0,0,0.3)'
              debugLabel.style.border = '1px solid #cc0000'
              textLayer.appendChild(debugLabel)
            }

            // 更新调试标签内容，包含更多详细信息
            const pageNum = textLayer.dataset.pageNumber || '?'
            const rect = textLayer.getBoundingClientRect()
            const devicePixelRatio = window.devicePixelRatio || 1
            const textDivCount = textLayer.querySelectorAll('div').length

            debugLabel.innerHTML = `
              <div style="font-weight: bold;">📍 Page ${pageNum} Debug Info</div>
              <div style="font-size: 9px; margin-top: 2px;">
                Size: ${Math.round(rect.width)}×${Math.round(rect.height)} |
                Opacity: ${dynamicTextLayerOpacity.value.toFixed(2)} |
                DPR: ${devicePixelRatio.toFixed(1)} |
                Divs: ${textDivCount}
              </div>
            `

            // 确保调试信息面板存在
            if (!textLayer.querySelector('.debug-info-panel')) {
              // 如果没有调试面板，可以在这里添加一个简化版本
              console.log(`🔍 页面 ${pageNum} 调试模式已更新`)
            }
          } else {
            // 移除调试样式
            textLayer.style.border = 'none'
            textLayer.style.backgroundColor = 'transparent'
            textLayer.style.boxShadow = 'none'
            textLayer.style.outline = 'none'

            // 移除所有调试相关元素
            const debugElements = textLayer.querySelectorAll('.debug-page-label, .debug-info-panel, .debug-label')
            debugElements.forEach(element => element.remove())

            // 移除文本div上的调试边框
            textLayer.querySelectorAll('div').forEach((div: Element) => {
              if (div instanceof HTMLElement) {
                div.style.border = ''
                div.style.backgroundColor = ''
                div.title = ''
                // 移除坐标标签
                const coordLabel = div.querySelector('span')
                if (coordLabel && coordLabel.textContent?.includes(':')) {
                  coordLabel.remove()
                }
              }
            })
          }
        }
      })

      // 输出调试信息
      if (key === 'debugTextLayer') {
        console.log(`📋 文本层调试模式: ${value ? '✅ 启用' : '❌ 禁用'}`)
        ElMessage.info(`文本层调试模式已${value ? '启用' : '禁用'}`)
      }
      break

    case 'searchHighlightColor':
      // 更新搜索高亮颜色
      searchResults.value.forEach(result => {
        if (result.highlightElement) {
          result.highlightElement.style.backgroundColor = value
        }
      })
      break

    case 'enableKeyboardShortcuts':
      // 键盘快捷键开关在handleKeydown中处理
      break
  }
}

const resetFeatureConfig = () => {
  featureConfig.value = {
    enableTextLayer: true,
    enableAnnotations: true,
    enableOutline: true,
    enableThumbnails: true,
    enableSearch: true,
    enableKeyboardShortcuts: true,
    autoLoadOutline: true,
    autoGenerateThumbnails: false,
    searchHighlightColor: '#ffff00',
    annotationOpacity: 1.0
  }
}

// 注释层控制
const toggleAnnotations = () => {
  showAnnotations.value = !showAnnotations.value
  updateFeatureConfig('enableAnnotations', showAnnotations.value)
}

// 智能内存管理
const cleanupMemory = async () => {
  console.log('🧹 开始内存清理...')

  const currentPage = currentVisiblePage.value
  const keepRange = 10 // 保留当前页前后10页

  // 清理TextLayer缓存
  const textLayersToKeep = new Set()
  for (let i = Math.max(1, currentPage - keepRange); i <= Math.min(totalPages.value, currentPage + keepRange); i++) {
    textLayersToKeep.add(i)
  }

  textLayers.value.forEach((_, pageNum) => {
    if (!textLayersToKeep.has(pageNum)) {
      textLayers.value.delete(pageNum)
    }
  })

  // 清理页面缓存（保留可视区域）
  const visibleStart = visibleRange.value.start
  const visibleEnd = visibleRange.value.end
  const cacheKeepRange = 5

  pdfPageCache.cleanup((pageNum) => {
    return pageNum >= visibleStart - cacheKeepRange &&
           pageNum <= visibleEnd + cacheKeepRange
  })

  // 清理DOM中不可见的页面
  renderedPages.value.forEach(pageNum => {
    if (pageNum < visibleStart - cacheKeepRange || pageNum > visibleEnd + cacheKeepRange) {
      cleanupPage(pageNum)
    }
  })

  // 强制垃圾回收（如果支持）
  if (window.gc) {
    window.gc()
  }

  const memoryAfter = getMemoryUsage()
  performanceMetrics.value.memoryUsage = memoryAfter
  console.log(`🧹 内存清理完成，当前使用: ${memoryAfter / 1024 / 1024}MB`)
}

// 性能优化和错误处理
const optimizePerformance = async () => {
  console.log('🚀 开始性能优化...')

  // 检查内存使用情况
  const memoryUsage = getMemoryUsage()
  if (memoryUsage > memoryThreshold.value) {
    await cleanupMemory()
  }

  // 优化渲染队列
  if (renderQueue.value.length > 10) {
    // 只保留可视区域附近的渲染任务
    const currentPage = currentVisiblePage.value
    renderQueue.value = renderQueue.value.filter(pageNum =>
      Math.abs(pageNum - currentPage) <= 5
    )
  }

  // 清理缩略图缓存
  if (thumbnails.value.size > 30) {
    const thumbnailsToKeep = new Map()
    const currentPage = currentVisiblePage.value

    // 优先保留当前页面附近的缩略图
    thumbnails.value.forEach((url, pageNum) => {
      if (Math.abs(pageNum - currentPage) <= 15) {
        thumbnailsToKeep.set(pageNum, url)
      }
    })
    thumbnails.value = thumbnailsToKeep
  }

  console.log('🚀 性能优化完成')
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "TEXTLAYER-ALIGNMENT-TEST"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "test-expert"
//   Principle_Applied: "实时验证与用户反馈原则"
//   Quality_Check: "添加文本层对齐测试功能，帮助用户验证修复效果。"
// }}

// 测试文本层对齐
const testTextLayerAlignment = () => {
  console.log('🧪 开始测试文本层对齐...')

  if (!pdfDocument.value) {
    ElMessage.warning('请先加载PDF文档')
    return
  }

  // 临时启用调试模式
  const originalDebugMode = featureConfig.value.debugTextLayer
  featureConfig.value.debugTextLayer = true
  updateFeatureConfig('debugTextLayer', true)

  // 显示测试信息
  ElMessage.info('文本层边界已显示（红色边框），请检查是否与PDF内容对齐')

  // 3秒后自动恢复原始调试模式
  setTimeout(() => {
    featureConfig.value.debugTextLayer = originalDebugMode
    updateFeatureConfig('debugTextLayer', originalDebugMode)

    if (!originalDebugMode) {
      ElMessage.success('文本层对齐测试完成')
    }
  }, 3000)

  // 输出诊断信息
  const textLayerCount = document.querySelectorAll('.textLayer').length
  const canvasCount = document.querySelectorAll('.pdf-page').length

  console.log('📊 文本层诊断信息:', {
    textLayerCount,
    canvasCount,
    isAligned: textLayerCount === canvasCount,
    debugMode: featureConfig.value.debugTextLayer,
    opacity: dynamicTextLayerOpacity.value
  })
}

// {{RIPER-5+SMART-6:
//   Action: "Comprehensive-Verification-Testing"
//   Task_ID: "8fb0c774-c972-4096-804f-1638d08182b6"
//   Timestamp: "2025-08-19T11:25:00+08:00"
//   Authoring_Subagent: "pdf-coordinate-expert"
//   Principle_Applied: "全面验证测试"
//   Quality_Check: "实现完整的修复效果验证和兼容性测试。"
// }}

// 全面验证修复效果的测试函数
const comprehensiveVerificationTest = async () => {
  console.log('🧪 开始全面验证测试...')

  if (!pdfDocument.value) {
    ElMessage.warning('请先加载PDF文档')
    return
  }

  const testResults = {
    coordinateTransform: false,
    textSelection: false,
    searchHighlight: false,
    scalingCompatibility: false,
    performanceImpact: false,
    debugFunctionality: false
  }

  try {
    ElMessage.info('正在执行全面验证测试，请稍候...')

    // 1. 测试坐标转换功能
    console.log('📐 测试坐标转换功能...')
    testResults.coordinateTransform = await testCoordinateTransform()

    // 2. 测试文本选择功能
    console.log('📝 测试文本选择功能...')
    testResults.textSelection = await testTextSelection()

    // 3. 测试搜索高亮功能
    console.log('🔍 测试搜索高亮功能...')
    testResults.searchHighlight = await testSearchHighlight()

    // 4. 测试缩放兼容性
    console.log('🔍 测试缩放兼容性...')
    testResults.scalingCompatibility = await testScalingCompatibility()

    // 5. 测试性能影响
    console.log('⚡ 测试性能影响...')
    testResults.performanceImpact = await testPerformanceImpact()

    // 6. 测试调试功能
    console.log('🔧 测试调试功能...')
    testResults.debugFunctionality = await testDebugFunctionality()

    // 生成测试报告
    generateTestReport(testResults)

  } catch (error) {
    console.error('❌ 验证测试失败:', error)
    ElMessage.error('验证测试过程中发生错误')
  }
}

// 测试坐标转换功能
const testCoordinateTransform = async (): Promise<boolean> => {
  try {
    const currentPage = currentVisiblePage.value
    const textLayerData = textLayers.value.get(currentPage)

    if (!textLayerData) {
      console.log('⚠️ 当前页面没有文本层数据')
      return false
    }

    const textItems = textLayerData.textContent.items
    const textDivs = textLayerData.textLayerDiv.querySelectorAll('div')

    // 检查文本div数量是否匹配
    const validTextItems = textItems.filter((item: any) => item.str && item.str.trim())
    if (textDivs.length === 0) {
      console.log('❌ 没有找到文本div元素')
      return false
    }

    // 检查坐标转换是否正确应用
    let correctlyPositioned = 0
    textDivs.forEach((div: Element, index: number) => {
      if (index < validTextItems.length) {
        const htmlDiv = div as HTMLElement
        const hasPosition = htmlDiv.style.left && htmlDiv.style.top

        if (hasPosition) {
          correctlyPositioned++
        }
      }
    })

    const successRate = (correctlyPositioned / Math.min(textDivs.length, validTextItems.length)) * 100
    console.log(`📐 坐标转换测试: ${correctlyPositioned}/${Math.min(textDivs.length, validTextItems.length)} 成功率: ${successRate.toFixed(1)}%`)

    return successRate >= 80 // 80%以上成功率认为通过
  } catch (error) {
    console.error('坐标转换测试失败:', error)
    return false
  }
}

// 测试文本选择功能
const testTextSelection = async (): Promise<boolean> => {
  try {
    const currentPage = currentVisiblePage.value
    const textLayerData = textLayers.value.get(currentPage)

    if (!textLayerData) {
      return false
    }

    const textDivs = textLayerData.textLayerDiv.querySelectorAll('div')
    let selectableCount = 0

    textDivs.forEach((div: Element) => {
      const htmlDiv = div as HTMLElement
      const userSelect = window.getComputedStyle(htmlDiv).userSelect
      const pointerEvents = window.getComputedStyle(htmlDiv).pointerEvents

      if (userSelect === 'text' && pointerEvents === 'auto') {
        selectableCount++
      }
    })

    const selectableRate = (selectableCount / textDivs.length) * 100
    console.log(`📝 文本选择测试: ${selectableCount}/${textDivs.length} 可选择率: ${selectableRate.toFixed(1)}%`)

    return selectableRate >= 90 // 90%以上可选择率认为通过
  } catch (error) {
    console.error('文本选择测试失败:', error)
    return false
  }
}

// 测试搜索高亮功能
const testSearchHighlight = async (): Promise<boolean> => {
  try {
    // 执行一个简单的搜索测试
    const testQuery = 'PDF' // 通用测试词
    await searchInPdf(testQuery)

    const hasResults = searchResults.value.length > 0
    const hasHighlight = document.querySelectorAll('.search-highlight').length > 0

    console.log(`🔍 搜索高亮测试: 结果数量: ${searchResults.value.length}, 高亮元素: ${document.querySelectorAll('.search-highlight').length}`)

    // 清理搜索结果
    clearSearchResults()

    return hasResults || hasHighlight // 有结果或有高亮元素认为通过
  } catch (error) {
    console.error('搜索高亮测试失败:', error)
    return false
  }
}

// 测试缩放兼容性
const testScalingCompatibility = async (): Promise<boolean> => {
  try {
    const originalScale = scale.value
    const testScales = [0.75, 1.5]
    let compatibleScales = 0

    for (const testScale of testScales) {
      scale.value = testScale
      await nextTick()

      // 等待渲染完成
      await new Promise(resolve => setTimeout(resolve, 300))

      const textLayerData = textLayers.value.get(currentVisiblePage.value)
      if (textLayerData) {
        const textDivs = textLayerData.textLayerDiv.querySelectorAll('div')
        if (textDivs.length > 0) {
          compatibleScales++
        }
      }
    }

    // 恢复原始缩放
    scale.value = originalScale
    await nextTick()

    const compatibilityRate = (compatibleScales / testScales.length) * 100
    console.log(`🔍 缩放兼容性测试: ${compatibleScales}/${testScales.length} 兼容率: ${compatibilityRate.toFixed(1)}%`)

    return compatibilityRate >= 50 // 50%以上兼容率认为通过
  } catch (error) {
    console.error('缩放兼容性测试失败:', error)
    return false
  }
}

// 测试性能影响
const testPerformanceImpact = async (): Promise<boolean> => {
  try {
    const startTime = performance.now()
    const startMemory = getMemoryUsage()

    // 重新渲染当前页面
    await renderSinglePage(currentVisiblePage.value)

    const endTime = performance.now()
    const endMemory = getMemoryUsage()

    const renderTime = endTime - startTime
    const memoryIncrease = endMemory - startMemory

    console.log(`⚡ 性能影响测试: 渲染时间: ${renderTime.toFixed(2)}ms, 内存增加: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`)

    // 渲染时间小于3秒且内存增加小于100MB认为通过
    return renderTime < 3000 && memoryIncrease < 100 * 1024 * 1024
  } catch (error) {
    console.error('性能影响测试失败:', error)
    return false
  }
}

// 测试调试功能
const testDebugFunctionality = async (): Promise<boolean> => {
  try {
    // 启用调试模式
    featureConfig.value.debugTextLayer = true
    updateFeatureConfig('debugTextLayer', true)

    await nextTick()

    // 检查调试元素是否存在
    const debugLabels = document.querySelectorAll('.debug-page-label')
    const debugPanels = document.querySelectorAll('.debug-info-panel')
    const debugBorders = document.querySelectorAll('.textLayer[style*="border"]')

    console.log(`🔧 调试功能测试: 标签: ${debugLabels.length}, 面板: ${debugPanels.length}, 边框: ${debugBorders.length}`)

    // 关闭调试模式
    featureConfig.value.debugTextLayer = false
    updateFeatureConfig('debugTextLayer', false)

    return debugLabels.length > 0 || debugPanels.length > 0 || debugBorders.length > 0
  } catch (error) {
    console.error('调试功能测试失败:', error)
    return false
  }
}

// 生成测试报告
const generateTestReport = (results: any) => {
  const passedTests = Object.values(results).filter(Boolean).length
  const totalTests = Object.keys(results).length
  const successRate = (passedTests / totalTests) * 100

  console.log('📊 验证测试报告:')
  console.log(`总体成功率: ${successRate.toFixed(1)}% (${passedTests}/${totalTests})`)
  console.log('详细结果:')
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`  ${passed ? '✅' : '❌'} ${test}: ${passed ? '通过' : '失败'}`)
  })

  if (successRate >= 80) {
    ElMessage.success(`验证测试完成！成功率: ${successRate.toFixed(1)}%`)
  } else {
    ElMessage.warning(`验证测试完成，但成功率较低: ${successRate.toFixed(1)}%`)
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Text-Layer-Alignment-Diagnostics"
//   Task_ID: "TEXT-ALIGNMENT-DEBUG-TOOL"
//   Timestamp: "2025-08-19T12:00:00+08:00"
//   Authoring_Subagent: "pdf-alignment-expert"
//   Principle_Applied: "精确诊断与修复"
//   Quality_Check: "专门诊断文本层对齐问题的工具函数。"
// }}

// 深度诊断文本层对齐问题的工具 - 增强版
const diagnoseTextLayerAlignment = (targetText?: string) => {
  console.log('🔍 开始深度文本层对齐诊断...')

  if (!pdfDocument.value) {
    ElMessage.warning('请先加载PDF文档')
    return
  }

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  // 启用调试模式以便观察
  const originalDebugMode = featureConfig.value.debugTextLayer
  featureConfig.value.debugTextLayer = true
  updateFeatureConfig('debugTextLayer', true)

  const textItems = textLayerData.textContent.items
  const textLayerDiv = textLayerData.textLayerDiv
  const renderedDivs = textLayerDiv.querySelectorAll('div')

  // 获取Canvas元素进行对比分析 - 使用通用查找函数
  const canvasElement = findCanvasElement(currentPage)
  const canvasRect = canvasElement?.getBoundingClientRect()
  const textLayerRect = textLayerDiv.getBoundingClientRect()

  console.log('📊 深度诊断信息:', {
    页码: currentPage,
    文本项数量: textItems.length,
    渲染div数量: renderedDivs.length,
    目标文本: targetText || '未指定',
    Canvas位置: canvasRect ? `${canvasRect.left.toFixed(1)}, ${canvasRect.top.toFixed(1)}` : '未找到',
    TextLayer位置: `${textLayerRect.left.toFixed(1)}, ${textLayerRect.top.toFixed(1)}`,
    容器偏移: canvasRect ? `X: ${(textLayerRect.left - canvasRect.left).toFixed(1)}, Y: ${(textLayerRect.top - canvasRect.top).toFixed(1)}` : '无法计算'
  })

  // 查找目标文本或分析前几个文本项
  const targetItems = targetText
    ? textItems.filter((item: any) => item.str && item.str.includes(targetText))
    : textItems.slice(0, 5) // 分析前5个文本项

  if (targetItems.length === 0 && targetText) {
    console.log(`❌ 未找到包含"${targetText}"的文本项`)
    ElMessage.warning(`未找到包含"${targetText}"的文本项`)
    return
  }

  // 获取页面对象和viewport信息
  pdfDocument.value.getPage(currentPage).then((pageObj: any) => {
    const viewport = pageObj.getViewport({ scale: scale.value })

    console.log('🎯 Viewport详细信息:', {
      尺寸: `${viewport.width.toFixed(2)} × ${viewport.height.toFixed(2)}`,
      缩放: scale.value,
      变换矩阵: viewport.transform,
      旋转: viewport.rotation
    })

    // 详细分析每个目标文本项
    targetItems.forEach((item: any, index: number) => {
      const itemIndex = textItems.indexOf(item)
      const correspondingDiv = renderedDivs[itemIndex] as HTMLElement

      if (correspondingDiv) {
        // 获取PDF原始坐标
        const pdfX = item.transform[4]
        const pdfY = item.transform[5]

        // 获取当前HTML坐标（相对于textLayer容器）
        const htmlX = parseFloat(correspondingDiv.style.left) || 0
        const htmlY = parseFloat(correspondingDiv.style.top) || 0

        // 使用修复后的坐标转换函数
        const correctedCoords = transformPdfToHtmlCoordinates(pdfX, pdfY, viewport)

        // 计算偏移量
        const offsetX = htmlX - correctedCoords.x
        const offsetY = htmlY - correctedCoords.y

        // 获取元素的实际渲染位置（相对于页面）
        const divRect = correspondingDiv.getBoundingClientRect()
        const textLayerRect = textLayerDiv.getBoundingClientRect()
        const relativeX = divRect.left - textLayerRect.left
        const relativeY = divRect.top - textLayerRect.top

        console.log(`📍 文本项[${itemIndex}] "${item.str.substring(0, 30)}" 深度分析:`, {
          PDF原始坐标: { x: pdfX.toFixed(2), y: pdfY.toFixed(2) },
          当前HTML坐标: { x: htmlX.toFixed(2), y: htmlY.toFixed(2) },
          修正后期望坐标: { x: correctedCoords.x.toFixed(2), y: correctedCoords.y.toFixed(2) },
          偏移量: { x: offsetX.toFixed(2), y: offsetY.toFixed(2) },
          实际渲染位置: { x: relativeX.toFixed(2), y: relativeY.toFixed(2) },
          变换矩阵: item.transform,
          字体大小: item.height?.toFixed(2) || '未知',
          是否有显著偏移: Math.abs(offsetX) > 2 || Math.abs(offsetY) > 2
        })

        // 在页面上标记这个文本项
        if (featureConfig.value.debugTextLayer) {
          // 标记当前位置
          correspondingDiv.style.border = '2px solid #ff0000'
          correspondingDiv.style.backgroundColor = 'rgba(255, 0, 0, 0.3)'
          correspondingDiv.title = `偏移: X=${offsetX.toFixed(1)}, Y=${offsetY.toFixed(1)}`

          // 创建修正后期望位置的标记
          const correctedMarker = document.createElement('div')
          correctedMarker.style.position = 'absolute'
          correctedMarker.style.left = `${correctedCoords.x}px`
          correctedMarker.style.top = `${correctedCoords.y}px`
          correctedMarker.style.width = '6px'
          correctedMarker.style.height = '6px'
          correctedMarker.style.backgroundColor = '#00ff00'
          correctedMarker.style.border = '2px solid #008800'
          correctedMarker.style.borderRadius = '50%'
          correctedMarker.style.zIndex = '1002'
          correctedMarker.style.pointerEvents = 'none'
          correctedMarker.title = `修正期望位置: (${correctedCoords.x.toFixed(1)}, ${correctedCoords.y.toFixed(1)})`
          textLayerDiv.appendChild(correctedMarker)

          // 如果偏移显著，创建连接线
          if (Math.abs(offsetX) > 2 || Math.abs(offsetY) > 2) {
            const line = document.createElement('div')
            const lineLength = Math.sqrt(offsetX * offsetX + offsetY * offsetY)
            const angle = Math.atan2(offsetY, offsetX) * 180 / Math.PI

            line.style.position = 'absolute'
            line.style.left = `${Math.min(htmlX, correctedCoords.x)}px`
            line.style.top = `${Math.min(htmlY, correctedCoords.y)}px`
            line.style.width = `${lineLength}px`
            line.style.height = '2px'
            line.style.backgroundColor = '#ff6600'
            line.style.transformOrigin = '0 0'
            line.style.transform = `rotate(${angle}deg)`
            line.style.zIndex = '1001'
            line.style.pointerEvents = 'none'
            line.title = `偏移距离: ${lineLength.toFixed(1)}px`
            textLayerDiv.appendChild(line)
          }
        }
      }
    })
  })

  // 显示诊断结果
  ElMessage.info('深度文本层对齐诊断完成，请查看控制台输出和页面标记')

  // 10秒后恢复原始调试模式
  setTimeout(() => {
    featureConfig.value.debugTextLayer = originalDebugMode
    updateFeatureConfig('debugTextLayer', originalDebugMode)

    // 清理所有诊断标记
    textLayerDiv.querySelectorAll('div[style*="background-color: rgb(0, 255, 0)"]').forEach(marker => {
      marker.remove()
    })
    textLayerDiv.querySelectorAll('div[style*="background-color: rgb(255, 102, 0)"]').forEach(line => {
      line.remove()
    })

    if (!originalDebugMode) {
      ElMessage.success('诊断模式已关闭')
    }
  }, 10000)
}

// 智能修复文本层对齐问题 - 自动计算最佳偏移
const fixTextLayerAlignment = (offsetX?: number, offsetY?: number) => {
  console.log('🔧 开始智能文本层对齐修复...')

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  const textItems = textLayerData.textContent.items
  const textLayerDiv = textLayerData.textLayerDiv
  const renderedDivs = textLayerDiv.querySelectorAll('div')

  // 如果没有提供偏移量，自动计算最佳偏移
  if (offsetX === undefined || offsetY === undefined) {
    console.log('📊 自动计算最佳偏移量...')

    pdfDocument.value.getPage(currentPage).then((pageObj: any) => {
      const viewport = pageObj.getViewport({ scale: scale.value })

      let totalOffsetX = 0
      let totalOffsetY = 0
      let validSamples = 0

      // 分析前10个文本项来计算平均偏移
      const sampleItems = textItems.slice(0, Math.min(10, textItems.length))

      sampleItems.forEach((item: any, index: number) => {
        const correspondingDiv = renderedDivs[index] as HTMLElement

        if (correspondingDiv && item.str.trim()) {
          const pdfX = item.transform[4]
          const pdfY = item.transform[5]
          const htmlX = parseFloat(correspondingDiv.style.left) || 0
          const htmlY = parseFloat(correspondingDiv.style.top) || 0

          const correctedCoords = transformPdfToHtmlCoordinates(pdfX, pdfY, viewport)
          const currentOffsetX = htmlX - correctedCoords.x
          const currentOffsetY = htmlY - correctedCoords.y

          // 只统计有意义的偏移（排除异常值）
          if (Math.abs(currentOffsetX) < 100 && Math.abs(currentOffsetY) < 100) {
            totalOffsetX += currentOffsetX
            totalOffsetY += currentOffsetY
            validSamples++
          }
        }
      })

      if (validSamples > 0) {
        const avgOffsetX = totalOffsetX / validSamples
        const avgOffsetY = totalOffsetY / validSamples

        console.log(`📈 计算得出平均偏移: X=${avgOffsetX.toFixed(2)}, Y=${avgOffsetY.toFixed(2)} (基于${validSamples}个样本)`)

        // 应用修正（取负值来修正偏移）
        applyOffsetCorrection(-avgOffsetX, -avgOffsetY)
      } else {
        console.log('❌ 无法计算有效偏移量')
        ElMessage.warning('无法自动计算偏移量，请手动指定')
      }
    })
  } else {
    // 使用提供的偏移量
    applyOffsetCorrection(offsetX, offsetY)
  }

  function applyOffsetCorrection(correctionX: number, correctionY: number) {
    console.log(`🎯 应用偏移修正: X=${correctionX.toFixed(2)}, Y=${correctionY.toFixed(2)}`)

    let fixedCount = 0

    renderedDivs.forEach((div: Element) => {
      const htmlDiv = div as HTMLElement
      const currentX = parseFloat(htmlDiv.style.left) || 0
      const currentY = parseFloat(htmlDiv.style.top) || 0

      // 应用偏移修正
      htmlDiv.style.left = `${currentX + correctionX}px`
      htmlDiv.style.top = `${currentY + correctionY}px`

      fixedCount++
    })

    console.log(`✅ 已修复 ${fixedCount} 个文本元素的位置`)
    ElMessage.success(`智能对齐修复完成，调整了 ${fixedCount} 个文本元素`)

    // 重新运行诊断验证修复效果
    setTimeout(() => {
      console.log('🔍 验证修复效果...')
      diagnoseTextLayerAlignment()
    }, 1000)
  }
}

// 重置文本层位置到原始状态
const resetTextLayerAlignment = () => {
  console.log('🔄 重置文本层到原始位置...')

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  // 重新渲染文本层
  renderTextLayer(currentPage).then(() => {
    console.log('✅ 文本层已重置到原始位置')
    ElMessage.success('文本层位置已重置')
  }).catch((error) => {
    console.error('❌ 重置文本层失败:', error)
    ElMessage.error('重置文本层失败')
  })
}

// 查找Canvas元素的通用函数
const findCanvasElement = (pageNumber: number): HTMLCanvasElement | null => {
  // 尝试多种选择器策略
  const selectors = [
    `canvas[data-page-number="${pageNumber}"]`,
    `.pdf-page[data-page-number="${pageNumber}"]`,
    `#pdf-canvas-${pageNumber}`,
    `.pdf-page-container[data-page-number="${pageNumber}"] canvas`,
    `.pdf-page-container:nth-child(${pageNumber}) canvas`
  ]

  for (const selector of selectors) {
    const element = document.querySelector(selector) as HTMLCanvasElement
    if (element && element.tagName === 'CANVAS') {
      console.log(`✅ 找到Canvas元素，使用选择器: ${selector}`)
      return element
    }
  }

  // 如果都没找到，输出调试信息
  console.log('🔍 Canvas查找失败，页面中的所有Canvas元素:')
  const allCanvases = document.querySelectorAll('canvas')
  allCanvases.forEach((canvas, index) => {
    console.log(`Canvas[${index}]:`, {
      className: canvas.className,
      dataPageNumber: canvas.dataset.pageNumber,
      id: canvas.id,
      width: canvas.width,
      height: canvas.height,
      parentElement: canvas.parentElement?.className
    })
  })

  console.log('🔍 页面中的所有页面容器:')
  const allContainers = document.querySelectorAll('[data-page-number], .pdf-page-container, .pdf-page')
  allContainers.forEach((container, index) => {
    console.log(`Container[${index}]:`, {
      className: container.className,
      dataPageNumber: container.getAttribute('data-page-number'),
      id: container.id,
      hasCanvas: !!container.querySelector('canvas')
    })
  })

  return null
}

// 深度分析Canvas与TextLayer的对齐问题
const analyzeCanvasTextLayerAlignment = () => {
  console.log('🔍 开始深度分析Canvas与TextLayer对齐问题...')

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  // 使用通用Canvas查找函数
  const canvasElement = findCanvasElement(currentPage)
  const textLayerDiv = textLayerData.textLayerDiv

  if (!canvasElement) {
    ElMessage.error(`找不到第${currentPage}页的Canvas元素，请查看控制台调试信息`)
    return
  }

  // 获取容器信息
  const canvasRect = canvasElement.getBoundingClientRect()
  const textLayerRect = textLayerDiv.getBoundingClientRect()
  const pageContainer = canvasElement.parentElement
  const containerRect = pageContainer?.getBoundingClientRect()

  console.log('📊 容器对齐分析:', {
    Canvas位置: {
      left: canvasRect.left.toFixed(2),
      top: canvasRect.top.toFixed(2),
      width: canvasRect.width.toFixed(2),
      height: canvasRect.height.toFixed(2)
    },
    TextLayer位置: {
      left: textLayerRect.left.toFixed(2),
      top: textLayerRect.top.toFixed(2),
      width: textLayerRect.width.toFixed(2),
      height: textLayerRect.height.toFixed(2)
    },
    容器偏移: {
      X: (textLayerRect.left - canvasRect.left).toFixed(2),
      Y: (textLayerRect.top - canvasRect.top).toFixed(2)
    },
    尺寸差异: {
      宽度差: (textLayerRect.width - canvasRect.width).toFixed(2),
      高度差: (textLayerRect.height - canvasRect.height).toFixed(2)
    }
  })

  // 分析viewport和缩放信息
  pdfDocument.value.getPage(currentPage).then((pageObj: any) => {
    const viewport = pageObj.getViewport({ scale: scale.value })

    console.log('🎯 Viewport详细分析:', {
      viewport尺寸: `${viewport.width.toFixed(2)} × ${viewport.height.toFixed(2)}`,
      当前缩放: scale.value,
      Canvas实际尺寸: `${canvasElement.width} × ${canvasElement.height}`,
      Canvas显示尺寸: `${canvasRect.width.toFixed(2)} × ${canvasRect.height.toFixed(2)}`,
      设备像素比: window.devicePixelRatio,
      viewport变换: viewport.transform
    })

    // 检查关键文本项的位置
    const textItems = textLayerData.textContent.items
    const renderedDivs = textLayerDiv.querySelectorAll('div')

    // 查找标题文本 "招标文件示范文本"
    const titleItems = textItems.filter((item: any) =>
      item.str && (item.str.includes('招标') || item.str.includes('示范') || item.str.includes('文本'))
    )

    console.log('🎯 关键文本项分析:', {
      总文本项: textItems.length,
      标题相关项: titleItems.length,
      渲染div数: renderedDivs.length
    })

    titleItems.forEach((item: any, index: number) => {
      const itemIndex = textItems.indexOf(item)
      const correspondingDiv = renderedDivs[itemIndex] as HTMLElement

      if (correspondingDiv) {
        const divRect = correspondingDiv.getBoundingClientRect()
        const relativeToCanvas = {
          x: divRect.left - canvasRect.left,
          y: divRect.top - canvasRect.top
        }

        console.log(`📍 标题文本[${index}] "${item.str}" 位置分析:`, {
          PDF坐标: { x: item.transform[4].toFixed(2), y: item.transform[5].toFixed(2) },
          HTML样式: {
            left: correspondingDiv.style.left,
            top: correspondingDiv.style.top
          },
          实际渲染位置: {
            相对页面: { x: divRect.left.toFixed(2), y: divRect.top.toFixed(2) },
            相对Canvas: { x: relativeToCanvas.x.toFixed(2), y: relativeToCanvas.y.toFixed(2) }
          },
          字体信息: {
            fontSize: correspondingDiv.style.fontSize,
            fontFamily: correspondingDiv.style.fontFamily,
            transform: correspondingDiv.style.transform
          }
        })
      }
    })
  })

  ElMessage.info('Canvas与TextLayer对齐分析完成，请查看控制台详细输出')
}

// 验证标准PDF.js实现的对齐效果
const validateStandardAlignment = () => {
  console.log('🎯 验证标准PDF.js文本层对齐效果...')

  // 先进行深度分析
  analyzeCanvasTextLayerAlignment()

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  const textItems = textLayerData.textContent.items
  const textLayerDiv = textLayerData.textLayerDiv
  const renderedDivs = textLayerDiv.querySelectorAll('div')

  console.log('📊 标准实现验证结果:', {
    页码: currentPage,
    文本项数量: textItems.length,
    渲染div数量: renderedDivs.length,
    实现方式: 'PDF.js标准API',
    坐标转换: '由PDF.js内部处理'
  })

  ElMessage.success('标准PDF.js实现验证完成，请查看控制台输出')
}

// {{RIPER-5+SMART-6:
//   Action: "CSS-Conflict-Detection-Tool"
//   Task_ID: "CSS-CONFLICT-DETECTOR"
//   Timestamp: "2025-08-19T12:19:13+08:00"
//   Authoring_Subagent: "css-debugging-expert"
//   Principle_Applied: "深度CSS样式冲突分析原则"
//   Quality_Check: "全面检测CSS样式冲突和覆盖问题。"
// }}

// CSS样式冲突深度检测工具
const detectCSSConflicts = () => {
  console.log('🔍 开始CSS样式冲突深度检测...')

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  const canvasElement = findCanvasElement(currentPage)
  const textLayerDiv = textLayerData.textLayerDiv

  if (!canvasElement) {
    ElMessage.error('找不到Canvas元素')
    return
  }

  // 获取计算后的样式
  const canvasComputedStyle = window.getComputedStyle(canvasElement)
  const textLayerComputedStyle = window.getComputedStyle(textLayerDiv)

  console.log('📊 Canvas计算后样式分析:', {
    position: canvasComputedStyle.position,
    left: canvasComputedStyle.left,
    top: canvasComputedStyle.top,
    width: canvasComputedStyle.width,
    height: canvasComputedStyle.height,
    zIndex: canvasComputedStyle.zIndex,
    transform: canvasComputedStyle.transform,
    margin: canvasComputedStyle.margin,
    padding: canvasComputedStyle.padding,
    border: canvasComputedStyle.border
  })

  console.log('📊 TextLayer计算后样式分析:', {
    position: textLayerComputedStyle.position,
    left: textLayerComputedStyle.left,
    top: textLayerComputedStyle.top,
    width: textLayerComputedStyle.width,
    height: textLayerComputedStyle.height,
    zIndex: textLayerComputedStyle.zIndex,
    transform: textLayerComputedStyle.transform,
    margin: textLayerComputedStyle.margin,
    padding: textLayerComputedStyle.padding,
    border: textLayerComputedStyle.border,
    opacity: textLayerComputedStyle.opacity
  })

  // 检测样式冲突
  const conflicts = []

  // 检查position冲突
  if (textLayerComputedStyle.position !== 'absolute') {
    conflicts.push({
      property: 'position',
      expected: 'absolute',
      actual: textLayerComputedStyle.position,
      severity: 'critical'
    })
  }

  // {{RIPER-5+SMART-6:
  //   Action: "Enhanced-Precision-Detection"
  //   Task_ID: "CSS-CONFLICT-PRECISION-UPGRADE"
  //   Timestamp: "2025-08-19T10:25:00+08:00"
  //   Authoring_Subagent: "css-conflict-resolver"
  //   Principle_Applied: "精确数值检测与getBoundingClientRect对比原则"
  //   Quality_Check: "增强尺寸冲突检测精度，同时检测CSS尺寸和实际显示尺寸。"
  // }}

  // 检查尺寸冲突 - 使用多种方法获取精确数据
  const canvasWidth = parseFloat(canvasComputedStyle.width)
  const canvasHeight = parseFloat(canvasComputedStyle.height)
  const textLayerWidth = parseFloat(textLayerComputedStyle.width)
  const textLayerHeight = parseFloat(textLayerComputedStyle.height)

  // 同时获取实际显示尺寸进行对比
  const canvasRect = canvasElement.getBoundingClientRect()
  const textLayerRect = textLayerDiv.getBoundingClientRect()

  console.log('📏 精确尺寸对比分析:', {
    Canvas: {
      CSS尺寸: `${canvasWidth.toFixed(3)}×${canvasHeight.toFixed(3)}px`,
      显示尺寸: `${canvasRect.width.toFixed(3)}×${canvasRect.height.toFixed(3)}px`
    },
    TextLayer: {
      CSS尺寸: `${textLayerWidth.toFixed(3)}×${textLayerHeight.toFixed(3)}px`,
      显示尺寸: `${textLayerRect.width.toFixed(3)}×${textLayerRect.height.toFixed(3)}px`
    }
  })

  // 使用显示尺寸进行冲突检测（这是用户实际看到的）
  const widthDiff = Math.abs(canvasRect.width - textLayerRect.width)
  const heightDiff = Math.abs(canvasRect.height - textLayerRect.height)

  if (widthDiff > 1) {
    conflicts.push({
      property: 'width',
      expected: `${canvasRect.width.toFixed(3)}px`,
      actual: `${textLayerRect.width.toFixed(3)}px`,
      difference: widthDiff.toFixed(3),
      severity: widthDiff > 50 ? 'high' : 'medium'
    })
  }

  if (heightDiff > 1) {
    conflicts.push({
      property: 'height',
      expected: `${canvasRect.height.toFixed(3)}px`,
      actual: `${textLayerRect.height.toFixed(3)}px`,
      difference: heightDiff.toFixed(3),
      severity: heightDiff > 50 ? 'high' : 'medium'
    })
  }

  // 检查位置冲突
  const canvasLeft = parseFloat(canvasComputedStyle.left) || 0
  const canvasTop = parseFloat(canvasComputedStyle.top) || 0
  const textLayerLeft = parseFloat(textLayerComputedStyle.left) || 0
  const textLayerTop = parseFloat(textLayerComputedStyle.top) || 0

  if (Math.abs(textLayerLeft) > 5) {
    conflicts.push({
      property: 'left',
      expected: '0px',
      actual: `${textLayerLeft}px`,
      severity: 'medium'
    })
  }

  if (Math.abs(textLayerTop) > 5) {
    conflicts.push({
      property: 'top',
      expected: '0px',
      actual: `${textLayerTop}px`,
      severity: 'medium'
    })
  }

  console.log('⚠️ 检测到的CSS冲突:', conflicts)

  // 分析父容器
  const canvasParent = canvasElement.parentElement
  const textLayerParent = textLayerDiv.parentElement

  console.log('🏗️ 容器结构分析:', {
    Canvas父容器: {
      tagName: canvasParent?.tagName,
      className: canvasParent?.className,
      position: canvasParent ? window.getComputedStyle(canvasParent).position : 'none'
    },
    TextLayer父容器: {
      tagName: textLayerParent?.tagName,
      className: textLayerParent?.className,
      position: textLayerParent ? window.getComputedStyle(textLayerParent).position : 'none'
    },
    是否同一父容器: canvasParent === textLayerParent
  })

  return {
    conflicts,
    canvasComputedStyle: Object.fromEntries(
      ['position', 'left', 'top', 'width', 'height', 'zIndex', 'transform'].map(prop =>
        [prop, canvasComputedStyle[prop]]
      )
    ),
    textLayerComputedStyle: Object.fromEntries(
      ['position', 'left', 'top', 'width', 'height', 'zIndex', 'transform', 'opacity'].map(prop =>
        [prop, textLayerComputedStyle[prop]]
      )
    )
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Auto-Fix-CSS-Conflicts"
//   Task_ID: "CSS-CONFLICT-AUTO-RESOLVER"
//   Timestamp: "2025-08-19T10:30:00+08:00"
//   Authoring_Subagent: "css-conflict-resolver"
//   Principle_Applied: "基于检测数据的自动修复原则"
//   Quality_Check: "根据CSS冲突检测结果，自动应用精确的修复方案。"
// }}

// 基于CSS冲突检测的自动修复函数
const autoFixCSSConflicts = () => {
  console.log('🎯 开始基于冲突检测的自动修复...')

  // 先检测冲突
  const detectionResult = detectCSSConflicts()
  if (!detectionResult || !detectionResult.conflicts) {
    ElMessage.warning('无法获取冲突检测结果')
    return
  }

  const { conflicts } = detectionResult
  if (conflicts.length === 0) {
    ElMessage.success('✅ 未检测到需要修复的CSS冲突')
    return
  }

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)
  const canvasElement = findCanvasElement(currentPage)

  if (!textLayerData || !canvasElement) {
    ElMessage.error('无法获取必要的元素进行修复')
    return
  }

  const textLayerDiv = textLayerData.textLayerDiv
  const canvasRect = canvasElement.getBoundingClientRect()

  console.log('🔧 应用基于检测结果的精确修复...')

  // 应用强制性修复，解决所有检测到的冲突
  const criticalFixes = {
    'position': 'absolute !important',
    'left': '0px !important',
    'top': '0px !important',
    'width': `${canvasRect.width}px !important`,
    'height': `${canvasRect.height}px !important`,
    'z-index': '10 !important',
    'overflow': 'hidden !important',
    'pointer-events': 'auto !important'
  }

  // 逐一应用修复
  Object.entries(criticalFixes).forEach(([property, value]) => {
    textLayerDiv.style.setProperty(property, value.replace(' !important', ''), 'important')
  })

  console.log(`✅ 自动修复完成，已解决${conflicts.length}个冲突:`, {
    修复的冲突: conflicts.map(c => `${c.property}: ${c.expected} (差异: ${c.difference || 'N/A'})`),
    应用的尺寸: `${canvasRect.width.toFixed(3)}×${canvasRect.height.toFixed(3)}px`
  })

  ElMessage.success(`🎯 自动修复完成！已解决${conflicts.length}个CSS冲突`)

  // 等待一下再次检测验证修复效果
  setTimeout(() => {
    console.log('🔍 验证修复效果...')
    const verificationResult = detectCSSConflicts()
    if (verificationResult && verificationResult.conflicts.length === 0) {
      ElMessage.success('✅ 修复验证成功：所有冲突已解决')
    } else {
      ElMessage.warning(`⚠️ 仍有${verificationResult?.conflicts.length || 0}个冲突未完全解决`)
    }
  }, 500)
}

// 基于控制台数据的精确对齐修复
const fixCanvasTextLayerAlignment = () => {
  console.log('🔧 开始基于数据分析的精确对齐修复...')

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  const canvasElement = findCanvasElement(currentPage)
  const textLayerDiv = textLayerData.textLayerDiv

  if (!canvasElement) {
    ElMessage.error('找不到Canvas元素，无法进行对齐修复')
    return
  }

  // 获取Canvas的实际位置和尺寸
  const canvasRect = canvasElement.getBoundingClientRect()
  const pageContainer = canvasElement.parentElement

  console.log('🎯 对齐修复前的状态:', {
    Canvas位置: {
      left: canvasRect.left.toFixed(2),
      top: canvasRect.top.toFixed(2),
      width: canvasRect.width.toFixed(2),
      height: canvasRect.height.toFixed(2)
    },
    TextLayer当前位置: {
      left: textLayerDiv.style.left,
      top: textLayerDiv.style.top,
      width: textLayerDiv.style.width,
      height: textLayerDiv.style.height
    }
  })

  // 关键修复：确保TextLayer与Canvas完全重叠
  // 1. 重置位置到容器原点
  textLayerDiv.style.position = 'absolute'
  textLayerDiv.style.left = '0px'
  textLayerDiv.style.top = '0px'

  // 2. 设置与Canvas相同的尺寸
  textLayerDiv.style.width = `${canvasRect.width}px`
  textLayerDiv.style.height = `${canvasRect.height}px`

  // 3. 确保在同一个容器中
  if (pageContainer && textLayerDiv.parentElement !== pageContainer) {
    console.log('🔄 将TextLayer移动到Canvas的父容器中')
    pageContainer.appendChild(textLayerDiv)
  }

  // 4. 设置正确的层级
  textLayerDiv.style.zIndex = '10'
  canvasElement.style.zIndex = '1'

  // 验证修复结果
  setTimeout(() => {
    const newTextLayerRect = textLayerDiv.getBoundingClientRect()
    const newCanvasRect = canvasElement.getBoundingClientRect()

    const offsetX = newTextLayerRect.left - newCanvasRect.left
    const offsetY = newTextLayerRect.top - newCanvasRect.top
    const widthDiff = newTextLayerRect.width - newCanvasRect.width
    const heightDiff = newTextLayerRect.height - newCanvasRect.height

    console.log('✅ 对齐修复后的状态:', {
      Canvas位置: {
        left: newCanvasRect.left.toFixed(2),
        top: newCanvasRect.top.toFixed(2),
        width: newCanvasRect.width.toFixed(2),
        height: newCanvasRect.height.toFixed(2)
      },
      TextLayer位置: {
        left: newTextLayerRect.left.toFixed(2),
        top: newTextLayerRect.top.toFixed(2),
        width: newTextLayerRect.width.toFixed(2),
        height: newTextLayerRect.height.toFixed(2)
      },
      对齐结果: {
        X偏移: offsetX.toFixed(2),
        Y偏移: offsetY.toFixed(2),
        宽度差: widthDiff.toFixed(2),
        高度差: heightDiff.toFixed(2),
        是否完美对齐: Math.abs(offsetX) < 1 && Math.abs(offsetY) < 1 && Math.abs(widthDiff) < 1 && Math.abs(heightDiff) < 1
      }
    })

    if (Math.abs(offsetX) < 1 && Math.abs(offsetY) < 1) {
      ElMessage.success('Canvas与TextLayer对齐修复成功！')
    } else {
      ElMessage.warning(`对齐修复部分成功，仍有偏移: X=${offsetX.toFixed(1)}, Y=${offsetY.toFixed(1)}`)
    }
  }, 100)
}

// {{RIPER-5+SMART-6:
//   Action: "Force-CSS-Override-Tool"
//   Task_ID: "FORCE-CSS-OVERRIDE"
//   Timestamp: "2025-08-19T12:19:13+08:00"
//   Authoring_Subagent: "css-debugging-expert"
//   Principle_Applied: "强制CSS样式覆盖原则"
//   Quality_Check: "使用!important强制覆盖所有冲突样式。"
// }}

// 强制性CSS修复工具 - 使用!important覆盖所有冲突
const forceFixCSSAlignment = () => {
  console.log('💪 开始强制性CSS修复...')

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  const canvasElement = findCanvasElement(currentPage)
  const textLayerDiv = textLayerData.textLayerDiv

  if (!canvasElement) {
    ElMessage.error('找不到Canvas元素')
    return
  }

  // 先检测冲突
  const conflictData = detectCSSConflicts()

  // 获取Canvas的实际尺寸
  const canvasRect = canvasElement.getBoundingClientRect()

  console.log('🔧 应用强制性CSS修复...')

  // 强制设置TextLayer样式，使用!important覆盖所有可能的冲突
  const forceStyles = {
    'position': 'absolute !important',
    'left': '0px !important',
    'top': '0px !important',
    'width': `${canvasRect.width}px !important`,
    'height': `${canvasRect.height}px !important`,
    'z-index': '10 !important',
    'pointer-events': 'auto !important',
    'user-select': 'text !important',
    'overflow': 'hidden !important',
    'transform': 'none !important',
    'margin': '0px !important',
    'padding': '0px !important',
    'border': 'none !important',
    'box-sizing': 'border-box !important'
  }

  // 应用强制样式
  Object.entries(forceStyles).forEach(([property, value]) => {
    textLayerDiv.style.setProperty(property.replace(/([A-Z])/g, '-$1').toLowerCase(), value.replace(' !important', ''), 'important')
  })

  // 确保Canvas的z-index较低
  canvasElement.style.setProperty('z-index', '1', 'important')
  canvasElement.style.setProperty('position', 'relative', 'important')

  // 确保父容器的定位上下文
  const pageContainer = canvasElement.parentElement
  if (pageContainer) {
    pageContainer.style.setProperty('position', 'relative', 'important')

    // 如果TextLayer不在正确的容器中，移动它
    if (textLayerDiv.parentElement !== pageContainer) {
      console.log('🔄 移动TextLayer到正确的容器')
      pageContainer.appendChild(textLayerDiv)
    }
  }

  // 验证修复结果
  setTimeout(() => {
    const newTextLayerRect = textLayerDiv.getBoundingClientRect()
    const newCanvasRect = canvasElement.getBoundingClientRect()

    const offsetX = newTextLayerRect.left - newCanvasRect.left
    const offsetY = newTextLayerRect.top - newCanvasRect.top
    const widthDiff = newTextLayerRect.width - newCanvasRect.width
    const heightDiff = newTextLayerRect.height - newCanvasRect.height

    console.log('✅ 强制修复后的验证结果:', {
      修复前冲突数: conflictData?.conflicts?.length || 0,
      Canvas位置: {
        left: newCanvasRect.left.toFixed(2),
        top: newCanvasRect.top.toFixed(2),
        width: newCanvasRect.width.toFixed(2),
        height: newCanvasRect.height.toFixed(2)
      },
      TextLayer位置: {
        left: newTextLayerRect.left.toFixed(2),
        top: newTextLayerRect.top.toFixed(2),
        width: newTextLayerRect.width.toFixed(2),
        height: newTextLayerRect.height.toFixed(2)
      },
      最终对齐结果: {
        X偏移: offsetX.toFixed(2),
        Y偏移: offsetY.toFixed(2),
        宽度差: widthDiff.toFixed(2),
        高度差: heightDiff.toFixed(2),
        完美对齐: Math.abs(offsetX) < 1 && Math.abs(offsetY) < 1 && Math.abs(widthDiff) < 1 && Math.abs(heightDiff) < 1
      }
    })

    if (Math.abs(offsetX) < 1 && Math.abs(offsetY) < 1 && Math.abs(widthDiff) < 1 && Math.abs(heightDiff) < 1) {
      ElMessage.success('🎯 强制修复成功！Canvas与TextLayer已完美对齐')
    } else {
      ElMessage.error(`❌ 强制修复失败，仍有偏移: X=${offsetX.toFixed(1)}, Y=${offsetY.toFixed(1)}`)

      // 如果强制修复仍然失败，提供调试信息
      console.error('🚨 强制修复失败，可能的原因:', {
        TextLayer父容器: textLayerDiv.parentElement?.className,
        Canvas父容器: canvasElement.parentElement?.className,
        TextLayer计算样式: window.getComputedStyle(textLayerDiv),
        建议: '请检查是否有更高优先级的CSS规则覆盖了!important声明'
      })
    }
  }, 200)
}

// {{RIPER-5+SMART-6:
//   Action: "DOM-Structure-Visualization-Tool"
//   Task_ID: "DOM-STRUCTURE-VISUALIZER"
//   Timestamp: "2025-08-19T12:19:13+08:00"
//   Authoring_Subagent: "dom-structure-expert"
//   Principle_Applied: "DOM结构可视化分析原则"
//   Quality_Check: "提供清晰的DOM层级结构分析。"
// }}

// DOM结构可视化分析工具
const visualizeDOMStructure = () => {
  console.log('🏗️ 开始DOM结构可视化分析...')

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  const canvasElement = findCanvasElement(currentPage)
  const textLayerDiv = textLayerData.textLayerDiv

  if (!canvasElement) {
    ElMessage.error('找不到Canvas元素')
    return
  }

  // 分析DOM层级结构
  const analyzeDOMHierarchy = (element, depth = 0, maxDepth = 3) => {
    if (depth > maxDepth) return null

    const rect = element.getBoundingClientRect()
    const computedStyle = window.getComputedStyle(element)

    return {
      tagName: element.tagName,
      className: element.className,
      id: element.id,
      dataPageNumber: element.dataset?.pageNumber,
      position: computedStyle.position,
      zIndex: computedStyle.zIndex,
      dimensions: {
        width: rect.width.toFixed(2),
        height: rect.height.toFixed(2),
        left: rect.left.toFixed(2),
        top: rect.top.toFixed(2)
      },
      children: Array.from(element.children).map(child =>
        analyzeDOMHierarchy(child, depth + 1, maxDepth)
      ).filter(Boolean)
    }
  }

  // 分析Canvas的DOM结构
  const canvasHierarchy = analyzeDOMHierarchy(canvasElement.parentElement || canvasElement)

  // 分析TextLayer的DOM结构
  const textLayerHierarchy = analyzeDOMHierarchy(textLayerDiv.parentElement || textLayerDiv)

  console.log('📊 Canvas DOM结构分析:', canvasHierarchy)
  console.log('📊 TextLayer DOM结构分析:', textLayerHierarchy)

  // 查找共同祖先
  const findCommonAncestor = (element1, element2) => {
    const ancestors1 = []
    let current = element1
    while (current) {
      ancestors1.push(current)
      current = current.parentElement
    }

    current = element2
    while (current) {
      if (ancestors1.includes(current)) {
        return current
      }
      current = current.parentElement
    }
    return null
  }

  const commonAncestor = findCommonAncestor(canvasElement, textLayerDiv)

  console.log('🔗 容器关系分析:', {
    Canvas父容器: canvasElement.parentElement?.className,
    TextLayer父容器: textLayerDiv.parentElement?.className,
    共同祖先: commonAncestor?.className,
    是否同一父容器: canvasElement.parentElement === textLayerDiv.parentElement,
    Canvas在DOM中的位置: Array.from(canvasElement.parentElement?.children || []).indexOf(canvasElement),
    TextLayer在DOM中的位置: Array.from(textLayerDiv.parentElement?.children || []).indexOf(textLayerDiv)
  })

  // 检查层叠上下文
  const checkStackingContext = (element) => {
    const style = window.getComputedStyle(element)
    const hasStackingContext =
      style.position !== 'static' ||
      style.zIndex !== 'auto' ||
      parseFloat(style.opacity) < 1 ||
      style.transform !== 'none' ||
      style.filter !== 'none' ||
      style.isolation === 'isolate'

    return {
      hasStackingContext,
      position: style.position,
      zIndex: style.zIndex,
      opacity: style.opacity,
      transform: style.transform,
      isolation: style.isolation
    }
  }

  console.log('📚 层叠上下文分析:', {
    Canvas: checkStackingContext(canvasElement),
    TextLayer: checkStackingContext(textLayerDiv),
    Canvas父容器: canvasElement.parentElement ? checkStackingContext(canvasElement.parentElement) : null,
    TextLayer父容器: textLayerDiv.parentElement ? checkStackingContext(textLayerDiv.parentElement) : null
  })

  ElMessage.info('DOM结构分析完成，请查看控制台详细输出')
}

// {{RIPER-5+SMART-6:
//   Action: "Precise-Size-Fix-Based-On-Conflict-Data"
//   Task_ID: "PRECISE-SIZE-FIX"
//   Timestamp: "2025-08-19T12:25:00+08:00"
//   Authoring_Subagent: "css-debugging-expert"
//   Principle_Applied: "基于具体冲突数据的精确修复原则"
//   Quality_Check: "针对234px宽度差异和742px高度差异的精确修复。"
// }}

// 基于具体冲突数据的精确尺寸修复
const fixPreciseSizeConflicts = () => {
  console.log('🎯 开始基于冲突数据的精确尺寸修复...')

  const currentPage = currentVisiblePage.value
  const textLayerData = textLayers.value.get(currentPage)

  if (!textLayerData) {
    ElMessage.warning('当前页面没有文本层数据')
    return
  }

  const canvasElement = findCanvasElement(currentPage)
  const textLayerDiv = textLayerData.textLayerDiv

  if (!canvasElement) {
    ElMessage.error('找不到Canvas元素')
    return
  }

  // 获取Canvas的精确尺寸
  const canvasRect = canvasElement.getBoundingClientRect()
  const canvasComputedStyle = window.getComputedStyle(canvasElement)

  console.log('📏 修复前的精确尺寸对比:', {
    Canvas: {
      width: canvasRect.width,
      height: canvasRect.height,
      computedWidth: canvasComputedStyle.width,
      computedHeight: canvasComputedStyle.height
    },
    TextLayer当前: {
      width: textLayerDiv.style.width,
      height: textLayerDiv.style.height,
      computedWidth: window.getComputedStyle(textLayerDiv).width,
      computedHeight: window.getComputedStyle(textLayerDiv).height
    }
  })

  // 精确修复步骤1：确保容器结构正确
  const canvasParent = canvasElement.parentElement
  if (canvasParent && textLayerDiv.parentElement !== canvasParent) {
    console.log('🔄 修复容器结构：将TextLayer移动到Canvas的父容器')
    canvasParent.appendChild(textLayerDiv)
  }

  // 精确修复步骤2：强制设置精确尺寸
  const targetWidth = canvasRect.width
  const targetHeight = canvasRect.height

  console.log(`🎯 应用精确尺寸修复: ${targetWidth}×${targetHeight}`)

  // 使用最高优先级的样式设置
  textLayerDiv.style.cssText = `
    position: absolute !important;
    left: 0px !important;
    top: 0px !important;
    width: ${targetWidth}px !important;
    height: ${targetHeight}px !important;
    z-index: 10 !important;
    overflow: hidden !important;
    pointer-events: auto !important;
    user-select: text !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    transform: none !important;
    box-sizing: border-box !important;
    opacity: ${dynamicTextLayerOpacity.value} !important;
  `

  // 确保Canvas的层级正确
  canvasElement.style.setProperty('z-index', '1', 'important')
  canvasElement.style.setProperty('position', 'relative', 'important')

  // 验证修复结果
  setTimeout(() => {
    const newTextLayerRect = textLayerDiv.getBoundingClientRect()
    const newCanvasRect = canvasElement.getBoundingClientRect()

    const widthDiff = Math.abs(newTextLayerRect.width - newCanvasRect.width)
    const heightDiff = Math.abs(newTextLayerRect.height - newCanvasRect.height)
    const positionOffsetX = Math.abs(newTextLayerRect.left - newCanvasRect.left)
    const positionOffsetY = Math.abs(newTextLayerRect.top - newCanvasRect.top)

    console.log('✅ 精确修复后的验证结果:', {
      修复前差异: {
        宽度差异: '234.16px',
        高度差异: '742.386px'
      },
      修复后状态: {
        Canvas尺寸: `${newCanvasRect.width.toFixed(2)}×${newCanvasRect.height.toFixed(2)}`,
        TextLayer尺寸: `${newTextLayerRect.width.toFixed(2)}×${newTextLayerRect.height.toFixed(2)}`,
        宽度差异: `${widthDiff.toFixed(2)}px`,
        高度差异: `${heightDiff.toFixed(2)}px`,
        位置偏移X: `${positionOffsetX.toFixed(2)}px`,
        位置偏移Y: `${positionOffsetY.toFixed(2)}px`
      },
      修复效果: {
        尺寸完美匹配: widthDiff < 1 && heightDiff < 1,
        位置完美对齐: positionOffsetX < 1 && positionOffsetY < 1,
        整体修复成功: widthDiff < 1 && heightDiff < 1 && positionOffsetX < 1 && positionOffsetY < 1
      }
    })

    if (widthDiff < 1 && heightDiff < 1 && positionOffsetX < 1 && positionOffsetY < 1) {
      ElMessage.success('🎯 精确修复成功！尺寸和位置已完美对齐')
      console.log('🎉 修复成功总结:', {
        宽度差异: `从234.16px减少到${widthDiff.toFixed(2)}px`,
        高度差异: `从742.386px减少到${heightDiff.toFixed(2)}px`,
        修复效果: '完美对齐'
      })
    } else {
      ElMessage.warning(`⚠️ 修复部分成功，剩余差异: 宽度${widthDiff.toFixed(1)}px, 高度${heightDiff.toFixed(1)}px`)
    }
  }, 100)
}

// 搜索工具栏UI控制方法
const toggleSearchBar = () => {
  console.log('🔍 切换搜索栏调试:', {
    beforeToggle: showSearchBar.value,
    searchInputRefExists: !!searchInputRef.value,
    searchTextValue: searchText.value
  })

  showSearchBar.value = !showSearchBar.value
  if (showSearchBar.value) {
    // 显示搜索栏时，聚焦到搜索输入框
    nextTick(() => {
      console.log('🔍 聚焦搜索框调试:', {
        searchInputRefExists: !!searchInputRef.value,
        inputElement: searchInputRef.value?.$el,
        inputDisabled: searchInputRef.value?.disabled,
        inputReadonly: searchInputRef.value?.readonly
      })

      if (searchInputRef.value) {
        try {
          searchInputRef.value.focus()
          console.log('✅ 搜索框聚焦成功')
        } catch (error) {
          console.error('❌ 搜索框聚焦失败:', error)
        }
      }
    })
  } else {
    // 隐藏搜索栏时，清除搜索结果
    clearSearchResults()
  }
}

const closeSearchBar = () => {
  showSearchBar.value = false
  clearSearchResults()
}

const handleSearchInput = debounce((value: string) => {
  // {{RIPER-5+SMART-6:
  //   Action: "Modified"
  //   Task_ID: "87d109c3-d879-494b-9ebf-431e93d2585e"
  //   Timestamp: "2025-08-18T16:54:22+08:00"
  //   Authoring_Subagent: "vue-frontend-expert"
  //   Principle_Applied: "调试优先原则"
  //   Quality_Check: "添加详细调试日志，系统性诊断搜索输入框问题。"
  // }}
  console.log('🔍 搜索输入调试:', {
    inputValue: value,
    searchTextRef: searchText.value,
    inputRefExists: !!searchInputRef.value,
    inputRefFocused: searchInputRef.value?.focused,
    showSearchBar: showSearchBar.value,
    pdfDocumentLoaded: !!pdfDocument.value,
    textLayersCount: textLayers.value.size,
    timestamp: new Date().toISOString()
  })
  console.log('搜索输入:', value)
  console.log('PDF文档状态:', !!pdfDocument.value)
  console.log('文本层数量:', textLayers.value.size)

  if (value.trim()) {
    // 根据是否启用高级搜索选择搜索方法
    if (showAdvancedSearch.value) {
      advancedSearch(value.trim(), searchOptions.value)
    } else {
      searchInPdf(value.trim())
    }
  } else {
    clearSearchResults()
  }
}, 300) // 300ms防抖

const handleSearchEnter = () => {
  if (searchText.value.trim()) {
    if (searchResults.value.length === 0) {
      // 根据是否启用高级搜索选择搜索方法
      if (showAdvancedSearch.value) {
        advancedSearch(searchText.value.trim(), searchOptions.value)
      } else {
        searchInPdf(searchText.value.trim())
      }
    } else {
      nextSearchResult()
    }
  }
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // {{RIPER-5+SMART-6:
  //   Action: "Modified"
  //   Task_ID: "87d109c3-d879-494b-9ebf-431e93d2585e"
  //   Timestamp: "2025-08-18T17:10:00+08:00"
  //   Authoring_Subagent: "vue-frontend-expert"
  //   Principle_Applied: "事件冲突避免原则"
  //   Quality_Check: "避免全局键盘事件干扰搜索输入框正常输入。"
  // }}

  // {{RIPER-5+SMART-6:
  //   Action: "Critical-Fix"
  //   Task_ID: "EMERGENCY-INPUT-FOCUS"
  //   Timestamp: "2025-08-18T17:17:35+08:00"
  //   Authoring_Subagent: "vue-frontend-expert"
  //   Principle_Applied: "事件冲突彻底避免原则"
  //   Quality_Check: "彻底解决搜索输入框事件冲突问题。"
  // }}

  // 检查当前焦点是否在输入框上，如果是则不处理快捷键
  const activeElement = document.activeElement
  const isInputFocused = activeElement && (
    activeElement.tagName === 'INPUT' ||
    activeElement.tagName === 'TEXTAREA' ||
    activeElement.contentEditable === 'true' ||
    activeElement.classList.contains('el-input__inner') ||
    // Element Plus特殊检查
    activeElement.closest('.el-input') ||
    activeElement.closest('.search-input') ||
    // 检查是否是搜索输入框
    activeElement === searchInputRef.value?.$el?.querySelector('input')
  )

  console.log('🔍 键盘事件调试:', {
    key: event.key,
    activeElement: activeElement?.tagName,
    activeElementClass: activeElement?.className,
    isInputFocused,
    searchInputRef: !!searchInputRef.value
  })

  // 如果输入框获得焦点，只处理特定的快捷键
  if (isInputFocused) {
    // 只允许Escape键关闭搜索框
    if (event.key === 'Escape') {
      if (showSearchBar.value) {
        closeSearchBar()
        event.preventDefault()
      }
    }
    // 其他键盘事件不处理，让输入框正常工作
    return
  }

  // 检查是否启用键盘快捷键
  if (!featureConfig.value.enableKeyboardShortcuts) {
    return
  }

  // Ctrl+F 或 Cmd+F 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
    event.preventDefault()
    if (featureConfig.value.enableSearch && !showSearchBar.value) {
      toggleSearchBar()
    }
  }
  // Ctrl+Shift+F 打开高级搜索
  else if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F') {
    event.preventDefault()
    if (featureConfig.value.enableSearch) {
      toggleAdvancedSearch()
    }
  }
  // Escape 关闭搜索或面板
  else if (event.key === 'Escape') {
    if (showConfigPanel.value) {
      showConfigPanel.value = false
    } else if (showAdvancedSearch.value) {
      showAdvancedSearch.value = false
    } else if (showSearchBar.value) {
      closeSearchBar()
    } else if (showSidebar.value) {
      closeSidebar()
    }
  }
  // F3 或 Enter 搜索下一个
  else if (event.key === 'F3' || (event.key === 'Enter' && showSearchBar.value)) {
    if (searchResults.value.length > 0) {
      event.preventDefault()
      nextSearchResult()
    }
  }
  // Shift+F3 搜索上一个
  else if (event.key === 'F3' && event.shiftKey) {
    if (searchResults.value.length > 0) {
      event.preventDefault()
      prevSearchResult()
    }
  }
  // Ctrl+B 切换侧边栏
  else if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
    event.preventDefault()
    if (featureConfig.value.enableOutline || featureConfig.value.enableThumbnails) {
      toggleSidebar()
    }
  }
  // Ctrl+Shift+A 切换注释显示
  else if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
    event.preventDefault()
    if (featureConfig.value.enableAnnotations) {
      toggleAnnotations()
    }
  }
  // Ctrl+, 打开配置面板
  else if ((event.ctrlKey || event.metaKey) && event.key === ',') {
    event.preventDefault()
    toggleConfigPanel()
  }
}

// PDF文档大纲导航功能实现
const loadDocumentOutline = async () => {
  if (!pdfDocument.value) {
    console.warn('PDF文档未加载，无法获取大纲')
    return
  }

  try {
    console.log('开始获取PDF文档大纲...')
    const outline = await pdfDocument.value.getOutline()

    if (outline && outline.length > 0) {
      console.log('PDF文档大纲获取成功:', outline)
      documentOutline.value = processOutlineData(outline)
      filteredOutline.value = documentOutline.value
      console.log('处理后的大纲数据:', documentOutline.value)
    } else {
      console.log('PDF文档没有大纲信息')
      documentOutline.value = []
      filteredOutline.value = []
    }
  } catch (error) {
    console.error('获取PDF文档大纲失败:', error)
    documentOutline.value = []
    filteredOutline.value = []
  }
}

// 处理大纲数据，添加层级和ID
const processOutlineData = (outline: any[], level: number = 0, parentId: string = ''): PdfOutlineItem[] => {
  return outline.map((item, index) => {
    const id = parentId ? `${parentId}-${index}` : `${index}`
    const processedItem: PdfOutlineItem = {
      id,
      title: item.title || '未命名',
      bold: item.bold || false,
      italic: item.italic || false,
      color: item.color || [0, 0, 0],
      dest: item.dest,
      url: item.url,
      newWindow: item.newWindow,
      unsafeUrl: item.unsafeUrl,
      action: item.action,
      level,
      count: item.count
    }

    // 递归处理子项
    if (item.items && item.items.length > 0) {
      processedItem.items = processOutlineData(item.items, level + 1, id)
    }

    return processedItem
  })
}

// 大纲项点击处理
const handleOutlineClick = async (item: PdfOutlineItem) => {
  if (!pdfDocument.value) return

  try {
    console.log('点击大纲项:', item.title, item)

    if (item.dest) {
      // 处理内部链接
      let dest = item.dest
      if (typeof dest === 'string') {
        dest = await pdfDocument.value.getDestination(dest)
      }

      if (dest && dest.length > 0) {
        const pageRef = dest[0]
        const pageIndex = await pdfDocument.value.getPageIndex(pageRef)
        const targetPage = pageIndex + 1 // PDF.js页面索引从0开始，UI显示从1开始

        console.log(`跳转到第 ${targetPage} 页`)
        scrollToPage(targetPage)
      }
    } else if (item.url) {
      // 处理外部链接
      if (item.newWindow) {
        window.open(item.url, '_blank')
      } else {
        window.location.href = item.url
      }
    }
  } catch (error) {
    console.error('处理大纲项点击失败:', error)
    ElMessage.error('跳转失败，请重试')
  }
}

// 大纲搜索过滤
const filterOutline = (searchText: string) => {
  if (!searchText.trim()) {
    filteredOutline.value = documentOutline.value
    return
  }

  const filterItems = (items: PdfOutlineItem[]): PdfOutlineItem[] => {
    const filtered: PdfOutlineItem[] = []

    for (const item of items) {
      const matchesSearch = item.title.toLowerCase().includes(searchText.toLowerCase())
      let filteredChildren: PdfOutlineItem[] = []

      if (item.items) {
        filteredChildren = filterItems(item.items)
      }

      // 如果当前项匹配或有匹配的子项，则包含此项
      if (matchesSearch || filteredChildren.length > 0) {
        filtered.push({
          ...item,
          items: filteredChildren.length > 0 ? filteredChildren : item.items
        })
      }
    }

    return filtered
  }

  filteredOutline.value = filterItems(documentOutline.value)
}

// 侧边栏控制
const toggleSidebar = () => {
  showSidebar.value = !showSidebar.value
}

const closeSidebar = () => {
  showSidebar.value = false
}

const switchSidebarTab = (tab: 'outline' | 'thumbnails' | 'bookmarks') => {
  sidebarActiveTab.value = tab
  if (!showSidebar.value) {
    showSidebar.value = true
  }

  // 如果切换到缩略图标签，开始生成缩略图
  if (tab === 'thumbnails' && !thumbnailsGenerated.value) {
    generateAllThumbnails()
  }
}

// PDF缩略图功能实现
const generateThumbnail = async (pageNum: number): Promise<string | null> => {
  if (thumbnails.value.has(pageNum)) {
    return thumbnails.value.get(pageNum)!
  }

  if (thumbnailsLoading.value.has(pageNum)) {
    // 如果正在加载，等待加载完成
    return new Promise((resolve) => {
      const checkLoading = () => {
        if (!thumbnailsLoading.value.has(pageNum)) {
          resolve(thumbnails.value.get(pageNum) || null)
        } else {
          setTimeout(checkLoading, 100)
        }
      }
      checkLoading()
    })
  }

  if (!pdfDocument.value || !documentContent.value) {
    console.warn(`无法生成第 ${pageNum} 页缩略图：PDF文档或数据未加载`)
    return null
  }

  try {
    console.log(`开始生成第 ${pageNum} 页缩略图`)
    thumbnailsLoading.value.add(pageNum)

    // 使用pdf-utils.ts中的pdfToImage函数生成缩略图
    const thumbnailUrl = await pdfToImage(documentContent.value, pageNum, thumbnailScale.value)

    thumbnails.value.set(pageNum, thumbnailUrl)
    console.log(`第 ${pageNum} 页缩略图生成完成`)

    return thumbnailUrl
  } catch (error) {
    console.error(`第 ${pageNum} 页缩略图生成失败:`, error)
    return null
  } finally {
    thumbnailsLoading.value.delete(pageNum)
  }
}

// 批量生成缩略图（懒加载策略）
const generateAllThumbnails = async () => {
  if (thumbnailsGenerated.value || !pdfDocument.value) {
    return
  }

  console.log('开始批量生成缩略图...')
  thumbnailsGenerated.value = true

  // 分批生成，避免阻塞UI
  const batchSize = 5
  for (let i = 1; i <= totalPages.value; i += batchSize) {
    const batch = []
    for (let j = i; j < Math.min(i + batchSize, totalPages.value + 1); j++) {
      batch.push(generateThumbnail(j))
    }

    await Promise.all(batch)

    // 让出控制权，避免阻塞UI
    await new Promise(resolve => setTimeout(resolve, 10))
  }

  console.log('所有缩略图生成完成')
}

// 缩略图点击跳转
const handleThumbnailClick = (pageNum: number) => {
  console.log(`点击缩略图，跳转到第 ${pageNum} 页`)
  scrollToPage(pageNum)
}

// 获取缩略图尺寸样式
const getThumbnailSizeClass = () => {
  return `thumbnail-${thumbnailSize.value}`
}

// 调整缩略图尺寸
const changeThumbnailSize = (size: 'small' | 'medium' | 'large') => {
  thumbnailSize.value = size
}

// 渲染当前页面（保留用于兼容）
const renderCurrentPage = async () => {
  if (!pdfDocument.value) {
    console.warn('PDF 文档未加载')
    return
  }

  // 等待容器引用可用，最多重试 3 次
  let retryCount = 0
  while (!pagesContainerRef.value && retryCount < 3) {
    console.log(`等待容器引用可用，重试 ${retryCount + 1}/3`)
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))
    retryCount++
  }

  if (!pagesContainerRef.value) {
    console.error('PDF 容器引用未找到，检查状态:', {
      loading: loading.value,
      error: error.value,
      hasDocument: !!pdfDocument.value
    })
    return
  }

  console.log('开始渲染页面，容器状态:', {
    containerExists: !!pagesContainerRef.value,
    containerChildren: pagesContainerRef.value?.children.length || 0,
    currentPage: currentPage.value,
    totalPages: totalPages.value
  })

  try {
    // 清空容器，避免重复渲染累积
    if (pagesContainerRef.value) {
      pagesContainerRef.value.innerHTML = ''
    }

    // 严格的页码验证
    const pageNum = Number(currentPage.value)
    const totalPagesNum = Number(totalPages.value)

    console.log('页码验证:', pageNum, '总页数:', totalPagesNum)

    if (!Number.isInteger(pageNum) || pageNum < 1) {
      console.error('无效的页码:', pageNum)
      currentPage.value = 1
      return
    }

    if (pageNum > totalPagesNum) {
      console.error('页码超出范围:', pageNum, '>', totalPagesNum)
      currentPage.value = totalPagesNum
      return
    }

    let page: any
    try {
      console.log('调用 getPage 方法，页码:', pageNum)

      // 使用验证过的页码调用 getPage (已通过 markRaw 避免私有字段问题)
      page = await pdfDocument.value.getPage(pageNum)
      console.log('成功获取页面:', typeof page)
    } catch (pageError) {
      console.error('获取页面失败:', pageError)
      console.error('错误详情:', {
        message: pageError.message,
        stack: pageError.stack,
        name: pageError.name,
        requestedPage: currentPage.value,
        totalPages: totalPages.value
      })

      // 检查是否是私有字段访问错误 (理论上已通过 markRaw 解决)
      if (
        pageError.message.includes('private field') ||
        pageError.message.includes('Cannot read from private') ||
        pageError.message.includes('__privateGet') ||
        pageError.message.includes('__accessCheck')
      ) {
        ElMessage.error('PDF 文档渲染出现问题，请检查 PDF.js 版本兼容性')
        console.warn('私有字段错误仍然存在，可能需要降级 PDF.js 版本')
        return
      }

      // 其他类型的错误
      throw pageError
    }

    const viewport = page.getViewport({ scale: scale.value })

    // 创建canvas
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')

    if (!context) {
      throw new Error('无法获取Canvas上下文')
    }

    canvas.height = viewport.height
    canvas.width = viewport.width
    canvas.className = 'pdf-page'

    console.log('Canvas 创建完成:', {
      width: canvas.width,
      height: canvas.height,
      scale: scale.value
    })

    // 渲染页面
    const renderContext = {
      canvasContext: context,
      viewport: viewport
    }

    console.log('开始渲染 PDF 页面...')

    // 添加渲染任务管理
    const renderTask = page.render(renderContext)

    await renderTask.promise

    console.log('PDF 页面渲染完成，准备添加到 DOM')

    // 验证容器存在
    if (!pagesContainerRef.value) {
      throw new Error('PDF 容器引用丢失')
    }

    // 添加到容器
    pagesContainerRef.value.appendChild(canvas)

    console.log(`页面 ${currentPage.value} 成功添加到 DOM，容器子元素数量:`, pagesContainerRef.value.children.length)
  } catch (error) {
    console.error('渲染页面失败:', error)
    ElMessage.error(`渲染页面失败: ${error.message}`)
  }
}

// 工具栏功能 (移除手动 renderCurrentPage 调用，由 watch 自动处理)
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value += 0.2
    console.log('放大到:', scale.value)
  }
}

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value -= 0.2
    console.log('缩小到:', scale.value)
  }
}

const resetZoom = () => {
  scale.value = 1.2
  console.log('重置缩放到:', scale.value)
}

const prevPage = () => {
  if (currentVisiblePage.value > 1) {
    const targetPage = currentVisiblePage.value - 1
    scrollToPage(targetPage)
    console.log('切换到上一页:', targetPage)
  }
}

const nextPage = () => {
  if (currentVisiblePage.value < totalPages.value) {
    const targetPage = currentVisiblePage.value + 1
    scrollToPage(targetPage)
    console.log('切换到下一页:', targetPage)
  }
}

const goToVisiblePage = () => {
  const page = Number(currentVisiblePage.value)
  if (page >= 1 && page <= totalPages.value) {
    scrollToPage(page)
    console.log('跳转到页面:', page)
  } else {
    // 如果输入的页码无效，重置为有效范围
    currentVisiblePage.value = Math.max(1, Math.min(totalPages.value, page))
    ElMessage.warning(`页码超出范围，已调整为第 ${currentVisiblePage.value} 页`)
  }
}

// 注释：旧的 goToPage 函数，已被 goToVisiblePage 替代
// const goToPage = async () => {
//   const page = Number(currentPage.value)
//   if (page >= 1 && page <= totalPages.value) {
//     currentPage.value = page
//     await renderCurrentPage()
//   } else {
//     currentPage.value = Math.max(1, Math.min(totalPages.value, page))
//   }
// }

const downloadPdf = () => {
  if (documentContent.value) {
    try {
      const filename = `document_${documentId.value || Date.now()}.pdf`

      if (typeof documentContent.value === 'string') {
        // 如果是URL，创建下载链接
        const a = document.createElement('a')
        a.href = documentContent.value
        a.download = filename
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      } else {
        // 使用工具函数下载
        downloadPdf(documentContent.value, filename)
      }

      ElMessage.success('PDF下载成功')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('下载失败')
    }
  } else {
    ElMessage.error('没有可下载的PDF文件')
  }
}

const printPdf = () => {
  if (pagesContainerRef.value) {
    const canvas = pagesContainerRef.value.querySelector('canvas')
    if (canvas) {
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>打印PDF</title>
              <style>
                body { margin: 0; padding: 20px; }
                img { max-width: 100%; height: auto; }
              </style>
            </head>
            <body>
              <img src="${canvas.toDataURL()}" />
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.print()
      }
    }
  }
}

const refreshDocument = () => {
  loadDocument()
}

// 新增的工具栏功能方法
const goToPage = () => {
  const page = Number(pageInputValue.value)
  if (page >= 1 && page <= totalPages.value) {
    scrollToPage(page)
    console.log('跳转到页面:', page)
  } else {
    // 修正无效页码
    const correctedPage = Math.max(1, Math.min(totalPages.value, page))
    pageInputValue.value = correctedPage
    scrollToPage(correctedPage)
    ElMessage.warning(`页码超出范围，已调整为第 ${correctedPage} 页`)
  }
}

const syncPageInput = () => {
  pageInputValue.value = currentVisiblePage.value
}

// 适应模式功能
const fitToWidth = () => {
  fitMode.value = 'width'
  // 计算适应宽度的缩放比例
  if (pdfContentRef.value && pagesContainerRef.value) {
    const containerWidth = pdfContentRef.value.clientWidth - 40 // 减去padding
    const firstCanvas = pagesContainerRef.value.querySelector('canvas')
    if (firstCanvas) {
      const pageWidth = firstCanvas.width / window.devicePixelRatio
      const newScale = containerWidth / pageWidth
      scale.value = Math.max(0.5, Math.min(3, newScale))
      console.log('适应宽度，缩放比例:', scale.value)
    }
  }
}

const fitToPage = () => {
  fitMode.value = 'page'
  // 计算适应页面的缩放比例
  if (pdfContentRef.value && pagesContainerRef.value) {
    const containerWidth = pdfContentRef.value.clientWidth - 40
    const containerHeight = pdfContentRef.value.clientHeight - 40
    const firstCanvas = pagesContainerRef.value.querySelector('canvas')
    if (firstCanvas) {
      const pageWidth = firstCanvas.width / window.devicePixelRatio
      const pageHeight = firstCanvas.height / window.devicePixelRatio
      const scaleX = containerWidth / pageWidth
      const scaleY = containerHeight / pageHeight
      const newScale = Math.min(scaleX, scaleY)
      scale.value = Math.max(0.5, Math.min(3, newScale))
      console.log('适应页面，缩放比例:', scale.value)
    }
  }
}

const autoFit = () => {
  fitMode.value = 'auto'
  // 自动选择最佳适应模式
  if (pdfContentRef.value && pagesContainerRef.value) {
    const containerWidth = pdfContentRef.value.clientWidth - 40
    const containerHeight = pdfContentRef.value.clientHeight - 40
    const firstCanvas = pagesContainerRef.value.querySelector('canvas')
    if (firstCanvas) {
      const pageWidth = firstCanvas.width / window.devicePixelRatio
      const pageHeight = firstCanvas.height / window.devicePixelRatio
      const aspectRatio = pageWidth / pageHeight
      const containerAspectRatio = containerWidth / containerHeight

      // 根据宽高比选择适应模式
      if (aspectRatio > containerAspectRatio) {
        fitToWidth()
      } else {
        fitToPage()
      }
    }
  }
}

// 全屏功能
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    // 进入全屏
    if (viewerRef.value?.requestFullscreen) {
      viewerRef.value.requestFullscreen()
      isFullscreen.value = true
    }
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }
}

// 移动端触摸手势处理
const handleTouchStart = (event: TouchEvent) => {
  if (deviceType.value === 'desktop') return

  const touches = event.touches
  const currentTime = Date.now()

  if (touches.length === 1) {
    // 单指触摸
    const touch = touches[0]
    touchStartData.value = {
      x: touch.clientX,
      y: touch.clientY,
      scale: scale.value,
      timestamp: currentTime
    }

    // 检测双击
    if (currentTime - lastTouchTime.value < 300) {
      handleDoubleTap(touch.clientX, touch.clientY)
    }
    lastTouchTime.value = currentTime

  } else if (touches.length === 2) {
    // 双指触摸（缩放手势）
    const touch1 = touches[0]
    const touch2 = touches[1]
    const distance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )

    touchStartData.value = {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2,
      scale: scale.value,
      distance: distance,
      timestamp: currentTime
    }

    isGestureActive.value = true
    event.preventDefault() // 防止默认的缩放行为
  }
}

const handleTouchMove = (event: TouchEvent) => {
  if (deviceType.value === 'desktop' || !touchStartData.value) return

  const touches = event.touches

  if (touches.length === 2 && isGestureActive.value) {
    // 双指缩放
    const touch1 = touches[0]
    const touch2 = touches[1]
    const currentDistance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )

    if (touchStartData.value.distance) {
      const scaleChange = currentDistance / touchStartData.value.distance
      const newScale = touchStartData.value.scale * scaleChange

      // 限制缩放范围
      const clampedScale = Math.max(0.5, Math.min(3, newScale))
      if (clampedScale !== scale.value) {
        scale.value = clampedScale
        fitMode.value = 'custom'
      }
    }

    event.preventDefault()
  }
}

const handleTouchEnd = (event: TouchEvent) => {
  if (deviceType.value === 'desktop') return

  const touches = event.touches

  if (touches.length === 0) {
    // 所有手指离开
    if (touchStartData.value && !isGestureActive.value) {
      // 单指滑动检测（翻页）
      const touch = event.changedTouches[0]
      const deltaX = touch.clientX - touchStartData.value.x
      const deltaY = touch.clientY - touchStartData.value.y
      const deltaTime = Date.now() - touchStartData.value.timestamp

      // 检测水平滑动翻页
      if (Math.abs(deltaX) > 50 && Math.abs(deltaX) > Math.abs(deltaY) && deltaTime < 500) {
        if (deltaX > 0 && canGoToPrevious.value) {
          prevPage()
        } else if (deltaX < 0 && canGoToNext.value) {
          nextPage()
        }
      }
    }

    touchStartData.value = null
    isGestureActive.value = false
  }
}

const handleDoubleTap = (x: number, y: number) => {
  // 双击缩放
  if (scale.value <= 1.2) {
    // 放大到2倍
    scale.value = 2
    fitMode.value = 'custom'
  } else {
    // 恢复到适应宽度
    fitToWidth()
  }

  console.log('双击缩放:', scale.value)
}

// 设备类型检测和更新
const updateDeviceType = () => {
  if (typeof window === 'undefined') return

  isMobile.value = deviceType.value === 'mobile'

  // 根据设备类型自动调整工具栏
  if (shouldCollapseToolbar.value) {
    toolbarCollapsed.value = true
  }

  console.log('设备类型检测:', deviceType.value, '移动端:', isMobile.value)
}

// 窗口大小变化处理
const handleWindowResize = debounce(() => {
  updateDeviceType()

  // 移动端自动适应宽度
  if (deviceType.value === 'mobile' && fitMode.value === 'auto') {
    nextTick(() => {
      fitToWidth()
    })
  }

  console.log('窗口大小变化，设备类型:', deviceType.value)
}, 300)

// 切换工具栏折叠状态
const toggleToolbar = () => {
  toolbarCollapsed.value = !toolbarCollapsed.value
}

// 滚动监听函数，检测当前可见页面并触发懒加载
const handleScroll = () => {
  if (!pdfContentRef.value || !pagesContainerRef.value) {
    return
  }

  const container = pdfContentRef.value
  const containerRect = container.getBoundingClientRect()
  const containerTop = containerRect.top
  const containerHeight = containerRect.height
  const containerCenter = containerTop + containerHeight / 2

  // 懒加载模式：触发可视区域渲染
  if (isLazyLoadingEnabled.value) {
    renderVisiblePages()
  }

  // 获取所有页面元素（支持懒加载和传统模式）
  const pages = pagesContainerRef.value.querySelectorAll('.pdf-page, .pdf-page-container')
  let visiblePage = 1

  // 找到最接近容器中心的页面
  let minDistance = Infinity

  pages.forEach((page: HTMLElement, index: number) => {
    const pageRect = page.getBoundingClientRect()
    const pageCenter = pageRect.top + pageRect.height / 2
    const distance = Math.abs(pageCenter - containerCenter)

    // 检查页面是否在可视区域内
    const isVisible = pageRect.bottom > containerTop && pageRect.top < containerTop + containerHeight

    if (isVisible && distance < minDistance) {
      minDistance = distance
      visiblePage = index + 1
    }
  })

  // 更新当前可见页码
  if (visiblePage !== currentVisiblePage.value) {
    currentVisiblePage.value = visiblePage
    console.log('当前可见页面:', visiblePage)

    // 记录内存使用情况（仅在懒加载模式下）
    if (isLazyLoadingEnabled.value) {
      const memoryUsage = getMemoryUsage()
      if (memoryUsage) {
        console.log('内存使用情况:', `${memoryUsage.used}MB / ${memoryUsage.total}MB`)
      }
    }
  }
}

// 使用优化的节流函数，避免滚动事件过于频繁
const throttledHandleScroll = throttle(handleScroll, 100)

// 跳转到指定页面
const scrollToPage = (pageNumber: number) => {
  if (!pagesContainerRef.value || pageNumber < 1 || pageNumber > totalPages.value) {
    return
  }

  // 优先查找页面容器（懒加载模式）
  let targetElement = pagesContainerRef.value.querySelector(`[data-page-number="${pageNumber}"]`) as HTMLElement

  // 如果没找到，查找canvas元素（传统模式）
  if (!targetElement) {
    const pages = pagesContainerRef.value.querySelectorAll('.pdf-page, canvas[data-page-number]')
    targetElement = pages[pageNumber - 1] as HTMLElement
  }

  if (targetElement && pdfContentRef.value) {
    // 计算目标页面相对于容器的位置
    const containerRect = pdfContentRef.value.getBoundingClientRect()
    const pageRect = targetElement.getBoundingClientRect()
    const scrollTop = pdfContentRef.value.scrollTop

    // 将页面滚动到视口中心位置
    const viewportHeight = containerRect.height
    const pageHeight = pageRect.height
    const centerOffset = (viewportHeight - pageHeight) / 2
    const targetScrollTop = scrollTop + pageRect.top - containerRect.top - Math.max(centerOffset, 20)

    // 平滑滚动到目标页面
    pdfContentRef.value.scrollTo({
      top: Math.max(0, targetScrollTop),
      behavior: 'smooth'
    })

    // 更新当前可见页面
    currentVisiblePage.value = pageNumber
    pageInputValue.value = pageNumber

    console.log(`滚动到第 ${pageNumber} 页`)
  }
}

// 精确滚动到搜索结果位置
const scrollToSearchResult = (result: PdfSearchMatch) => {
  // 先滚动到页面
  scrollToPage(result.pageIndex)

  // 等待滚动完成后，再精确定位到文本位置
  setTimeout(() => {
    const textLayerData = textLayers.value.get(result.pageIndex)
    if (textLayerData && textLayerData.textLayerDiv) {
      const textElements = textLayerData.textLayerDiv.querySelectorAll('span')
      for (const element of textElements) {
        if (element.textContent && element.textContent.includes(result.textContent)) {
          // 精确滚动到文本元素
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          })
          break
        }
      }
    }
  }, 500) // 等待页面滚动动画完成
}

// 监听文档变化
watch(
  documentId,
  newId => {
    if (newId && props.autoLoad) {
      loadDocument()
    }
  },
  { immediate: true }
)

// 注释：由于现在显示所有页面，不需要监听 currentPage 变化
// 页面跳转通过 scrollToPage 实现
// watch(currentPage, async (newPage, oldPage) => {
//   if (newPage !== oldPage && pdfDocument.value) {
//     console.log('页面切换:', oldPage, '->', newPage)
//     await nextTick()
//     await renderCurrentPage()
//   }
// })

// 监听缩放变化，重新渲染所有页面
watch(scale, async (newScale, oldScale) => {
  if (newScale !== oldScale && pdfDocument.value && !isRendering.value) {
    console.log('缩放变化:', oldScale, '->', newScale)

    // 如果不是适应模式，则设置为自定义模式
    if (fitMode.value !== 'width' && fitMode.value !== 'page' && fitMode.value !== 'auto') {
      fitMode.value = 'custom'
    }

    // 清理页面缓存（因为缩放比例变化）
    if (isLazyLoadingEnabled.value) {
      pdfPageCache.clear()
      renderedPages.value.clear()
      // 清理文本层缓存和搜索结果
      textLayers.value.clear()
      clearSearchResults()
      console.log('缓存已清理，准备重新渲染')
    }

    await nextTick()
    await renderAllPages()
  }
})

// 监听当前可见页面变化，同步页码输入框
watch(currentVisiblePage, (newPage) => {
  pageInputValue.value = newPage
})

// 监听大纲搜索文本变化
watch(outlineSearchText, (newText) => {
  filterOutline(newText)
})

// 全屏状态监听器
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
  console.log('全屏状态变化:', isFullscreen.value)
}

// 生命周期
onMounted(async () => {
  console.log('DocumentPdfViewer 组件已挂载')

  // {{RIPER-5+SMART-6:
  //   Action: "Global-Function-Exposure"
  //   Task_ID: "GLOBAL-FUNCTION-SETUP"
  //   Timestamp: "2025-08-19T12:19:13+08:00"
  //   Authoring_Subagent: "browser-devtools-expert"
  //   Principle_Applied: "全局调试工具暴露原则"
  //   Quality_Check: "提供完整的控制台调试工具集。"
  // }}

  // 将RIPER-5模式诊断和修复函数暴露到全局，方便控制台调用
  if (typeof window !== 'undefined') {
    // 基础工具
    window.findCanvasElement = findCanvasElement
    window.analyzeCanvasTextLayerAlignment = analyzeCanvasTextLayerAlignment

    // RIPER-5模式专业工具
    window.detectCSSConflicts = detectCSSConflicts
    window.fixPreciseSizeConflicts = fixPreciseSizeConflicts
    window.visualizeDOMStructure = visualizeDOMStructure
    window.forceFixCSSAlignment = forceFixCSSAlignment

    // 传统修复工具
    window.diagnoseTextLayerAlignment = diagnoseTextLayerAlignment
    window.fixTextLayerAlignment = fixTextLayerAlignment
    window.fixCanvasTextLayerAlignment = fixCanvasTextLayerAlignment
    window.resetTextLayerAlignment = resetTextLayerAlignment
    window.validateStandardAlignment = validateStandardAlignment

    console.log('🚀 RIPER-5模式文本层调试工具已加载，可在控制台使用：')
    console.log('')
    console.log('📊 RIPER-5专业调试工具:')
    console.log('  - detectCSSConflicts() // 检测CSS样式冲突')
    console.log('  - visualizeDOMStructure() // 可视化DOM结构')
    console.log('  - forceFixCSSAlignment() // 强制修复CSS对齐')
    console.log('')
    console.log('🔧 基础调试工具:')
    console.log('  - findCanvasElement(pageNumber) // 查找Canvas元素')
    console.log('  - analyzeCanvasTextLayerAlignment() // 深度分析对齐')
    console.log('  - fixCanvasTextLayerAlignment() // 精确对齐修复')
    console.log('')
    console.log('💡 建议调试流程:')
    console.log('  1. detectCSSConflicts() - 检测样式冲突')
    console.log('  2. visualizeDOMStructure() - 分析DOM结构')
    console.log('  3. forceFixCSSAlignment() - 强制修复')
  }

  // {{RIPER-5+SMART-6:
  //   Action: "PDF.js-Initialization-Check"
  //   Task_ID: "PDFJS-MOUNT-INIT"
  //   Timestamp: "2025-08-18T17:40:36+08:00"
  //   Authoring_Subagent: "lifecycle-expert"
  //   Principle_Applied: "组件初始化安全检查原则"
  //   Quality_Check: "确保PDF.js在组件使用前正确初始化。"
  // }}

  // PDF.js配置已在loadDocument中处理，跳过初始化检查
  console.log('✅ 组件初始化完成，PDF.js将在加载文档时配置')

  await nextTick()

  if (documentId.value && props.autoLoad) {
    loadDocument()
  }

  // 添加滚动监听器
  await nextTick() // 确保 DOM 已渲染
  if (pdfContentRef.value) {
    pdfContentRef.value.addEventListener('scroll', throttledHandleScroll)
    console.log('滚动监听器已添加')
  }

  // 添加全屏状态监听器
  document.addEventListener('fullscreenchange', handleFullscreenChange)

  // 初始化移动端检测
  updateDeviceType()

  // 添加窗口大小变化监听器
  window.addEventListener('resize', handleWindowResize)

  // 添加键盘快捷键支持
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 清理PDF文档
  if (pdfDocument.value) {
    pdfDocument.value.destroy()
  }

  // 移除滚动监听器
  if (pdfContentRef.value) {
    pdfContentRef.value.removeEventListener('scroll', throttledHandleScroll)
    console.log('滚动监听器已移除')
  }

  // 移除全屏监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange)

  // 移除窗口大小监听器
  window.removeEventListener('resize', handleWindowResize)

  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeydown)

  // 清理页面缓存
  pdfPageCache.clear()
  renderedPages.value.clear()
  console.log('页面缓存已清理')

  // 清理搜索相关缓存
  clearSearchResults()
  textLayers.value.clear()
  searchCache.value.clear()
  console.log('搜索缓存已清理')

  // 清理大纲相关数据
  documentOutline.value = []
  filteredOutline.value = []
  expandedOutlineKeys.value = []
  console.log('大纲数据已清理')

  // 清理缩略图相关数据
  thumbnails.value.clear()
  thumbnailsLoading.value.clear()
  thumbnailsGenerated.value = false
  console.log('缩略图数据已清理')

  // 清理注释层和高级功能数据
  annotationLayers.value.clear()
  annotationsLoading.value.clear()
  searchHistory.value = []
  showAdvancedSearch.value = false
  showConfigPanel.value = false
  console.log('高级功能数据已清理')

  // 清理定时器
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
})

// 暴露方法
defineExpose({
  loadDocument,
  loadPdfFromData,
  zoomIn,
  zoomOut,
  resetZoom,
  prevPage,
  nextPage,
  goToPage,
  goToVisiblePage,
  scrollToPage,
  downloadPdf,
  printPdf,
  fitToWidth,
  fitToPage,
  autoFit,
  toggleFullscreen,
  renderVisiblePages,
  initializePageContainers,
  toggleToolbar,
  updateDeviceType,
  searchInPdf,
  nextSearchResult,
  prevSearchResult,
  clearSearchResults,
  toggleSearchBar,
  closeSearchBar,
  loadDocumentOutline,
  handleOutlineClick,
  toggleSidebar,
  closeSidebar,
  switchSidebarTab,
  generateThumbnail,
  generateAllThumbnails,
  handleThumbnailClick,
  changeThumbnailSize,
  advancedSearch,
  toggleAdvancedSearch,
  toggleConfigPanel,
  updateFeatureConfig,
  resetFeatureConfig,
  toggleAnnotations,
  optimizePerformance,
  testTextLayerAlignment,
  addToSearchHistory,
  clearSearchHistory,
  searchFromHistory,
  currentVisiblePage: readonly(currentVisiblePage),
  isFullscreen: readonly(isFullscreen),
  fitMode: readonly(fitMode),
  isLazyLoadingEnabled: readonly(isLazyLoadingEnabled),
  renderedPages: readonly(renderedPages),
  visibleRange: readonly(visibleRange),
  deviceType: readonly(deviceType),
  isMobile: readonly(isMobile),
  toolbarCollapsed: readonly(toolbarCollapsed),
  searchText: readonly(searchText),
  searchResults: readonly(searchResults),
  currentSearchIndex: readonly(currentSearchIndex),
  textLayers: readonly(textLayers),
  documentOutline: readonly(documentOutline),
  showSidebar: readonly(showSidebar),
  sidebarActiveTab: readonly(sidebarActiveTab),
  filteredOutline: readonly(filteredOutline),
  thumbnails: readonly(thumbnails),
  thumbnailsLoading: readonly(thumbnailsLoading),
  thumbnailSize: readonly(thumbnailSize),
  thumbnailsGenerated: readonly(thumbnailsGenerated),
  annotationLayers: readonly(annotationLayers),
  showAnnotations: readonly(showAnnotations),
  showAdvancedSearch: readonly(showAdvancedSearch),
  searchHistory: readonly(searchHistory),
  showConfigPanel: readonly(showConfigPanel),
  featureConfig: readonly(featureConfig)
})
</script>

<style lang="scss" scoped>
.pdf-viewer-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;

  span {
    margin-top: 16px;
    font-size: 14px;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #f56c6c;

  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  span {
    margin-bottom: 16px;
    font-size: 14px;
  }
}

.pdf-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .page-input {
    width: 60px;

    :deep(.el-input__inner) {
      text-align: center;
    }
  }

  .page-info {
    padding: 0 8px;
    color: #666;
    font-size: 14px;
  }

  // 工具栏按钮组间距
  .ml-3 {
    margin-left: 12px;
  }
}

// 全屏模式样式
.pdf-viewer-container:fullscreen {
  background-color: #000;

  .pdf-toolbar {
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;

    .el-button {
      background-color: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      color: #fff;

      &:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }

      &.is-disabled {
        background-color: rgba(255, 255, 255, 0.05);
        color: rgba(255, 255, 255, 0.3);
      }
    }

    .page-info {
      color: #ccc;
    }
  }

  .pdf-content {
    background-color: #000;
  }
}

// 移动端响应式样式
@media (max-width: 768px) {
  .pdf-viewer-container {
    .pdf-toolbar {
      padding: 8px 12px;
      flex-wrap: wrap;
      gap: 8px;

      &.mobile {
        .toolbar-left {
          flex: 1;
          min-width: 0;

          .el-button-group {
            margin-right: 8px;
            margin-bottom: 0;

            .el-button {
              padding: 6px 8px;
              font-size: 12px;
            }
          }

          .page-input {
            width: 50px;

            .el-input__inner {
              font-size: 12px;
              text-align: center;
            }
          }

          .page-info {
            font-size: 12px;
            padding: 0 4px;
          }
        }

        .toolbar-right {
          .el-button {
            padding: 6px 8px;
            font-size: 12px;

            span {
              display: none; // 隐藏按钮文字，只显示图标
            }
          }
        }

        &.collapsed {
          .toolbar-left {
            .el-button-group:not(:first-child) {
              display: none;
            }
          }

          .toolbar-right {
            .el-button:not(:first-child) {
              display: none;
            }
          }
        }
      }
    }

    .pdf-content {
      padding: 10px;

      &.mobile {
        padding: 5px;

        // 移动端触摸优化
        touch-action: pan-x pan-y;
        -webkit-overflow-scrolling: touch;

        &.gesture-active {
          touch-action: none; // 手势操作时禁用默认滚动
        }
      }

      .pdf-pages-container {
        .pdf-page-container,
        .pdf-page {
          margin-bottom: 10px;
          box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }
      }
    }
  }
}

// 平板端样式
@media (min-width: 769px) and (max-width: 1024px) {
  .pdf-viewer-container {
    .pdf-toolbar {
      &.tablet {
        .toolbar-left {
          .el-button-group {
            .el-button {
              padding: 8px 12px;
            }
          }
        }
      }
    }

    .pdf-content {
      &.tablet {
        padding: 15px;
      }
    }
  }
}

// 小屏幕移动端样式
@media (max-width: 480px) {
  .pdf-viewer-container {
    .pdf-toolbar {
      padding: 6px 8px;

      .toolbar-left {
        .ml-3 {
          margin-left: 6px;
        }

        .el-button-group {
          margin-right: 6px;
        }
      }

      .toolbar-right {
        .el-button {
          min-width: 32px;
          padding: 6px;

          .el-icon {
            margin: 0;
          }
        }
      }
    }

    .pdf-content {
      padding: 5px;

      .pdf-pages-container {
        .pdf-page-container,
        .pdf-page {
          margin-bottom: 8px;
        }
      }
    }
  }
}

.pdf-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
  background-color: #f5f5f5;
}

.pdf-pages-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100%;

  .pdf-page {
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    background-color: #fff;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pdf-toolbar {
    flex-direction: column;
    gap: 12px;

    .toolbar-left,
    .toolbar-right {
      width: 100%;
      justify-content: center;
    }
  }

  .pdf-content {
    padding: 10px;
  }
}

// 打印样式
@media print {
  .pdf-toolbar {
    display: none;
  }

  .pdf-content {
    padding: 0;
    overflow: visible;
  }

  .pdf-pages-container {
    .pdf-page {
      margin-bottom: 0;
      box-shadow: none;
      page-break-after: always;
    }
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "fffe9572-35e9-4270-ae53-71f5ebdc44ae"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "frontend-vue-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF搜索工具栏样式，参考DocumentNavigator.vue设计。"
// }}

// PDF搜索工具栏样式
.pdf-search-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  transition: all 0.3s ease;

  .search-input-container {
    flex: 1;
    max-width: 300px;

    .search-input {
      width: 100%;
    }
  }

  .search-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #606266;
    font-size: 14px;

    .search-count {
      font-weight: 500;
      color: #409eff;
    }

    .el-button-group {
      .el-button {
        padding: 4px 8px;
      }
    }
  }

  .search-no-results {
    color: #f56c6c;
    font-size: 14px;
    font-weight: 500;
  }

  .search-actions {
    display: flex;
    align-items: center;
  }

  // 移动端适配
  &.mobile {
    padding: 8px 12px;
    gap: 8px;

    .search-input-container {
      max-width: none;
      flex: 1;
    }

    .search-info {
      font-size: 12px;
      gap: 6px;

      .el-button-group .el-button {
        padding: 2px 6px;
      }
    }
  }

  // 平板端适配
  &.tablet {
    padding: 10px 14px;
    gap: 10px;
  }
}

// TextLayer调试模式样式
.config-section {
  .opacity-value {
    margin-left: 8px;
    font-size: 12px;
    color: #666;
    font-weight: bold;
  }
}

// 全屏模式下的搜索工具栏样式
.pdf-viewer-container:fullscreen {
  .pdf-search-toolbar {
    background-color: rgba(0, 0, 0, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.2);

    .search-input-container .search-input {
      .el-input__wrapper {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);

        .el-input__inner {
          color: #fff;

          &::placeholder {
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .el-input__prefix {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }

    .search-info {
      color: #ccc;

      .search-count {
        color: #409eff;
      }

      .el-button {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #fff;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }

        &.is-disabled {
          background-color: rgba(255, 255, 255, 0.05);
          color: rgba(255, 255, 255, 0.3);
        }
      }
    }

    .search-no-results {
      color: #f78989;
    }
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "bb8c1cc5-efda-4000-b46c-ee5393c2741f"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-navigation-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF文档大纲侧边栏样式，参考DocumentNavigator.vue设计。"
// }}

// PDF侧边栏和大纲样式
.pdf-main-content {
  display: flex;
  flex: 1;
  overflow: hidden;

  &.with-sidebar {
    .pdf-content {
      flex: 1;
      margin-left: 0;
    }
  }
}

.pdf-sidebar {
  width: 300px;
  background-color: #f8f9fa;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .sidebar-header {
    position: relative;
    padding: 12px 16px 0;
    border-bottom: 1px solid #e4e7ed;

    .el-tabs {
      .el-tabs__header {
        margin: 0;
      }

      .el-tabs__nav-wrap {
        &::after {
          display: none;
        }
      }

      .el-tabs__item {
        padding: 0 12px;
        font-size: 14px;

        .el-icon {
          margin-right: 4px;
        }
      }
    }

    .sidebar-close-btn {
      position: absolute;
      top: 8px;
      right: 8px;
      padding: 4px;
    }
  }

  .sidebar-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  // 移动端适配
  &.mobile {
    width: 280px;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    .sidebar-header {
      padding: 8px 12px 0;
    }
  }

  // 平板端适配
  &.tablet {
    width: 320px;
  }
}

// 大纲面板样式
.outline-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .outline-search {
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .outline-tree-container {
    flex: 1;
    overflow: auto;
    padding: 8px 0;

    .outline-tree {
      .el-tree-node {
        .el-tree-node__content {
          padding: 4px 16px;
          height: auto;
          min-height: 32px;

          &:hover {
            background-color: #f0f9ff;
          }

          .outline-node {
            width: 100%;
            padding: 4px 0;
            cursor: pointer;

            &.bold {
              font-weight: bold;
            }

            &.italic {
              font-style: italic;
            }

            &.level-0 {
              font-size: 14px;
              font-weight: 600;
            }

            &.level-1 {
              font-size: 13px;
              padding-left: 16px;
            }

            &.level-2 {
              font-size: 12px;
              padding-left: 32px;
            }

            &.level-3 {
              font-size: 12px;
              padding-left: 48px;
            }

            .outline-title {
              display: block;
              line-height: 1.4;
              word-break: break-word;
            }
          }
        }

        &.is-current > .el-tree-node__content {
          background-color: #409eff;
          color: white;

          .outline-node {
            color: white;
          }
        }
      }
    }

    .no-outline,
    .no-search-results {
      padding: 20px;
      text-align: center;
    }
  }
}

// 全屏模式下的侧边栏样式
.pdf-viewer-container:fullscreen {
  .pdf-sidebar {
    background-color: rgba(0, 0, 0, 0.9);
    border-right-color: rgba(255, 255, 255, 0.2);

    .sidebar-header {
      border-bottom-color: rgba(255, 255, 255, 0.2);

      .el-tabs {
        .el-tabs__item {
          color: #ccc;

          &.is-active {
            color: #409eff;
          }

          &:hover {
            color: #fff;
          }
        }

        .el-tabs__active-bar {
          background-color: #409eff;
        }
      }

      .sidebar-close-btn {
        color: #ccc;

        &:hover {
          color: #fff;
          background-color: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .outline-panel {
      .outline-search {
        border-bottom-color: rgba(255, 255, 255, 0.2);

        .el-input__wrapper {
          background-color: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);

          .el-input__inner {
            color: #fff;

            &::placeholder {
              color: rgba(255, 255, 255, 0.6);
            }
          }

          .el-input__prefix {
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }

      .outline-tree-container {
        .outline-tree {
          .el-tree-node {
            .el-tree-node__content {
              &:hover {
                background-color: rgba(255, 255, 255, 0.1);
              }

              .outline-node {
                color: #ccc;

                .outline-title {
                  color: #ccc;
                }
              }
            }

            &.is-current > .el-tree-node__content {
              background-color: #409eff;

              .outline-node {
                color: white;

                .outline-title {
                  color: white;
                }
              }
            }
          }
        }

        .no-outline,
        .no-search-results {
          color: #ccc;
        }
      }
    }
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "2f86cbd6-164f-43b9-aceb-06c4b6c3f149"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "thumbnail-navigation-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF缩略图网格样式，支持响应式设计和多种尺寸。"
// }}

// PDF缩略图面板样式
.thumbnails-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .thumbnails-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #fafafa;

    .thumbnails-info {
      font-size: 14px;
      color: #606266;
    }

    .thumbnails-controls {
      .el-button-group {
        .el-button {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }
  }

  .thumbnails-scrollbar {
    flex: 1;

    .el-scrollbar__view {
      padding: 12px;
    }
  }

  .thumbnails-grid {
    display: grid;
    gap: 12px;

    // 小尺寸：4列
    &.thumbnail-small {
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;

      .thumbnail-item {
        .thumbnail-image-container {
          height: 60px;
        }

        .thumbnail-label {
          font-size: 11px;
          padding: 2px 4px;
        }
      }
    }

    // 中尺寸：3列（默认）
    &.thumbnail-medium {
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;

      .thumbnail-item {
        .thumbnail-image-container {
          height: 80px;
        }

        .thumbnail-label {
          font-size: 12px;
          padding: 4px 6px;
        }
      }
    }

    // 大尺寸：2列
    &.thumbnail-large {
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;

      .thumbnail-item {
        .thumbnail-image-container {
          height: 120px;
        }

        .thumbnail-label {
          font-size: 13px;
          padding: 6px 8px;
        }
      }
    }

    .thumbnail-item {
      display: flex;
      flex-direction: column;
      cursor: pointer;
      border: 2px solid transparent;
      border-radius: 6px;
      overflow: hidden;
      transition: all 0.2s ease;
      background-color: #fff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
        transform: translateY(-1px);
      }

      &.active {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

        .thumbnail-label {
          background-color: #409eff;
          color: white;
        }
      }

      &.loading {
        .thumbnail-image-container {
          background-color: #f5f7fa;
        }
      }

      .thumbnail-image-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fafafa;
        position: relative;
        overflow: hidden;

        .thumbnail-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
          display: block;
        }

        .thumbnail-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #409eff;

          .el-icon {
            font-size: 20px;
          }
        }

        .thumbnail-placeholder {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #c0c4cc;

          .el-icon {
            font-size: 24px;
          }
        }
      }

      .thumbnail-label {
        text-align: center;
        background-color: #f0f2f5;
        color: #606266;
        font-weight: 500;
        transition: all 0.2s ease;
      }
    }
  }

  .no-pdf {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }
}

// 移动端缩略图适配
.pdf-sidebar.mobile {
  .thumbnails-panel {
    .thumbnails-grid {
      &.thumbnail-small {
        grid-template-columns: repeat(3, 1fr);
      }

      &.thumbnail-medium {
        grid-template-columns: repeat(2, 1fr);
      }

      &.thumbnail-large {
        grid-template-columns: repeat(1, 1fr);
      }
    }
  }
}

// 全屏模式下的缩略图样式
.pdf-viewer-container:fullscreen {
  .thumbnails-panel {
    .thumbnails-toolbar {
      background-color: rgba(0, 0, 0, 0.8);
      border-bottom-color: rgba(255, 255, 255, 0.2);

      .thumbnails-info {
        color: #ccc;
      }

      .thumbnails-controls {
        .el-button-group .el-button {
          background-color: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.2);
          color: #ccc;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: #fff;
          }

          &.el-button--primary {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;
          }
        }
      }
    }

    .thumbnails-grid {
      .thumbnail-item {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);

        &:hover {
          border-color: #409eff;
          background-color: rgba(255, 255, 255, 0.15);
        }

        &.active {
          border-color: #409eff;
          background-color: rgba(64, 158, 255, 0.2);
        }

        .thumbnail-image-container {
          background-color: rgba(255, 255, 255, 0.05);

          .thumbnail-loading,
          .thumbnail-placeholder {
            color: #ccc;
          }
        }

        .thumbnail-label {
          background-color: rgba(0, 0, 0, 0.6);
          color: #ccc;
        }

        &.active .thumbnail-label {
          background-color: #409eff;
          color: #fff;
        }
      }
    }

    .no-pdf {
      color: #ccc;
    }
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "0067320b-4e1e-4f48-a5fe-5da5e8ee59dc"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "advanced-features-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF高级功能面板样式，包括高级搜索和配置面板。"
// }}

// {{RIPER-5+SMART-6:
//   Action: "Fallback-Styling"
//   Task_ID: "CANVAS-FALLBACK-STYLES"
//   Timestamp: "2025-08-18T17:40:36+08:00"
//   Authoring_Subagent: "css-expert"
//   Principle_Applied: "视觉区分与用户体验原则"
//   Quality_Check: "为fallback模式添加视觉标识和样式。"
// }}

// 备用方案模式样式
.pdf-viewer-container.legacy-canvas-viewer.fallback-mode {
  border: 2px dashed #f39c12;
  background: linear-gradient(45deg, #fff9e6 25%, transparent 25%),
              linear-gradient(-45deg, #fff9e6 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #fff9e6 75%),
              linear-gradient(-45deg, transparent 75%, #fff9e6 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;

  &::before {
    content: "Canvas备用方案";
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f39c12;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
  }
}

// PDF页面容器样式 - 建立正确的层叠上下文
.pdf-page-container {
  position: relative !important; // 建立层叠上下文
  isolation: isolate !important; // 隔离层叠上下文，避免干扰
}

// {{RIPER-5+SMART-6:
//   Action: "CSS-Layer-Fix"
//   Task_ID: "TEXTLAYER-CSS-OPTIMIZATION"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "css-expert"
//   Principle_Applied: "层叠上下文优化原则"
//   Quality_Check: "优化文本层CSS，确保正确的层级和定位。"
// }}

// PDF文本层样式 - 支持文本选择
// 移除!important声明，让JavaScript动态控制样式，避免冲突
.pdf-content .textLayer {
  position: absolute;
  left: 0;
  top: 0;
  // 移除right和bottom，使用明确的width和height
  overflow: hidden;
  line-height: 1.0;
  pointer-events: auto;
  z-index: 10; // 明确高于Canvas的层级
  transform-origin: 0 0; // 确保变换原点正确

  // 文本选择样式 - 保留!important确保文本选择功能
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;

  // 文本div样式
  > div {
    position: absolute;
    white-space: pre;
    cursor: text;
    transform-origin: 0% 0%;
    user-select: text !important;
    -webkit-user-select: text !important;

    // 选中文本的高亮样式
    &::selection {
      background-color: rgba(0, 123, 255, 0.3);
      color: inherit;
    }

    &::-moz-selection {
      background-color: rgba(0, 123, 255, 0.3);
      color: inherit;
    }
  }
}

// 搜索高亮样式
.search-highlight {
  background-color: #ffff00 !important;
  color: #000 !important;
  padding: 1px 2px;
  border-radius: 2px;
}

// PDF高级搜索面板样式
.pdf-advanced-search-panel {
  background-color: #f0f2f5;
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 16px;

  .advanced-search-content {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .search-options {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;

      .el-checkbox {
        margin-right: 0;
      }
    }

    .search-scope {
      display: flex;
      align-items: center;
      gap: 12px;

      label {
        font-size: 14px;
        color: #606266;
        min-width: 80px;
      }

      .el-radio-group {
        .el-radio {
          margin-right: 16px;
        }
      }
    }

    .page-range {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: 92px;

      span {
        color: #909399;
      }

      .el-input-number {
        width: 80px;
      }
    }

    .search-history {
      label {
        font-size: 14px;
        color: #606266;
        display: block;
        margin-bottom: 8px;
      }

      .history-items {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .el-tag {
          transition: all 0.2s ease;

          &:hover {
            background-color: #409eff;
            color: white;
          }
        }
      }
    }
  }
}

// PDF配置面板样式
.pdf-config-panel {
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin: 12px 16px;
  overflow: hidden;

  .config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;

    h4 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }
  }

  .config-content {
    padding: 16px;

    .config-section {
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      h5 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #606266;
        font-weight: 600;
      }

      .el-switch {
        display: block;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .config-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        label {
          font-size: 14px;
          color: #606266;
        }

        .el-slider {
          width: 120px;
        }
      }
    }

    .config-actions {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      margin-top: 20px;
      padding-top: 16px;
      border-top: 1px solid #e4e7ed;
    }
  }
}

// 注释层样式
.annotationLayer {
  .linkAnnotation {
    cursor: pointer;

    &:hover {
      background-color: rgba(64, 158, 255, 0.1);
    }
  }

  .textAnnotation {
    cursor: help;
  }

  .highlightAnnotation {
    mix-blend-mode: multiply;
  }

  .underlineAnnotation {
    border-bottom: 2px solid;
  }

  .strikeoutAnnotation {
    text-decoration: line-through;
  }

  .squigglyAnnotation {
    text-decoration: underline wavy;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .pdf-advanced-search-panel {
    padding: 8px 12px;

    .advanced-search-content {
      gap: 8px;

      .search-options {
        gap: 12px;
      }

      .search-scope {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .el-radio-group {
          .el-radio {
            margin-right: 12px;
          }
        }
      }

      .page-range {
        margin-left: 0;
      }
    }
  }

  .pdf-config-panel {
    margin: 8px 12px;

    .config-content {
      padding: 12px;

      .config-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }

      .config-actions {
        flex-direction: column;
        gap: 8px;
      }
    }
  }
}

// 全屏模式下的高级功能样式
.pdf-viewer-container:fullscreen {
  .pdf-advanced-search-panel {
    background-color: rgba(0, 0, 0, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.2);

    .advanced-search-content {
      .search-options {
        .el-checkbox {
          color: #ccc;

          &.is-checked {
            color: #409eff;
          }
        }
      }

      .search-scope {
        label {
          color: #ccc;
        }

        .el-radio-group {
          .el-radio {
            color: #ccc;

            &.is-checked {
              color: #409eff;
            }
          }
        }
      }

      .search-history {
        label {
          color: #ccc;
        }

        .history-items {
          .el-tag {
            background-color: rgba(255, 255, 255, 0.1);
            color: #ccc;
            border-color: rgba(255, 255, 255, 0.2);

            &:hover {
              background-color: #409eff;
              color: #fff;
            }
          }
        }
      }
    }
  }

  .pdf-config-panel {
    background-color: rgba(0, 0, 0, 0.9);
    border-color: rgba(255, 255, 255, 0.2);

    .config-header {
      background-color: rgba(255, 255, 255, 0.1);
      border-bottom-color: rgba(255, 255, 255, 0.2);

      h4 {
        color: #ccc;
      }
    }

    .config-content {
      .config-section {
        h5 {
          color: #ccc;
        }

        .config-item {
          label {
            color: #ccc;
          }
        }
      }

      .config-actions {
        border-top-color: rgba(255, 255, 255, 0.2);
      }
    }
  }
}
</style>
