<template>
  <div class="analysis-results">
    <!-- 简化的头部 -->
    <div class="results-header-simple">
      <div class="analysis-summary">
        <div class="summary-item">
          <span class="summary-label">提取结果</span>
          <span class="summary-count">{{ extractCount }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">检测问题</span>
          <span class="summary-count">{{ detectCount }}</span>
        </div>
      </div>

      <el-button
        :icon="Refresh"
        @click="refreshResults"
        :loading="loading"
        size="small"
        text
      />
    </div>

    <!-- 简化的搜索 -->
    <div class="results-search">
      <el-input
        v-model="searchText"
        placeholder="搜索结果..."
        :prefix-icon="Search"
        clearable
        @input="handleSearch"
        size="small"
      />
    </div>

    <!-- 结果分类Tab -->
    <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="results-tabs">
      <el-tab-pane label="提取结果" name="extract">
        <template #label>
          <span class="tab-label">
            <el-icon><DocumentCopy /></el-icon>
            提取结果
            <el-badge :value="extractCount" :max="99" type="success" />
          </span>
        </template>
      </el-tab-pane>
      
      <el-tab-pane label="检测问题" name="detect">
        <template #label>
          <span class="tab-label">
            <el-icon><Warning /></el-icon>
            检测问题
            <el-badge :value="detectCount" :max="99" type="warning" />
          </span>
        </template>
      </el-tab-pane>
    </el-tabs>

    <!-- 结果列表 -->
    <div class="results-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 空状态 -->
      <div v-else-if="filteredResults.length === 0" class="empty-container">
        <el-empty :description="getEmptyDescription()">
          <template v-if="!searchText.trim() && props.documentId" #default>
            <el-button
              type="primary"
              :icon="Search"
              @click="manualTriggerAnalysis"
              :loading="loading"
            >
              开始AI分析
            </el-button>
          </template>
        </el-empty>
      </div>

      <!-- 结果列表 -->
      <div v-else class="results-list" ref="listRef">
        <div
          v-for="(item, index) in visibleResults"
          :key="item.id || index"
          class="result-item"
          :class="{ 
            active: selectedItem?.id === item.id,
            'extract-item': item.type === 'extract',
            'detect-item': item.type === 'detect'
          }"
          @click="handleItemClick(item)"
        >
          <div class="item-header">
            <div class="item-type">
              <el-tag
                :type="getItemTagType(item)"
                size="small"
                effect="light"
              >
                {{ getItemTypeText(item) }}
              </el-tag>
              
              <el-tag
                v-if="item.severity"
                :color="getSeverityColor(item.severity)"
                size="small"
                effect="dark"
              >
                {{ item.severity }}
              </el-tag>
            </div>
            
            <div class="item-actions">
              <el-button
                :icon="Location"
                @click.stop="locateInDocument(item)"
                size="small"
                text
                title="定位到文档"
              />
              <el-button
                :icon="More"
                @click.stop="showItemDetail(item)"
                size="small"
                text
                title="查看详情"
              />
            </div>
          </div>

          <div class="item-content">
            <h4 class="item-title">{{ item.title || item.content?.substring(0, 50) + '...' }}</h4>
            <p class="item-description">{{ item.description || item.content }}</p>
            
            <div v-if="item.suggestion" class="item-suggestion">
              <el-icon><InfoFilled /></el-icon>
              <span>{{ item.suggestion }}</span>
            </div>
          </div>

          <div class="item-footer">
            <div class="item-meta">
              <span v-if="item.confidence" class="confidence">
                置信度: {{ Math.round(item.confidence * 100) }}%
              </span>
              <span v-if="item.position" class="position">
                位置: 第{{ item.position.page }}页
              </span>
            </div>
            
            <div class="item-time">
              {{ formatTime(item.createdTime) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 分页加载 -->
      <div v-if="hasMore && !loading" class="load-more">
        <el-button @click="loadMore" :loading="loadingMore" text>
          加载更多
        </el-button>
      </div>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="showDetail"
      title="结果详情"
      direction="rtl"
      size="400px"
    >
      <div v-if="selectedItem" class="detail-content">
        <div class="detail-header">
          <h3>{{ selectedItem.title || '分析结果' }}</h3>
          <el-tag :type="getItemTagType(selectedItem)">
            {{ getItemTypeText(selectedItem) }}
          </el-tag>
        </div>
        
        <div class="detail-body">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="类型">
              {{ selectedItem.type }}
            </el-descriptions-item>
            <el-descriptions-item label="内容">
              {{ selectedItem.content }}
            </el-descriptions-item>
            <el-descriptions-item v-if="selectedItem.confidence" label="置信度">
              {{ Math.round(selectedItem.confidence * 100) }}%
            </el-descriptions-item>
            <el-descriptions-item v-if="selectedItem.severity" label="严重程度">
              {{ selectedItem.severity }}
            </el-descriptions-item>
            <el-descriptions-item v-if="selectedItem.suggestion" label="建议">
              {{ selectedItem.suggestion }}
            </el-descriptions-item>
            <el-descriptions-item v-if="selectedItem.position" label="位置信息">
              第{{ selectedItem.position.page }}页，第{{ selectedItem.position.paragraphIndex }}段
            </el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="detail-actions">
          <el-button type="primary" @click="locateInDocument(selectedItem)">
            定位到文档
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search,
  Refresh,
  DocumentCopy,
  Warning,
  Location,
  More,
  InfoFilled
} from '@element-plus/icons-vue'
import {
  analyzeDocument,
  getAnalysisStatus,
  getDocumentAnalysisResults,
  parseExtractedData,
  parseDetectedIssues,
  getSeverityColor,
  getAgentTypeDescription,
  formatProcessingTime
} from '@/api/analysis'
import type { AnalysisResult } from '@/api/analysis'



// Props
interface Props {
  documentId?: number
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

// Emits
const emit = defineEmits<{
  'item-click': [item: ResultItem]
  'locate-request': [item: ResultItem]
  'item-select': [item: ResultItem | null]
}>()

// Types
interface ResultItem {
  id: string
  type: 'extract' | 'detect'
  title: string
  content: string
  description?: string
  severity?: string
  confidence?: number
  position?: {
    page: number
    paragraphIndex: number
    start: number
    end: number
    elementId: string
  }
  suggestion?: string
  createdTime: string
  attributes?: Record<string, any>
}

// Refs
const listRef = ref<HTMLElement>()

// State
const loading = ref(false)
const loadingMore = ref(false)
const activeTab = ref<'extract' | 'detect'>('extract')
const searchText = ref('')
const selectedSeverity = ref('')
const selectedType = ref('')
const selectedItem = ref<ResultItem | null>(null)
const showDetail = ref(false)

// 数据
const extractResults = ref<ResultItem[]>([])
const detectResults = ref<ResultItem[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)

// Computed
const currentResults = computed(() => {
  return activeTab.value === 'extract' ? extractResults.value : detectResults.value
})

const filteredResults = computed(() => {
  let results = currentResults.value

  // 搜索过滤
  if (searchText.value.trim()) {
    const keyword = searchText.value.toLowerCase()
    results = results.filter(item =>
      item.title.toLowerCase().includes(keyword) ||
      item.content.toLowerCase().includes(keyword) ||
      (item.description && item.description.toLowerCase().includes(keyword))
    )
  }

  // 严重程度过滤
  if (selectedSeverity.value) {
    results = results.filter(item => item.severity === selectedSeverity.value)
  }

  // 类型过滤
  if (selectedType.value) {
    results = results.filter(item => item.type === selectedType.value)
  }

  return results
})

const visibleResults = computed(() => {
  // 虚拟滚动：只显示前N个结果
  return filteredResults.value.slice(0, currentPage.value * pageSize.value)
})

const totalCount = computed(() => extractResults.value.length + detectResults.value.length)
const extractCount = computed(() => extractResults.value.length)
const detectCount = computed(() => detectResults.value.length)

// Methods
const loadResults = async () => {
  if (!props.documentId) return

  loading.value = true
  try {
    // 首先检查分析状态
    const statusResponse = await getAnalysisStatus(props.documentId)

    if (statusResponse.success) {
      const status = statusResponse.data.status

      // 如果还没有开始分析，则触发分析
      if (status === 'NOT_STARTED') {
        console.log('文档尚未分析，开始触发分析...')
        await triggerAnalysis()
        return // 触发分析后会通过轮询获取结果
      }

      // 如果正在处理中，开始轮询
      if (status === 'PROCESSING') {
        console.log('文档正在分析中，开始轮询状态...')
        await pollAnalysisStatus()
        return
      }
    }

    // 如果已完成，直接获取结果
    await fetchAnalysisResults()

  } catch (error: any) {
    console.error('加载分析结果失败:', error)
    ElMessage.error(`加载失败: ${error.message}`)
    loading.value = false
  }
}

// 触发分析
const triggerAnalysis = async () => {
  try {
    console.log('触发文档分析:', props.documentId)
    const response = await analyzeDocument(props.documentId!)

    if (response.success) {
      ElMessage.success('开始分析文档，请稍候...')
      // 开始轮询状态
      await pollAnalysisStatus()
    } else {
      throw new Error(response.message || '触发分析失败')
    }
  } catch (error: any) {
    console.error('触发分析失败:', error)
    ElMessage.error('触发分析失败')
    loading.value = false
  }
}

// 轮询分析状态
const pollAnalysisStatus = async () => {
  const maxAttempts = 30 // 最多轮询30次（5分钟）
  let attempts = 0

  const poll = async (): Promise<void> => {
    try {
      attempts++
      const statusResponse = await getAnalysisStatus(props.documentId!)

      if (statusResponse.success) {
        const { status, isCompleted } = statusResponse.data

        console.log(`分析状态检查 ${attempts}/${maxAttempts}: ${status}`)

        if (isCompleted) {
          // 分析完成，获取结果
          await fetchAnalysisResults()
          return
        }

        if (attempts >= maxAttempts) {
          throw new Error('分析超时，请稍后重试')
        }

        // 继续轮询
        setTimeout(poll, 10000) // 10秒后再次检查
      } else {
        throw new Error('获取分析状态失败')
      }
    } catch (error: any) {
      console.error('轮询分析状态失败:', error)
      ElMessage.error('获取分析状态失败')
      loading.value = false
    }
  }

  // 开始轮询
  setTimeout(poll, 2000) // 2秒后开始第一次检查
}

// 获取分析结果
const fetchAnalysisResults = async () => {
  try {
    const response = await getDocumentAnalysisResults(props.documentId!)

    if (response.success) {
      processAnalysisResults(response.data.data)
      ElMessage.success(`分析完成，共 ${totalCount.value} 条结果`)
    } else {
      ElMessage.error(response.message || '获取分析结果失败')
    }
  } catch (error: any) {
    console.error('获取分析结果失败:', error)
    ElMessage.error('获取分析结果失败')
  } finally {
    loading.value = false
  }
}

const processAnalysisResults = (results: AnalysisResult[]) => {
  extractResults.value = []
  detectResults.value = []

  results.forEach(result => {
    if (result.agentType === 'EXTRACT') {
      const extractedData = parseExtractedData(result.resultContent)
      extractedData.forEach((item, index) => {
        extractResults.value.push({
          id: `extract-${result.id}-${index}`,
          type: 'extract',
          title: item.title,
          content: item.content,
          confidence: item.confidence,
          position: item.position,
          createdTime: result.createdTime,
          attributes: item.attributes
        })
      })
    } else if (result.agentType === 'DETECT') {
      const detectedIssues = parseDetectedIssues(result.resultContent)
      detectedIssues.forEach((issue, index) => {
        detectResults.value.push({
          id: `detect-${result.id}-${index}`,
          type: 'detect',
          title: issue.title,
          content: issue.description,
          description: issue.description,
          severity: issue.severity,
          position: issue.position,
          suggestion: issue.suggestion,
          createdTime: result.createdTime,
          attributes: issue.attributes
        })
      })
    }
  })

  // 更新统计信息
  emit('results-loaded', {
    totalCount: totalCount.value,
    extractCount: extractCount.value,
    detectCount: detectCount.value
  })
}

const refreshResults = () => {
  currentPage.value = 1
  loadResults()
}

// 手动触发分析（用于刷新按钮）
const manualTriggerAnalysis = async () => {
  if (!props.documentId) return

  loading.value = true
  try {
    await triggerAnalysis()
  } catch (error) {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const handleFilter = () => {
  currentPage.value = 1
}

const handleTabChange = (tab: string) => {
  activeTab.value = tab as 'extract' | 'detect'
  selectedItem.value = null
  currentPage.value = 1
}

const handleItemClick = (item: ResultItem) => {
  selectedItem.value = item
  emit('item-click', item)
  emit('item-select', item)
}

const locateInDocument = (item: ResultItem) => {
  emit('locate-request', item)

  // 触发高亮显示
  if (item.position) {
    // 这里可以通过事件总线或者父组件传递来触发高亮
    const event = new CustomEvent('highlight-result-item', {
      detail: { item }
    })
    window.dispatchEvent(event)
  }
}

const showItemDetail = (item: ResultItem) => {
  selectedItem.value = item
  showDetail.value = true
}

const loadMore = () => {
  if (loadingMore.value || !hasMore.value) return
  
  loadingMore.value = true
  currentPage.value++
  
  // 模拟加载延迟
  setTimeout(() => {
    loadingMore.value = false
    // 检查是否还有更多数据
    if (visibleResults.value.length >= filteredResults.value.length) {
      hasMore.value = false
    }
  }, 500)
}

// 工具方法
const getItemTagType = (item: ResultItem) => {
  return item.type === 'extract' ? 'success' : 'warning'
}

const getItemTypeText = (item: ResultItem) => {
  return item.type === 'extract' ? '提取' : '检测'
}

const getEmptyDescription = () => {
  if (searchText.value.trim()) {
    return '未找到匹配的结果'
  }

  if (!props.documentId) {
    return '请先上传文档'
  }

  const baseMessage = activeTab.value === 'extract' ? '暂无提取结果' : '暂无检测问题'
  return `${baseMessage}，点击下方按钮开始AI分析`
}

const formatTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听
watch(() => props.documentId, (newId) => {
  if (newId && props.autoLoad) {
    loadResults()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.documentId && props.autoLoad) {
    loadResults()
  }
})

// 暴露方法
defineExpose({
  loadResults,
  refreshResults,
  manualTriggerAnalysis,
  selectItem: (item: ResultItem | null) => {
    selectedItem.value = item
  }
})
</script>

<style scoped>
.analysis-results {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
  overflow: hidden;
}

.results-header-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  margin-bottom: 16px;
}

.analysis-summary {
  display: flex;
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-label {
  font-size: 12px;
  color: #909399;
}

.summary-count {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.results-search {
  margin-bottom: 16px;
}

.results-tabs {
  border-bottom: 1px solid #f0f0f0;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.results-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading-container,
.empty-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.results-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.result-item {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.result-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.result-item.active {
  border: 2px solid #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.result-item.extract-item {
  border-left: 4px solid #67c23a;
}

.result-item.detect-item {
  border-left: 4px solid #e6a23c;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-type {
  display: flex;
  gap: 4px;
}

.item-actions {
  display: flex;
  gap: 4px;
}

.item-content {
  margin-bottom: 8px;
}

.item-title {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.item-description {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.item-suggestion {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #409eff;
  background: #f0f8ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.item-meta {
  display: flex;
  gap: 12px;
}

.confidence {
  color: #67c23a;
}

.position {
  color: #409eff;
}

.load-more {
  text-align: center;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.detail-content {
  padding: 16px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.detail-header h3 {
  margin: 0;
  font-size: 16px;
}

.detail-body {
  margin-bottom: 16px;
}

.detail-actions {
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .results-header {
    padding: 12px;
  }
  
  .results-filters {
    padding: 8px 12px;
  }
  
  .filter-options {
    flex-direction: column;
    gap: 4px;
  }
  
  .result-item {
    padding: 8px;
  }
  
  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .item-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
