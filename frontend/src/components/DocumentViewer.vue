<template>
  <div class="document-viewer" ref="viewerRef">
    <!-- 工具栏 -->
    <div class="viewer-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button :icon="ZoomOut" @click="zoomOut" :disabled="zoomLevel <= 0.5">缩小</el-button>
          <el-button @click="resetZoom">{{ Math.round(zoomLevel * 100) }}%</el-button>
          <el-button :icon="ZoomIn" @click="zoomIn" :disabled="zoomLevel >= 3">放大</el-button>
        </el-button-group>
        <el-button :icon="FullScreen" @click="fitToWidth" class="ml-2">适应宽度</el-button>
      </div>

      <div class="toolbar-right">
        <el-button :icon="Position" @click="showNavigator = !showNavigator">导航</el-button>
        <el-button :icon="Refresh" @click="refreshDocument" :loading="loading">刷新</el-button>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="viewer-content pb-20px" :style="{ transform: `scale(${zoomLevel})` }">
      <el-scrollbar ref="scrollbarListRef" class="w-full px-20px" height="100%" always>
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="10" animated />
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
          <el-result icon="error" title="文档加载失败" :sub-title="error">
            <template #extra>
              <el-button type="primary" @click="refreshDocument">重新加载</el-button>
            </template>
          </el-result>
        </div>

        <!-- 文档内容 -->
        <div v-else-if="documentContent" class="document-container">
          <div class="document-content" v-html="documentContent.htmlContent" @click="handleContentClick" ref="contentRef"></div>

          <!-- 高亮交互组件 -->
          <HighlightInteraction
            ref="highlightInteractionRef"
            :document-container="contentRef"
            :auto-highlight="true"
            @highlight-click="handleHighlightClick"
            @highlight-added="handleHighlightAdded"
            @highlight-removed="handleHighlightRemoved"
            @position-mapped="handlePositionMapped"
          />
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-container">
          <el-empty description="暂无文档内容" />
        </div>
      </el-scrollbar>
    </div>

    <!-- 导航面板 -->
    <el-drawer v-model="showNavigator" title="文档导航" direction="rtl" size="300px">
      <div class="navigator-content">
        <el-input v-model="searchText" placeholder="搜索内容..." :prefix-icon="Search" clearable @input="handleSearch" class="mb-4" />

        <div class="outline-tree">
          <el-scrollbar ref="scrollbarRef" class="w-full px-10px" height="100%" always>
            <div v-for="(item, index) in outline" :key="index" class="outline-item" :class="{ active: currentSection === index }" @click="scrollToSection(item.elementId)">
              <span class="outline-text">{{ item.text }}</span>
              <span class="outline-page">第{{ item.page }}页</span>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </el-drawer>

    <!-- 高亮标记 -->
    <div class="highlight-markers">
      <div v-for="marker in highlightMarkers" :key="marker.id" class="highlight-marker" :style="marker.style" @click="handleMarkerClick(marker)"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ZoomIn, ZoomOut, FullScreen, Position, Refresh, Search } from '@element-plus/icons-vue'
import { getDocumentContent } from '@/api/document'
import { useDocumentStore } from '@/stores/document'
import HighlightInteraction from './HighlightInteraction.vue'
import type { DocumentContent } from '@/types'

/**
 * Document Viewer Component
 *
 * {{RIPER-5+SMART-6:
 *   Action: "Parallel-Added"
 *   Task_ID: "cfb46047-4a39-45f6-b3f1-0bb2e6642b37"
 *   Timestamp: "2025-08-07T13:50:00+08:00"
 *   Authoring_Subagent: "vue-frontend-expert"
 *   Principle_Applied: "SOLID-S (单一职责原则)"
 *   Quality_Check: "文档展示组件功能完整，性能优化良好。"
 * }}
 */

// Props
interface Props {
  documentId?: number
  autoLoad?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true
})

// Emits
const emit = defineEmits<{
  'content-click': [event: MouseEvent, element: HTMLElement]
  'marker-click': [marker: HighlightMarker]
  'section-change': [section: number]
}>()

// Store
const documentStore = useDocumentStore()

// Refs
const viewerRef = ref<HTMLElement>()
const contentRef = ref<HTMLElement>()

// State
const loading = ref(false)
const error = ref('')
const documentContent = ref<DocumentContent | null>(null)
const zoomLevel = ref(1)
const showNavigator = ref(false)
const searchText = ref('')
const currentSection = ref(0)
const highlightInteractionRef = ref()

// 高亮标记
interface HighlightMarker {
  id: string
  type: 'extract' | 'detect'
  position: { x: number; y: number }
  style: Record<string, string>
  data: any
}

const highlightMarkers = ref<HighlightMarker[]>([])

// 文档大纲
interface OutlineItem {
  text: string
  elementId: string
  page: number
  level: number
}

const outline = ref<OutlineItem[]>([])

// Computed
const documentId = computed(() => props.documentId || documentStore.currentDocument?.id)

// Methods
const loadDocument = async () => {
  if (!documentId.value) {
    error.value = '文档ID不存在'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const response = await getDocumentContent(documentId.value)
    if (response.success) {
      documentContent.value = response.data
      await nextTick()
      generateOutline()
      setupVirtualScroll()
    } else {
      error.value = response.message || '加载文档失败'
    }
  } catch (err: any) {
    error.value = err.message || '网络错误'
  } finally {
    loading.value = false
  }
}

const refreshDocument = () => {
  loadDocument()
}

// 缩放功能
const zoomIn = () => {
  if (zoomLevel.value < 3) {
    zoomLevel.value = Math.min(3, zoomLevel.value + 0.1)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const fitToWidth = () => {
  if (viewerRef.value && contentRef.value) {
    const viewerWidth = viewerRef.value.clientWidth - 40 // 减去padding
    const contentWidth = contentRef.value.scrollWidth
    zoomLevel.value = Math.min(2, viewerWidth / contentWidth)
  }
}

// 生成文档大纲
const generateOutline = () => {
  if (!contentRef.value) return

  const headings = contentRef.value.querySelectorAll('h1, h2, h3, h4, h5, h6, .document-paragraph')
  outline.value = Array.from(headings).map((heading, index) => {
    const text = heading.textContent?.trim() || `段落 ${index + 1}`
    const elementId = heading.id || `heading-${index}`

    // 如果没有ID，添加一个
    if (!heading.id) {
      heading.id = elementId
    }

    return {
      text: text.length > 50 ? text.substring(0, 50) + '...' : text,
      elementId,
      page: Math.floor(index / 10) + 1, // 简单的页码计算
      level: parseInt(heading.tagName.charAt(1)) || 1
    }
  })
}

// 滚动到指定章节
const scrollToSection = (elementId: string) => {
  const element = document.getElementById(elementId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    showNavigator.value = false
  }
}

// 搜索功能
const handleSearch = (value: string) => {
  if (!value.trim() || !contentRef.value) return

  // 清除之前的高亮
  clearSearchHighlight()

  // 搜索并高亮
  const walker = document.createTreeWalker(contentRef.value, NodeFilter.SHOW_TEXT, null)

  const textNodes: Text[] = []
  let node: Node | null
  while ((node = walker.nextNode())) {
    textNodes.push(node as Text)
  }

  textNodes.forEach(textNode => {
    const text = textNode.textContent || ''
    const regex = new RegExp(value, 'gi')
    if (regex.test(text)) {
      const highlightedText = text.replace(regex, `<mark class="search-highlight">$&</mark>`)
      const span = document.createElement('span')
      span.innerHTML = highlightedText
      textNode.parentNode?.replaceChild(span, textNode)
    }
  })
}

// 清除搜索高亮
const clearSearchHighlight = (className?: string) => {
  if (!contentRef.value) return

  const selector = className ? `.${className}` : '.search-highlight, [class*="source-highlight"]'
  const highlights = contentRef.value.querySelectorAll(selector)

  highlights.forEach(highlight => {
    const parent = highlight.parentNode
    if (parent) {
      parent.replaceChild(document.createTextNode(highlight.textContent || ''), highlight)
      parent.normalize()
    }
  })
}

// 清除所有源引用高亮
const clearAllSourceHighlights = () => {
  clearSearchHighlight()
}

// 虚拟滚动设置
const setupVirtualScroll = () => {
  // TODO: 实现虚拟滚动优化
  // 对于大文档，可以实现虚拟滚动来提升性能
}

// 内容点击处理
const handleContentClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement
  emit('content-click', event, target)
}

// 标记点击处理
const handleMarkerClick = (marker: HighlightMarker) => {
  emit('marker-click', marker)
}

// 添加高亮标记
const addHighlightMarker = (marker: Omit<HighlightMarker, 'id'>) => {
  const id = `marker-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  highlightMarkers.value.push({ ...marker, id })
}

// 清除高亮标记
const clearHighlightMarkers = () => {
  highlightMarkers.value = []
  highlightInteractionRef.value?.clearAllHighlights()
}

// 高亮结果项
const highlightResultItem = (item: any) => {
  if (highlightInteractionRef.value) {
    highlightInteractionRef.value.highlightResultItem(item)
  }
}

// 定位并高亮文本内容
const locateAndHighlightText = (content: string, highlightId?: string) => {
  if (!contentRef.value || !content.trim()) {
    console.warn('无法定位文本：内容为空或文档未加载')
    return false
  }

  // 清除之前的搜索高亮
  clearSearchHighlight()

  // 提取实际的文本内容（去除引用序号和位置信息）
  let searchText = content
  const match = content.match(/^第.+处（.+）：(.+)$/)
  if (match && match[2]) {
    searchText = match[2].trim()
  }

  // 如果文本太短，直接搜索原文
  if (searchText.length < 10) {
    searchText = content
  }

  console.log('搜索文本:', searchText)

  // 在文档中搜索文本
  const found = searchAndHighlightText(searchText, highlightId)

  if (found) {
    console.log('文本定位成功')
    return true
  } else {
    console.warn('未找到匹配的文本内容')
    // 尝试搜索关键词
    const keywords = extractKeywords(searchText)
    if (keywords.length > 0) {
      console.log('尝试搜索关键词:', keywords)
      return searchAndHighlightKeywords(keywords, highlightId)
    }
    return false
  }
}

// 搜索并高亮文本
const searchAndHighlightText = (searchText: string, highlightId?: string): boolean => {
  if (!contentRef.value) return false

  const walker = document.createTreeWalker(contentRef.value, NodeFilter.SHOW_TEXT, null)

  const textNodes: Text[] = []
  let node: Node | null
  while ((node = walker.nextNode())) {
    textNodes.push(node as Text)
  }

  let found = false
  const highlightClass = highlightId ? `source-highlight-${highlightId}` : 'source-highlight'

  textNodes.forEach(textNode => {
    const text = textNode.textContent || ''
    const normalizedText = text.replace(/\s+/g, ' ').trim()
    const normalizedSearch = searchText.replace(/\s+/g, ' ').trim()

    if (normalizedText.includes(normalizedSearch)) {
      const regex = new RegExp(escapeRegExp(normalizedSearch), 'gi')
      const highlightedText = text.replace(regex, `<mark class="${highlightClass}">$&</mark>`)

      const span = document.createElement('span')
      span.innerHTML = highlightedText
      textNode.parentNode?.replaceChild(span, textNode)

      // 滚动到第一个匹配的位置
      if (!found) {
        const firstHighlight = span.querySelector(`.${highlightClass}`)
        if (firstHighlight) {
          firstHighlight.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          })
          found = true
        }
      }
    }
  })

  return found
}

// 提取关键词
const extractKeywords = (text: string): string[] => {
  // 移除标点符号，分割成词语
  const words = text
    .replace(/[，。！？；：""''（）【】]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length >= 2)
    .slice(0, 5) // 最多取5个关键词

  return words
}

// 搜索并高亮关键词
const searchAndHighlightKeywords = (keywords: string[], highlightId?: string): boolean => {
  let found = false

  keywords.forEach((keyword, index) => {
    if (searchAndHighlightText(keyword, `${highlightId}-kw-${index}`)) {
      found = true
    }
  })

  return found
}

// 转义正则表达式特殊字符
const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// 高亮交互事件处理
const handleHighlightClick = (marker: any) => {
  emit('marker-click', marker)
}

const handleHighlightAdded = (marker: any) => {
  console.log('Highlight added:', marker)
}

const handleHighlightRemoved = (id: string) => {
  console.log('Highlight removed:', id)
}

const handlePositionMapped = (mapping: any) => {
  console.log('Position mapped:', mapping)
}

// 监听文档变化
watch(
  documentId,
  newId => {
    if (newId && props.autoLoad) {
      loadDocument()
    }
  },
  { immediate: true }
)

// 键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case '=':
      case '+':
        event.preventDefault()
        zoomIn()
        break
      case '-':
        event.preventDefault()
        zoomOut()
        break
      case '0':
        event.preventDefault()
        resetZoom()
        break
    }
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  if (documentId.value && props.autoLoad) {
    loadDocument()
  }
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 暴露方法
defineExpose({
  loadDocument,
  refreshDocument,
  zoomIn,
  zoomOut,
  resetZoom,
  fitToWidth,
  addHighlightMarker,
  clearHighlightMarkers,
  highlightResultItem,
  scrollToSection,
  locateAndHighlightText,
  clearAllSourceHighlights
})
</script>

<style scoped>
:deep .el-drawer__header {
  margin-bottom: 0;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.document-viewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.viewer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.viewer-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  transform-origin: top left;
  transition: transform 0.3s ease;
  height: calc(100vh - 200px);
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

.document-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.document-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-height: 100%;
  padding: 20px;
}

.navigator-content {
}

.outline-tree {
  height: calc(100vh - 200px);
  overflow: hidden;
}

.outline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.outline-item:hover {
  background-color: #f5f7fa;
}

.outline-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
}

.outline-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

.outline-page {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.highlight-markers {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.highlight-marker {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  pointer-events: auto;
  cursor: pointer;
  z-index: 10;
}

.highlight-marker[data-type='extract'] {
  background-color: #52c41a;
  border: 2px solid #389e0d;
}

.highlight-marker[data-type='detect'] {
  background-color: #ff4d4f;
  border: 2px solid #cf1322;
}

/* 搜索高亮样式 */
:deep(.search-highlight) {
  background-color: #fff566;
  padding: 2px 4px;
  border-radius: 2px;
}

/* 源引用高亮样式 */
:deep([class*='source-highlight']) {
  background-color: #e6f7ff;
  border: 2px solid #409eff;
  padding: 4px 6px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
  0% {
    background-color: #409eff;
    color: white;
  }
  50% {
    background-color: #e6f7ff;
    color: #409eff;
  }
  100% {
    background-color: #e6f7ff;
    color: #409eff;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .viewer-toolbar {
    flex-direction: column;
    gap: 8px;
    padding: 8px;
  }

  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }

  .viewer-content {
    padding: 10px;
  }
}
</style>
