

// PDF渲染模式
export type RenderMode = 'html' | 'pdf'

// PDF文档信息
export interface PdfDocumentInfo {
  numPages: number
  title?: string
  author?: string
  subject?: string
  creator?: string
  producer?: string
  creationDate?: Date
  modificationDate?: Date
}

// PDF页面信息
export interface PdfPageInfo {
  pageNumber: number
  width: number
  height: number
  rotation: number
}

// PDF渲染选项
export interface PdfRenderOptions {
  scale?: number
  rotation?: number
  enableTextSelection?: boolean
  enableAnnotations?: boolean
  renderTextLayer?: boolean
  renderAnnotationLayer?: boolean
}

// PDF加载状态
export interface PdfLoadingState {
  isLoading: boolean
  progress: number
  error?: string
}

// PDF缩放选项
export interface PdfZoomOptions {
  scale: number
  fitToWidth: boolean
  fitToHeight: boolean
  fitToPage: boolean
}

// vue-pdf-embed组件属性
export interface VuePdfEmbedProps {
  source: string | Uint8Array | ArrayBuffer
  page?: number
  scale?: number
  rotation?: number
  width?: number
  height?: number
  textLayer?: boolean
  annotationLayer?: boolean
  imageResourcesPath?: string
  cMapUrl?: string
  cMapPacked?: boolean
  workerSrc?: string
}

// PDF事件类型
export interface PdfEvents {
  onLoaded?: (pdf: any) => void
  onProgress?: (progress: number) => void
  onError?: (error: Error) => void
  onPageRendered?: (pageInfo: PdfPageInfo) => void
  onTextLayerRendered?: () => void
  onAnnotationLayerRendered?: () => void
}

// PDF工具栏状态
export interface PdfToolbarState {
  currentPage: number
  totalPages: number
  scale: number
  canZoomIn: boolean
  canZoomOut: boolean
  isLoading: boolean
  isFullscreen: boolean
  fitMode: 'width' | 'page' | 'auto' | 'custom'
  canGoToPrevious: boolean
  canGoToNext: boolean
}

// PDF搜索结果
export interface PdfSearchResult {
  pageIndex: number
  textContent: string
  matches: Array<{
    begin: number
    end: number
  }>
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "02a7b0ba-4810-4bfb-9184-ef3e3aa57966"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-integration-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF文本层和搜索相关的TypeScript接口定义。"
// }}

// PDF文本层数据
export interface PdfTextLayerData {
  textLayer: any // PDF.js 3.x renderTextLayer返回的任务对象
  textContent: any // PDF.js textContent对象
  textLayerDiv: HTMLElement // 文本层DOM容器
  pageNumber: number
}

// PDF搜索匹配结果
export interface PdfSearchMatch {
  pageIndex: number
  textContent: string
  position: { x: number; y: number; width: number; height: number }
  highlightElement?: HTMLElement
  matchIndex: number
  matchLength?: number // 匹配文本的长度
  startOffset?: number // 在原文本中的起始偏移
}

// PDF搜索选项
export interface PdfSearchOptions {
  caseSensitive?: boolean
  wholeWord?: boolean
  regex?: boolean
  highlightAll?: boolean
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "bb8c1cc5-efda-4000-b46c-ee5393c2741f"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "pdfjs-navigation-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF文档大纲相关的TypeScript接口定义。"
// }}

// PDF文档大纲项
export interface PdfOutlineItem {
  title: string
  bold?: boolean
  italic?: boolean
  color?: number[]
  dest?: any
  url?: string
  items?: PdfOutlineItem[]
  count?: number
  newWindow?: boolean
  unsafeUrl?: string
  action?: string
  id?: string
  level?: number
}

// PDF侧边栏配置
export interface PdfSidebarConfig {
  showOutline: boolean
  showThumbnails: boolean
  showBookmarks: boolean
  defaultTab: 'outline' | 'thumbnails' | 'bookmarks'
  width: number
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "2f86cbd6-164f-43b9-aceb-06c4b6c3f149"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "thumbnail-navigation-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF缩略图相关的TypeScript接口定义。"
// }}

// PDF缩略图数据
export interface PdfThumbnailData {
  pageNumber: number
  imageUrl: string
  width: number
  height: number
  scale: number
  isLoading?: boolean
}

// PDF缩略图配置
export interface PdfThumbnailConfig {
  scale: number
  size: 'small' | 'medium' | 'large'
  lazyLoad: boolean
  cacheSize: number
  quality: 'low' | 'medium' | 'high'
}

// {{RIPER-5+SMART-6:
//   Action: "Added"
//   Task_ID: "0067320b-4e1e-4f48-a5fe-5da5e8ee59dc"
//   Timestamp: "2025-08-18T11:25:43+08:00"
//   Authoring_Subagent: "advanced-features-expert"
//   Principle_Applied: "SOLID-S (单一职责原则)"
//   Quality_Check: "添加PDF注释层和高级功能相关的TypeScript接口定义。"
// }}

// PDF注释层数据
export interface PdfAnnotationData {
  pageNumber: number
  annotations: any[]
  annotationLayer: any
  annotationDiv: HTMLElement
}

// PDF高级搜索选项
export interface PdfAdvancedSearchOptions extends PdfSearchOptions {
  searchInAnnotations?: boolean
  searchInOutline?: boolean
  useRegex?: boolean
  maxResults?: number
  searchScope?: 'all' | 'current' | 'range'
  pageRange?: { start: number; end: number }
}

// PDF功能配置
export interface PdfFeatureConfig {
  enableTextLayer: boolean
  enableAnnotations: boolean
  enableOutline: boolean
  enableThumbnails: boolean
  enableSearch: boolean
  enableKeyboardShortcuts: boolean
  autoLoadOutline: boolean
  autoGenerateThumbnails: boolean
  searchHighlightColor: string
  annotationOpacity: number
}

// PDF搜索历史项
export interface PdfSearchHistoryItem {
  query: string
  timestamp: number
  resultsCount: number
  options: PdfAdvancedSearchOptions
}

// PDF配置选项
export interface PdfConfig {
  workerSrc: string
  cMapUrl: string
  cMapPacked: boolean
  standardFontDataUrl: string
  enableXfa: boolean
  disableAutoFetch: boolean
  disableStream: boolean
  disableRange: boolean
  fontExtraProperties: boolean
  useOnlyCssZoom: boolean
  isEvalSupported: boolean
  isOffscreenCanvasSupported: boolean
  canvasMaxAreaInBytes: number
  maxImageSize: number
  verbosity: number
}

// 导出默认PDF配置
export const defaultPdfConfig: Partial<PdfConfig> = {
  workerSrc: '/pdfjs-dist/build/pdf.worker.min.js',
  cMapUrl: '/pdfjs-dist/cmaps/',
  cMapPacked: true,
  enableXfa: false,
  disableAutoFetch: false,
  disableStream: false,
  disableRange: false,
  fontExtraProperties: false,
  useOnlyCssZoom: false,
  verbosity: 0
}
