

// API Response Types
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// Document Types
export interface DocumentInfo {
  id: number
  filename: string
  originalName: string
  filePath: string
  fileSize: number
  uploadTime: string
  status: 'UPLOADED' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
}

export interface DocumentContent {
  documentId: number
  originalName: string
  htmlContent: string
  textContent: string
  cssStyles: string
  metadata: {
    title?: string
    creator?: string
    description?: string
    paragraphCount?: number
    tableCount?: number
    characterCount?: number
    [key: string]: any
  }
  elements: DocumentElement[]
  positionMappings: PositionMapping[]
  parseStatus: string
  parseTime: number
  errorMessage?: string
}

export interface DocumentElement {
  elementId: string
  type: string
  content: string
  style: Record<string, any>
  startPosition: number
  endPosition: number
}

export interface PositionMapping {
  elementId: string
  originalStart: number
  originalEnd: number
  htmlStart: number
  htmlEnd: number
  htmlElementId: string
}

// Analysis Types
export interface AnalysisResult {
  id: number
  documentId: number
  agentType: 'EXTRACT' | 'DETECT'
  resultContent: any
  createdTime: string
}

// 新的结构化分析结果类型
export interface StructuredAnalysisResult {
  documentId: number
  extractItems: ExtractItem[]
  detectItems: DetectItem[]
  statistics: AnalysisStatistics
  lastUpdated: string
  status: string
}

export interface SourceReference {
  referenceIndex: string
  location: string
  content: string
  position?: PositionInfo
}

export interface PositionInfo {
  page?: number
  paragraph?: number
  startOffset?: number
  endOffset?: number
  keywords?: string[]
}

export interface ExtractItem {
  id: string
  title: string
  sources: SourceReference[]
  status: string
  importance: string
  position?: PositionInfo
  rawData?: any
  content: string
}

export interface DetectItem {
  id: string
  title: string
  sources: SourceReference[]
  result: string
  severity: string
  status: string
  position?: PositionInfo
  reason: string
  rawData?: any
  content: string
}

export interface AnalysisStatistics {
  totalExtractItems: number
  totalDetectItems: number
  successfulExtracts: number
  abnormalDetects: number
  normalDetects: number
  highSeverityDetects: number
  mediumSeverityDetects: number
  lowSeverityDetects: number
  processingStatus: string
  completionPercentage: number
}

// 兼容性类型（保留原有接口）
export interface ExtractData {
  content: string
  position?: {
    start: number
    end: number
    page?: number
  }
  confidence?: number
  type?: string
}

export interface DetectData {
  type: string
  description: string
  position?: {
    start: number
    end: number
    page?: number
  }
  severity?: 'low' | 'medium' | 'high'
  suggestion?: string
}

// UI Types
export interface HighlightRange {
  start: number
  end: number
  color?: string
  className?: string
}

export interface TabItem {
  name: string
  label: string
  count?: number
}

// File Upload Types
export interface UploadProgress {
  percent: number
  status: 'uploading' | 'success' | 'error'
  message?: string
}

// Error Types
export interface ErrorInfo {
  code: string
  message: string
  details?: any
}
