/**
 * {{RIPER-5+SMART-6:
 *   Action: "TypeScript-Definitions"
 *   Task_ID: "PDFJS-TYPES"
 *   Timestamp: "2025-08-18T17:40:36+08:00"
 *   Authoring_Subagent: "typescript-expert"
 *   Principle_Applied: "类型安全与开发体验原则"
 *   Quality_Check: "完整的PDF.js类型定义，确保类型安全。"
 * }}
 */

// PDF.js核心类型定义
declare module 'pdfjs-dist' {
  export interface PDFDocumentProxy {
    numPages: number
    fingerprint: string
    getPage(pageNumber: number): Promise<PDFPageProxy>
    getMetadata(): Promise<PDFMetadata>
    getOutline(): Promise<PDFOutline[] | null>
    destroy(): void
  }

  export interface PDFPageProxy {
    pageNumber: number
    getViewport(params: { scale: number; rotation?: number }): PDFPageViewport
    render(params: PDFRenderParams): PDFRenderTask
    getTextContent(): Promise<PDFTextContent>
    getAnnotations(): Promise<PDFAnnotation[]>
    cleanup(): void
  }

  export interface PDFPageViewport {
    width: number
    height: number
    scale: number
    rotation: number
    transform: number[]
    clone(params?: { scale?: number; rotation?: number }): PDFPageViewport
  }

  export interface PDFRenderParams {
    canvasContext: CanvasRenderingContext2D
    viewport: PDFPageViewport
    intent?: string
    enableWebGL?: boolean
    renderInteractiveForms?: boolean
    transform?: number[]
    background?: string
  }

  export interface PDFRenderTask {
    promise: Promise<void>
    cancel(): void
    onContinue?: (callback: () => void) => void
  }

  export interface PDFTextContent {
    items: PDFTextItem[]
    styles: Record<string, PDFTextStyle>
  }

  export interface PDFTextItem {
    str: string
    dir: string
    width: number
    height: number
    transform: number[]
    fontName: string
    hasEOL?: boolean
  }

  export interface PDFTextStyle {
    fontFamily: string
    fontSize: number
    ascent: number
    descent: number
    vertical: boolean
  }

  export interface PDFMetadata {
    info: PDFInfo
    metadata: any
    contentDispositionFilename?: string
    contentLength?: number
  }

  export interface PDFInfo {
    Title?: string
    Author?: string
    Subject?: string
    Keywords?: string
    Creator?: string
    Producer?: string
    CreationDate?: string
    ModDate?: string
    PDFFormatVersion?: string
  }

  export interface PDFOutline {
    title: string
    bold?: boolean
    italic?: boolean
    color?: number[]
    dest?: any
    url?: string
    items?: PDFOutline[]
  }

  export interface PDFAnnotation {
    annotationType: number
    subtype: string
    rect: number[]
    contents?: string
    title?: string
    modificationDate?: string
    flags?: number
  }

  export interface PDFLoadingTask {
    promise: Promise<PDFDocumentProxy>
    destroy(): void
    onProgress?: (progressData: PDFProgressData) => void
    onPassword?: (callback: (password: string) => void, reason: number) => void
  }

  export interface PDFProgressData {
    loaded: number
    total: number
  }

  export interface PDFSource {
    url?: string
    data?: Uint8Array | ArrayBuffer
    httpHeaders?: Record<string, string>
    withCredentials?: boolean
    password?: string
    length?: number
    range?: PDFDataRange
    rangeChunkSize?: number
    worker?: any
    verbosity?: number
    docBaseUrl?: string
    cMapUrl?: string
    cMapPacked?: boolean
    CMapReaderFactory?: any
    StandardFontDataFactory?: any
    useSystemFonts?: boolean
    stopAtErrors?: boolean
    maxImageSize?: number
    isEvalSupported?: boolean
    disableFontFace?: boolean
    fontExtraProperties?: boolean
    enableXfa?: boolean
    ownerDocument?: Document
    disableRange?: boolean
    disableStream?: boolean
    disableAutoFetch?: boolean
  }

  export interface PDFDataRange {
    begin: number
    chunk: Uint8Array
  }

  export function getDocument(src: PDFSource | string): PDFLoadingTask
  export const version: string
  export const build: string

  // Worker相关
  export class PDFWorker {
    constructor(params?: { name?: string; port?: any; verbosity?: number })
    destroy(): void
    static fromPort(params: { port: any; verbosity?: number }): PDFWorker
  }

  // 全局配置
  export const GlobalWorkerOptions: {
    workerSrc: string
    workerPort?: any
  }
}

// PDF.js查看器相关类型
declare module 'pdfjs-dist/web/pdf_viewer' {
  export interface PDFViewer {
    container: HTMLElement
    viewer: HTMLElement
    pagesCount: number
    currentPageNumber: number
    currentScale: number
    currentScaleValue: string
    pagesRotation: number
    
    setDocument(pdfDocument: any): void
    scrollPageIntoView(params: { pageNumber: number; destArray?: any }): void
    cleanup(): void
    
    // 事件
    eventBus: EventBus
  }

  export interface EventBus {
    on(eventName: string, listener: (...args: any[]) => void): void
    off(eventName: string, listener: (...args: any[]) => void): void
    dispatch(eventName: string, data?: any): void
  }

  export interface PDFLinkService {
    setDocument(pdfDocument: any, baseUrl?: string): void
    setViewer(viewer: PDFViewer): void
    setHistory(history: any): void
    navigateTo(dest: any): void
    getDestinationHash(dest: any): string
    getAnchorUrl(hash: string): string
    setHash(hash: string): void
    executeNamedAction(action: string): void
    cachePageRef(pageNum: number, pageRef: any): void
    isPageVisible(pageNumber: number): boolean
  }

  export interface PDFFindController {
    setDocument(pdfDocument: any): void
    executeCommand(cmd: string, state?: any): void
    onUpdateResultsCount?: (matchesCount: { current: number; total: number }) => void
    onUpdateState?: (state: number, previous?: any, matchesCount?: any) => void
  }
}

// 扩展Window接口以支持PDF.js全局变量
declare global {
  interface Window {
    pdfjsLib: typeof import('pdfjs-dist')
    pdfjsViewer: typeof import('pdfjs-dist/web/pdf_viewer')
    PDFViewerApplication: any
    PDFViewerApplicationOptions: any
  }

  // 扩展Performance接口以支持内存监控
  interface Performance {
    memory?: {
      usedJSHeapSize: number
      totalJSHeapSize: number
      jsHeapSizeLimit: number
    }
  }
}

// Vue组件相关类型
export interface PdfViewerConfig {
  disablePresentationMode?: boolean
  disableOpenFile?: boolean
  disablePrint?: boolean
  disableDownload?: boolean
  disableBookmark?: boolean
  disableEditing?: boolean
  enableAnnotations?: boolean
  locale?: string
  performanceMode?: 'standard' | 'optimized' | 'memory-saver'
}

export interface PdfViewerEvents {
  pageChanged: (pageNumber: number) => void
  documentLoaded: (totalPages: number) => void
  textSelected: (selectedText: string) => void
  error: (error: string) => void
  strategyChanged?: (strategy: ViewerStrategy) => void
}

export type ViewerStrategy = 'official' | 'canvas' | 'auto'

export interface DocumentInfo {
  totalPages: number
  fileSize: number
  hasAnnotations: boolean
  hasComplexGraphics: boolean
  requiresEditing: boolean
  metadata?: PDFInfo
}

export interface PerformanceMetrics {
  loadTime: number
  memoryUsage: number
  renderTime: number
  cacheHitRate: number
  strategyScore?: {
    official: number
    canvas: number
  }
}

// PDF.js消息通信类型
export interface PdfJsMessage {
  type: 'PDF_PAGE_CHANGED' | 'PDF_DOCUMENT_LOADED' | 'PDF_TEXT_SELECTED' | 'PDF_ERROR' | 'INIT_VIEWER' | 'GO_TO_PAGE'
  data?: any
  pageNumber?: number
  totalPages?: number
  selectedText?: string
  message?: string
  config?: PdfViewerConfig
}

// 错误类型
export interface PdfViewerError extends Error {
  code?: string
  details?: any
  strategy?: ViewerStrategy
  fallbackAvailable?: boolean
}

// 缓存相关类型
export interface PdfPageCache {
  get(pageNumber: number, scale: number): HTMLCanvasElement | null
  set(pageNumber: number, scale: number, canvas: HTMLCanvasElement): void
  has(pageNumber: number, scale: number): boolean
  delete(pageNumber: number, scale?: number): boolean
  clear(): void
  size: number
  cleanup(keepPredicate?: (pageNumber: number) => boolean): void
}

// 搜索相关类型
export interface PdfSearchOptions {
  query: string
  caseSensitive?: boolean
  entireWord?: boolean
  highlightAll?: boolean
  findPrevious?: boolean
}

export interface PdfSearchResult {
  pageIndex: number
  matchIndex: number
  text: string
  rect: number[]
}

// 注释相关类型
export interface PdfAnnotationData {
  type: string
  rect: number[]
  contents: string
  author?: string
  modificationDate?: Date
  color?: string
  opacity?: number
}

export {}
