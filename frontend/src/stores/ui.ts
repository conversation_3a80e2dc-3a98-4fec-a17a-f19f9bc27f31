import { defineStore } from 'pinia'
import { ref, computed } from 'vue'



export interface LoadingState {
  isLoading: boolean
  message: string
  progress?: number
}

export interface LayoutState {
  sidebarCollapsed: boolean
  sidebarWidth: number
  showNavigator: boolean
  showControls: boolean
  fullscreen: boolean
}

export interface NotificationState {
  visible: boolean
  type: 'success' | 'warning' | 'info' | 'error'
  title: string
  message: string
  duration: number
}

export const useUIStore = defineStore('ui', () => {
  // Loading state
  const globalLoading = ref<LoadingState>({
    isLoading: false,
    message: '',
    progress: undefined
  })

  const uploadLoading = ref<LoadingState>({
    isLoading: false,
    message: '',
    progress: 0
  })

  const parseLoading = ref<LoadingState>({
    isLoading: false,
    message: '',
    progress: undefined
  })

  const analysisLoading = ref<LoadingState>({
    isLoading: false,
    message: '',
    progress: undefined
  })

  // Layout state
  const layout = ref<LayoutState>({
    sidebarCollapsed: false,
    sidebarWidth: 350,
    showNavigator: true,
    showControls: false,
    fullscreen: false
  })

  // Notification state
  const notification = ref<NotificationState>({
    visible: false,
    type: 'info',
    title: '',
    message: '',
    duration: 3000
  })

  // Theme state
  const theme = ref<'light' | 'dark'>('light')
  const primaryColor = ref('#409eff')

  // Responsive state
  const isMobile = ref(false)
  const screenWidth = ref(window.innerWidth)
  const screenHeight = ref(window.innerHeight)

  // Computed
  const isAnyLoading = computed(() => 
    globalLoading.value.isLoading ||
    uploadLoading.value.isLoading ||
    parseLoading.value.isLoading ||
    analysisLoading.value.isLoading
  )

  const currentLoadingMessage = computed(() => {
    if (globalLoading.value.isLoading) return globalLoading.value.message
    if (uploadLoading.value.isLoading) return uploadLoading.value.message
    if (parseLoading.value.isLoading) return parseLoading.value.message
    if (analysisLoading.value.isLoading) return analysisLoading.value.message
    return ''
  })

  const adaptiveLayout = computed(() => ({
    ...layout.value,
    sidebarWidth: isMobile.value ? Math.min(layout.value.sidebarWidth, screenWidth.value * 0.8) : layout.value.sidebarWidth,
    showNavigator: isMobile.value ? false : layout.value.showNavigator
  }))

  // Actions
  function setGlobalLoading(loading: boolean, message: string = '', progress?: number) {
    globalLoading.value = { isLoading: loading, message, progress }
  }

  function setUploadLoading(loading: boolean, message: string = '', progress: number = 0) {
    uploadLoading.value = { isLoading: loading, message, progress }
  }

  function setParseLoading(loading: boolean, message: string = '') {
    parseLoading.value = { isLoading: loading, message }
  }

  function setAnalysisLoading(loading: boolean, message: string = '') {
    analysisLoading.value = { isLoading: loading, message }
  }

  function updateLayout(updates: Partial<LayoutState>) {
    Object.assign(layout.value, updates)
  }

  function toggleSidebar() {
    layout.value.sidebarCollapsed = !layout.value.sidebarCollapsed
  }

  function setSidebarWidth(width: number) {
    layout.value.sidebarWidth = Math.max(250, Math.min(500, width))
  }

  function toggleNavigator() {
    layout.value.showNavigator = !layout.value.showNavigator
  }

  function toggleControls() {
    layout.value.showControls = !layout.value.showControls
  }

  function toggleFullscreen() {
    layout.value.fullscreen = !layout.value.fullscreen
    
    if (layout.value.fullscreen) {
      document.documentElement.requestFullscreen?.()
    } else {
      document.exitFullscreen?.()
    }
  }

  function showNotification(
    type: NotificationState['type'],
    title: string,
    message: string,
    duration: number = 3000
  ) {
    notification.value = {
      visible: true,
      type,
      title,
      message,
      duration
    }

    if (duration > 0) {
      setTimeout(() => {
        notification.value.visible = false
      }, duration)
    }
  }

  function hideNotification() {
    notification.value.visible = false
  }

  function setTheme(newTheme: 'light' | 'dark') {
    theme.value = newTheme
    document.documentElement.setAttribute('data-theme', newTheme)
  }

  function setPrimaryColor(color: string) {
    primaryColor.value = color
    document.documentElement.style.setProperty('--el-color-primary', color)
  }

  function updateScreenSize() {
    screenWidth.value = window.innerWidth
    screenHeight.value = window.innerHeight
    isMobile.value = window.innerWidth < 768
  }

  function resetUI() {
    globalLoading.value = { isLoading: false, message: '' }
    uploadLoading.value = { isLoading: false, message: '', progress: 0 }
    parseLoading.value = { isLoading: false, message: '' }
    analysisLoading.value = { isLoading: false, message: '' }
    notification.value.visible = false
  }

  // 工作流程状态管理
  const workflowStep = ref<'upload' | 'parse' | 'analyze' | 'display' | 'interact'>('upload')
  const workflowProgress = ref(0)

  function setWorkflowStep(step: typeof workflowStep.value) {
    workflowStep.value = step
    
    const stepProgress = {
      upload: 20,
      parse: 40,
      analyze: 60,
      display: 80,
      interact: 100
    }
    
    workflowProgress.value = stepProgress[step]
  }

  function nextWorkflowStep() {
    const steps: (typeof workflowStep.value)[] = ['upload', 'parse', 'analyze', 'display', 'interact']
    const currentIndex = steps.indexOf(workflowStep.value)
    if (currentIndex < steps.length - 1) {
      setWorkflowStep(steps[currentIndex + 1])
    }
  }

  function resetWorkflow() {
    setWorkflowStep('upload')
  }

  return {
    // State
    globalLoading,
    uploadLoading,
    parseLoading,
    analysisLoading,
    layout,
    notification,
    theme,
    primaryColor,
    isMobile,
    screenWidth,
    screenHeight,
    workflowStep,
    workflowProgress,

    // Computed
    isAnyLoading,
    currentLoadingMessage,
    adaptiveLayout,

    // Actions
    setGlobalLoading,
    setUploadLoading,
    setParseLoading,
    setAnalysisLoading,
    updateLayout,
    toggleSidebar,
    setSidebarWidth,
    toggleNavigator,
    toggleControls,
    toggleFullscreen,
    showNotification,
    hideNotification,
    setTheme,
    setPrimaryColor,
    updateScreenSize,
    resetUI,
    setWorkflowStep,
    nextWorkflowStep,
    resetWorkflow
  }
})
