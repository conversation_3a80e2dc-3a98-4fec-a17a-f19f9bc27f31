 import { defineStore } from 'pinia'
import { ref, computed } from 'vue'



export interface Document {
  id?: number
  filename: string
  originalName: string
  filePath: string
  fileSize: number
  uploadTime?: string
  status: 'UPLOADED' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
}

export const useDocumentStore = defineStore('document', () => {
  // State
  const currentDocument = ref<Document | null>(null)
  const uploadProgress = ref(0)
  const parseStatus = ref<'idle' | 'parsing' | 'completed' | 'error'>('idle')
  const documentContent = ref<string>('')
  const isLoading = ref(false)

  // Getters
  const hasDocument = computed(() => currentDocument.value !== null)
  const isProcessing = computed(() => parseStatus.value === 'parsing' || isLoading.value)
  const canAnalyze = computed(() => hasDocument.value && parseStatus.value === 'completed')

  // Actions
  function setCurrentDocument(document: Document) {
    currentDocument.value = document
  }

  function updateUploadProgress(progress: number) {
    uploadProgress.value = Math.max(0, Math.min(100, progress))
  }

  function setParseStatus(status: typeof parseStatus.value) {
    parseStatus.value = status
  }

  function setDocumentContent(content: string) {
    documentContent.value = content
  }

  function setLoading(loading: boolean) {
    isLoading.value = loading
  }

  function updateDocumentStatus(status: Document['status']) {
    if (currentDocument.value) {
      currentDocument.value.status = status
    }
  }

  function resetDocument() {
    currentDocument.value = null
    uploadProgress.value = 0
    parseStatus.value = 'idle'
    documentContent.value = ''
    isLoading.value = false
  }

  return {
    // State
    currentDocument,
    uploadProgress,
    parseStatus,
    documentContent,
    isLoading,
    
    // Getters
    hasDocument,
    isProcessing,
    canAnalyze,
    
    // Actions
    setCurrentDocument,
    updateUploadProgress,
    setParseStatus,
    setDocumentContent,
    setLoading,
    updateDocumentStatus,
    resetDocument
  }
})
