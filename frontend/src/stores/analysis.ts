import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AnalysisResult } from '@/api/analysis'



export interface ResultItem {
  id: string
  type: 'extract' | 'detect'
  title: string
  content: string
  description?: string
  severity?: string
  confidence?: number
  position?: {
    page: number
    paragraphIndex: number
    start: number
    end: number
    elementId: string
  }
  suggestion?: string
  createdTime: string
  attributes?: Record<string, any>
}

export interface ExtractResult extends ResultItem {
  type: 'extract'
  confidence: number
}

export interface DetectResult extends ResultItem {
  type: 'detect'
  severity: 'low' | 'medium' | 'high' | 'critical'
  suggestion?: string
}

export interface HighlightInfo {
  id: string
  type: 'extract' | 'detect'
  position: {
    start: number
    end: number
  }
  color?: string
}

export const useAnalysisStore = defineStore('analysis', () => {
  // State
  const currentDocumentId = ref<number | null>(null)
  const analysisResults = ref<AnalysisResult[]>([])
  const extractResults = ref<ResultItem[]>([])
  const detectResults = ref<ResultItem[]>([])
  const selectedItem = ref<ResultItem | null>(null)
  const currentHighlight = ref<HighlightInfo | null>(null)
  const analysisStatus = ref<'idle' | 'processing' | 'completed' | 'error'>('idle')
  const activeHighlights = ref<HighlightInfo[]>([])
  const isLoading = ref(false)

  // Filter state
  const filterOptions = ref({
    searchText: '',
    agentType: '' as 'extract' | 'detect' | '',
    severity: '',
    type: ''
  })

  // Computed
  const hasExtractResults = computed(() => extractResults.value.length > 0)
  const hasDetectResults = computed(() => detectResults.value.length > 0)
  const hasAnyResults = computed(() => hasExtractResults.value || hasDetectResults.value)
  const isAnalyzing = computed(() => analysisStatus.value === 'processing')
  const totalResults = computed(() => extractResults.value.length + detectResults.value.length)
  const extractCount = computed(() => extractResults.value.length)
  const detectCount = computed(() => detectResults.value.length)

  const filteredExtractResults = computed(() => {
    return filterResults(extractResults.value)
  })

  const filteredDetectResults = computed(() => {
    return filterResults(detectResults.value)
  })

  // Actions
  function setExtractResults(results: ExtractResult[]) {
    extractResults.value = results
  }

  function setDetectResults(results: DetectResult[]) {
    detectResults.value = results
  }

  function setAnalysisStatus(status: typeof analysisStatus.value) {
    analysisStatus.value = status
  }

  function setCurrentHighlight(highlight: HighlightInfo | null) {
    currentHighlight.value = highlight
  }

  function addHighlight(highlight: HighlightInfo) {
    const existingIndex = activeHighlights.value.findIndex(h => h.id === highlight.id)
    if (existingIndex >= 0) {
      activeHighlights.value[existingIndex] = highlight
    } else {
      activeHighlights.value.push(highlight)
    }
  }

  function removeHighlight(id: string) {
    const index = activeHighlights.value.findIndex(h => h.id === id)
    if (index >= 0) {
      activeHighlights.value.splice(index, 1)
    }
  }

  function clearHighlights() {
    activeHighlights.value = []
    currentHighlight.value = null
  }

  function resetAnalysis() {
    extractResults.value = []
    detectResults.value = []
    analysisStatus.value = 'idle'
    clearHighlights()
  }

  function setCurrentDocument(documentId: number) {
    if (currentDocumentId.value !== documentId) {
      currentDocumentId.value = documentId
      resetAnalysis()
    }
  }

  function setSelectedItem(item: ResultItem | null) {
    selectedItem.value = item
  }

  function setLoading(loading: boolean) {
    isLoading.value = loading
  }

  function updateFilterOptions(options: Partial<typeof filterOptions.value>) {
    Object.assign(filterOptions.value, options)
  }

  function processResults(results: AnalysisResult[]) {
    // 处理分析结果，转换为ResultItem格式
    // 这里可以根据实际需要实现具体的转换逻辑
  }

  function filterResults(results: ResultItem[]): ResultItem[] {
    let filtered = results

    // 搜索文本过滤
    if (filterOptions.value.searchText.trim()) {
      const keyword = filterOptions.value.searchText.toLowerCase()
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(keyword) ||
        item.content.toLowerCase().includes(keyword) ||
        (item.description && item.description.toLowerCase().includes(keyword))
      )
    }

    // 严重程度过滤
    if (filterOptions.value.severity) {
      filtered = filtered.filter(item => item.severity === filterOptions.value.severity)
    }

    // 类型过滤
    if (filterOptions.value.type) {
      filtered = filtered.filter(item =>
        item.attributes?.type === filterOptions.value.type
      )
    }

    return filtered
  }

  return {
    // State
    currentDocumentId,
    analysisResults,
    extractResults,
    detectResults,
    selectedItem,
    currentHighlight,
    analysisStatus,
    activeHighlights,
    isLoading,
    filterOptions,

    // Computed
    hasExtractResults,
    hasDetectResults,
    hasAnyResults,
    isAnalyzing,
    totalResults,
    extractCount,
    detectCount,
    filteredExtractResults,
    filteredDetectResults,

    // Actions
    setCurrentDocument,
    setSelectedItem,
    setLoading,
    updateFilterOptions,
    setExtractResults,
    setDetectResults,
    setAnalysisStatus,
    setCurrentHighlight,
    addHighlight,
    removeHighlight,
    clearHighlights,
    resetAnalysis,
    processResults
  }
})
