import request from '@/utils/request-chat'

import type { ApiResponse } from '@/types'
// const APP_ID = import.meta.env.VITE_AI_APP_ID
//获取历史记录

export const getHistory = (appId: string): Promise<ApiResponse<any>> => {
  return request.get(`/${appId}/chat/client/1/20`)
}
//新建对话获取新记录id
export const getNewHistoryId = (appId: string): Promise<ApiResponse<any>> => {
  return request.get(`/${appId}/chat/open`)
}
//获取历史对话中的对话记录
export const getChatList = (appId: string, id: number, order_asc: boolean): Promise<ApiResponse<any>> => {
  return request.get(`/${appId}/chat/${id}/chat_record/1/20?order_asc=${order_asc}`)
}
//编辑历史记录标题
export const editHistoryTitle = (appId: string, id: number, title: string): Promise<ApiResponse<any>> => {
  return request.put(`/${appId}/chat/client/${id}`, {
    abstract: title
  })
}
//删除历史记录
export const deleteHistory = (appId: string, id: number): Promise<ApiResponse<any>> => {
  return request.delete(`/${appId}/chat/client/${id}`)
}
