import request from './request'
import type { ApiResponse, StructuredAnalysisResult, ExtractItem, DetectItem } from '@/types'



/**
 * 分析结果类型定义
 */
export interface AnalysisResult {
  id: number
  documentId: number
  agentType: string
  resultContent: Record<string, any>
  processingTime: number
  apiResponseCode: number
  createdTime: string
}

export interface AnalysisStatistics {
  totalCount: number
  successCount: number
  failedCount: number
  avgProcessingTime: number
  maxProcessingTime: number
  minProcessingTime: number
  agentTypeStats?: Array<{
    agentType: string
    count: number
  }>
}

export interface ExtractedItem {
  type: string
  title: string
  content: string
  position?: PositionInfo
  confidence: number
  importance: string
  attributes: Record<string, any>
}

export interface DetectedIssue {
  type: string
  title: string
  description: string
  severity: string
  position?: PositionInfo
  suggestion: string
  impact: string
  attributes: Record<string, any>
}

export interface PositionInfo {
  start: number
  end: number
  page: number
  paragraphIndex: number
  lineNumber: number
  elementId: string
}

/**
 * 触发文档分析
 */
export const analyzeDocument = (documentId: number): Promise<ApiResponse<{
  documentId: number
  status: string
  message: string
}>> => {
  return request.post(`/maxkb/analyze/${documentId}`)
}

/**
 * 获取分析状态
 */
export const getAnalysisStatus = (documentId: number): Promise<ApiResponse<{
  documentId: number
  status: string
  isProcessing: boolean
  isCompleted: boolean
}>> => {
  return request.get(`/maxkb/status/${documentId}`)
}

/**
 * 获取文档的分析结果（原始格式）
 */
export const getDocumentAnalysisResults = (documentId: number): Promise<ApiResponse<{
  data: AnalysisResult[]
  count: number
}>> => {
  return request.get(`/analysis/document/${documentId}`)
}

/**
 * 获取结构化分析结果（新格式）
 */
export const getStructuredAnalysisResults = (documentId: number): Promise<ApiResponse<StructuredAnalysisResult>> => {
  return request.get(`/analysis/structured/${documentId}`)
}

/**
 * 获取特定类型的分析结果
 */
export const getAnalysisResult = (documentId: number, agentType: 'EXTRACT' | 'DETECT'): Promise<ApiResponse<AnalysisResult>> => {
  return request.get(`/analysis/document/${documentId}/${agentType}`)
}

/**
 * 分页查询分析结果
 */
export const getAnalysisResultsPage = (params: {
  page?: number
  size?: number
  documentId?: number
  agentType?: string
  startTime?: string
  endTime?: string
}): Promise<ApiResponse<{
  data: AnalysisResult[]
  total: number
  page: number
  size: number
  pages: number
}>> => {
  return request.get('/analysis/page', { params })
}

/**
 * 获取分析统计信息
 */
export const getAnalysisStatistics = (params?: {
  documentId?: number
  startTime?: string
  endTime?: string
}): Promise<ApiResponse<AnalysisStatistics>> => {
  return request.get('/analysis/statistics', { params })
}

/**
 * 删除文档的分析结果
 */
export const deleteDocumentAnalysisResults = (documentId: number): Promise<ApiResponse<{
  deletedCount: number
  message: string
}>> => {
  return request.delete(`/analysis/document/${documentId}`)
}

/**
 * 清理过期的分析结果
 */
export const cleanupExpiredResults = (retentionDays: number = 30): Promise<ApiResponse<{
  cleanedCount: number
  retentionDays: number
  message: string
}>> => {
  return request.post('/analysis/cleanup', null, {
    params: { retentionDays }
  })
}

/**
 * 检查分析结果是否存在
 */
export const checkAnalysisResultExists = (documentId: number, agentType: 'EXTRACT' | 'DETECT'): Promise<ApiResponse<{
  exists: boolean
  documentId: number
  agentType: string
}>> => {
  return request.get(`/analysis/exists/${documentId}/${agentType}`)
}

/**
 * 获取最近的分析结果
 */
export const getRecentAnalysisResults = (limit: number = 10): Promise<ApiResponse<{
  data: AnalysisResult[]
  count: number
  limit: number
}>> => {
  return request.get('/analysis/recent', {
    params: { limit }
  })
}

/**
 * 解析提取结果
 */
export const parseExtractedData = (resultContent: Record<string, any>): ExtractedItem[] => {
  try {
    const extractedData = resultContent.extractedData
    if (Array.isArray(extractedData)) {
      return extractedData.map((item: any) => ({
        type: item.type || 'unknown',
        title: item.title || item.name || '未命名',
        content: item.content || item.value || '',
        position: item.position,
        confidence: item.confidence || 0,
        importance: item.importance || 'normal',
        attributes: item.attributes || {}
      }))
    }
  } catch (error) {
    console.error('解析提取数据失败:', error)
  }
  return []
}

/**
 * 解析检测问题
 */
export const parseDetectedIssues = (resultContent: Record<string, any>): DetectedIssue[] => {
  try {
    const detectedIssues = resultContent.detectedIssues
    if (Array.isArray(detectedIssues)) {
      return detectedIssues.map((issue: any) => ({
        type: issue.type || 'unknown',
        title: issue.title || issue.name || '未命名问题',
        description: issue.description || issue.content || '',
        severity: issue.severity || 'medium',
        position: issue.position,
        suggestion: issue.suggestion || '',
        impact: issue.impact || '',
        attributes: issue.attributes || {}
      }))
    }
  } catch (error) {
    console.error('解析检测问题失败:', error)
  }
  return []
}

/**
 * 获取分析结果摘要
 */
export const getAnalysisResultSummary = (result: AnalysisResult): string => {
  const agentType = result.agentType
  const content = result.resultContent
  
  if (agentType === 'EXTRACT') {
    const extractedData = parseExtractedData(content)
    return `提取了 ${extractedData.length} 项数据`
  } else if (agentType === 'DETECT') {
    const detectedIssues = parseDetectedIssues(content)
    return `检测到 ${detectedIssues.length} 个问题`
  }
  
  return '分析完成'
}

/**
 * 格式化处理时间
 */
export const formatProcessingTime = (processingTime: number): string => {
  if (processingTime < 1000) {
    return `${processingTime}ms`
  } else if (processingTime < 60000) {
    return `${(processingTime / 1000).toFixed(1)}s`
  } else {
    const minutes = Math.floor(processingTime / 60000)
    const seconds = Math.floor((processingTime % 60000) / 1000)
    return `${minutes}m ${seconds}s`
  }
}

/**
 * 获取严重程度颜色
 */
export const getSeverityColor = (severity: string): string => {
  const colorMap: Record<string, string> = {
    'low': '#52c41a',
    'medium': '#faad14',
    'high': '#ff4d4f',
    'critical': '#a8071a'
  }
  return colorMap[severity.toLowerCase()] || '#1890ff'
}

/**
 * 获取智能体类型描述
 */
export const getAgentTypeDescription = (agentType: string): string => {
  const descriptionMap: Record<string, string> = {
    'EXTRACT': '提取智能体',
    'DETECT': '检测智能体'
  }
  return descriptionMap[agentType] || agentType
}

/**
 * 渲染原文引用列表
 */
export const renderSourceReferences = (sources: Array<{
  referenceIndex: string
  location: string
  content: string
}>): string => {
  if (!sources || sources.length === 0) {
    return '暂无原文引用'
  }

  return sources.map(source => {
    // content 字段已经包含完整格式化信息
    return source.content
  }).join('\n')
}

/**
 * 获取提取项状态颜色
 */
export const getExtractItemStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'success': '#52c41a',
    'warning': '#faad14',
    'error': '#ff4d4f',
    'normal': '#1890ff'
  }
  return colorMap[status.toLowerCase()] || '#1890ff'
}

/**
 * 获取检测项结果颜色
 */
export const getDetectItemResultColor = (result: string): string => {
  const colorMap: Record<string, string> = {
    '正常结果': '#52c41a',
    '异常结果': '#ff4d4f'
  }
  return colorMap[result] || '#1890ff'
}

/**
 * 格式化检测项显示文本
 */
export const formatDetectItemDisplay = (item: DetectItem): {
  title: string
  description: string
  statusText: string
  statusColor: string
} => {
  return {
    title: item.title,
    description: item.reason || '无详细说明',
    statusText: item.result || '未知',
    statusColor: getDetectItemResultColor(item.result)
  }
}

/**
 * 格式化提取项显示文本
 */
export const formatExtractItemDisplay = (item: ExtractItem): {
  title: string
  description: string
  sourceCount: number
  sourcesText: string
} => {
  return {
    title: item.title,
    description: `共 ${item.sources.length} 处原文引用`,
    sourceCount: item.sources.length,
    sourcesText: renderSourceReferences(item.sources)
  }
}
