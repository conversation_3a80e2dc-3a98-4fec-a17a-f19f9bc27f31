import request from './request'
import type { ApiResponse, DocumentInfo, UploadProgress } from '@/types'



/**
 * 上传文档
 */
export const uploadDocument = (file: File, onProgress?: (progress: UploadProgress) => void): Promise<ApiResponse<DocumentInfo>> => {
  const formData = new FormData()
  formData.append('file', file)

  return request.post('/documents/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: progressEvent => {
      if (onProgress && progressEvent.total) {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress({
          percent,
          status: 'uploading',
          message: `上传中... ${percent}%`
        })
      }
    }
  })
}

/**
 * 获取文档列表
 */
export const getDocuments = (params?: {
  page?: number
  size?: number
  status?: string
}): Promise<
  ApiResponse<{
    list: DocumentInfo[]
    total: number
    page: number
    size: number
  }>
> => {
  return request.get('/documents', { params })
}

/**
 * 获取文档详情
 */
export const getDocument = (id: number): Promise<ApiResponse<DocumentInfo>> => {
  return request.get(`/documents/${id}`)
}

/**
 * 删除文档
 */
export const deleteDocument = (id: number): Promise<ApiResponse<void>> => {
  return request.delete(`/documents/${id}`)
}

/**
 * 获取上传配置
 */
export const getUploadConfig = (): Promise<
  ApiResponse<{
    maxFileSize: number
    allowedTypes: string[]
    allowedExtensions: string[]
    maxFileSizeText: string
  }>
> => {
  return request.get('/documents/upload-config')
}

/**
 * 批量删除文档
 */
export const batchDeleteDocuments = (ids: number[]): Promise<ApiResponse<void>> => {
  return request.post('/documents/batch-delete', { ids })
}

/**
 * 获取文档统计信息
 */
export const getDocumentStatistics = (params?: {
  startTime?: string
  endTime?: string
}): Promise<
  ApiResponse<{
    totalCount: number
    completedCount: number
    processingCount: number
    failedCount: number
    totalSize: number
    successRate: number
  }>
> => {
  return request.get('/documents/statistics', { params })
}

/**
 * 重新处理文档
 */
export const reprocessDocument = (id: number): Promise<ApiResponse<void>> => {
  return request.post(`/documents/${id}/reprocess`)
}

/**
 * 下载文档
 */
export const downloadDocument = (id: number): Promise<Blob> => {
  return request.get(`/documents/${id}/download`, {
    responseType: 'blob'
  })
}

/**
 * 检查文档处理状态
 */
export const checkDocumentStatus = (
  id: number
): Promise<
  ApiResponse<{
    status: string
    progress: number
    message?: string
    error?: string
  }>
> => {
  return request.get(`/documents/${id}/status`)
}

/**
 * 解析文档内容
 */
export const parseDocument = (id: number): Promise<ApiResponse<DocumentContent>> => {
  return request.post(`/documents/${id}/parse`)
}

/**
 * 获取文档内容
 */
export const getDocumentContent = (id: number): Promise<ApiResponse<DocumentContent>> => {
  return request.get(`/documents/${id}/content`)
}

/**
 * 重新解析文档
 */
export const reparseDocument = (id: number): Promise<ApiResponse<DocumentContent>> => {
  return request.post(`/documents/${id}/reparse`)
}

/**
 * 获取文档解析状态
 */
export const getParseStatus = (
  id: number
): Promise<
  ApiResponse<{
    documentId: number
    status: string
    statusText: string
    isParsed: boolean
    canParse: boolean
  }>
> => {
  return request.get(`/documents/${id}/parse-status`)
}

export const getPdfDocument = (
  id: number,
  options?: {
    onProgress?: (progress: number) => void
    timeout?: number
  }
): Promise<Blob> => {
  return request.get(`/documents/${id}/pdf`, {
    responseType: 'blob',
    timeout: options?.timeout || 30000, // 30秒超时
    onDownloadProgress: progressEvent => {
      if (options?.onProgress && progressEvent.total) {
        const progress = progressEvent.loaded / progressEvent.total
        options.onProgress(progress)
      }
    }
  })
}
//获取文档pdf文件流 - 支持 Blob 格式
export const getDocumentPdf = (
  id: number,
  options?: {
    responseType?: 'blob' | 'arraybuffer' | 'json'
    onProgress?: (progress: number) => void
    timeout?: number
  }
): Promise<Blob | ArrayBuffer | any> => {
  const responseType = options?.responseType || 'arraybuffer' // 修正：使用小写 arraybuffer
  return request.get(`/documents/${id}/pdf`, {
    responseType: responseType,
    timeout: options?.timeout || 30000,
    onDownloadProgress: progressEvent => {
      if (options?.onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        options.onProgress(progress)
      }
    }
  })
}
