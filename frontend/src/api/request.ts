import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'
import { getAccessToken, removeToken, setToken } from '@/utils/auth'
import { getUrl } from '@/utils/url'
const baseUrl = getUrl()


// Create axios instance
const request: AxiosInstance = axios.create({
  baseURL: baseUrl, //import.meta.env.VITE_APP_BASE_API
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 是否需要设置 token
    // const isToken = (config.headers || {}).isToken === false
    // if (getAccessToken() && !isToken) {
    //   // 'Bearer ' + getAccessToken()
    //   ;(config as Recordable).headers.Authorization = getAccessToken() // 让每个请求携带自定义token
    // }
    // Add loading logic here if needed
    console.log('Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data, status, config } = response

    if (status === 200) {
      // 对于 ArrayBuffer 响应，直接返回原始数据
      if (config.responseType === 'arraybuffer' || config.responseType === 'blob') {
        console.log('返回二进制数据:', {
          type: config.responseType,
          size: data instanceof ArrayBuffer ? data.byteLength :
                data instanceof Blob ? data.size : 'unknown'
        })
        return data
      }
      return data
    } else {
      ElMessage.error(`请求失败: ${status}`)
      return Promise.reject(new Error(`HTTP ${status}`))
    }
  },
  error => {
    console.error('Response Error:', error)

    if (error.response) {
      const { status, data } = error.response
      let message = `请求失败: ${status}`

      if (data?.message) {
        message = data.message
      } else if (status === 404) {
        message = '请求的资源不存在'
      } else if (status === 500) {
        message = '服务器内部错误'
      } else if (status >= 400 && status < 500) {
        message = '请求参数错误'
      }

      ElMessage.error(message)
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

export default request
