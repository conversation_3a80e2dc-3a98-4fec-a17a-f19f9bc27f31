<template>
  <div class="problem-container h-full">
    <h3 class="problem-title">常见问题</h3>

    <!-- 搜索栏 -->
    <div class="search-container">
      <el-input v-model="searchKeyword" placeholder="搜索问题..." prefix-icon="Search" clearable @input="handleSearch" @clear="clearSearch" class="search-input" />
    </div>

    <!-- 无搜索结果提示 -->
    <div v-if="searchKeyword && displayProblems.length == 0" class="no-results">
      <el-empty description="未找到相关问题" :image-size="100" />
    </div>
    <!-- 手风琴折叠面板 -->
    <div class="accordion-container w-full p-20px" v-else>
      <el-collapse v-model="activeName" accordion v-for="category in displayProblems" :key="category.key" class="category-tab">
        <el-collapse-item :title="category.name" :name="category.key">
          <div v-for="(item, index) in category.data" :key="index" @click="selectProblem(item)">
            <div class="accordion-header flex items-center flex-wrap mb-20px">
              <div class="problem-number flex items-center justify-center mr-6px">问</div>
              <div class="problem-text flex-1 flex flex-wrap">{{ item.problem }}</div>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script setup lang="ts" name="Problem">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ArrowDown, Search } from '@element-plus/icons-vue'

// 定义数据类型
interface ProblemItem {
  problem: string
  answer: string
}

interface Category {
  name: string
  key: string
  data: ProblemItem[]
}

// 定义 emit 事件
const emit = defineEmits<{
  selectProblem: [item: ProblemItem]
}>()
const activeName = ref('')

// 响应式数据
const categories = ref<Category[]>([])
const originalCategories = ref<Category[]>([]) // 保存原始数据
const searchKeyword = ref<string>('')
const searchResults = ref<Category[]>([])

// 防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// 计算显示的问题列表（搜索结果或原始分类）
const displayProblems = computed(() => {
  return searchKeyword.value ? searchResults.value : originalCategories.value
})

// 搜索处理（带防抖）
const handleSearch = (value: string) => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 如果搜索框为空，立即清空结果
  if (!value.trim()) {
    searchResults.value = []
    activeName.value = ''
    return
  }

  // 防抖处理，300ms后执行搜索
  searchTimer = setTimeout(() => {
    performSearch(value)
  }, 300)
}

// 执行搜索
const performSearch = (value: string) => {
  console.log('搜索关键词:', value)

  const keyword = value.toLowerCase()
  const filteredCategories: Category[] = []

  // 遍历原始数据进行搜索，不修改原始数据
  originalCategories.value.forEach(category => {
    const matchedProblems: ProblemItem[] = []

    category.data.forEach(item => {
      // 搜索问题标题和答案内容
      if (item.problem.toLowerCase().includes(keyword)) {
        matchedProblems.push(item)
      }
    })

    // 如果该分类下有匹配的问题，创建新的分类对象
    if (matchedProblems.length > 0) {
      filteredCategories.push({
        ...category,
        data: matchedProblems
      })
    }
  })

  console.log('搜索结果:', filteredCategories)
  searchResults.value = filteredCategories

  // 自动展开第一个有结果的分类
  activeName.value = filteredCategories.length > 0 ? filteredCategories[0].key : ''
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
  activeName.value = ''
}

// 选择问题
const selectProblem = (item: ProblemItem) => {
  console.log('选择的问题:', item.problem)
  // 向父组件传递选中的问题
  emit('selectProblem', item)
}

// 加载问题数据
const loadProblems = async () => {
  try {
    // 动态导入 JSON 文件
    const data = await import('../../../../public/problem.json')
    const loadedData = data.default || data

    // 保存原始数据的深拷贝，避免被搜索逻辑修改
    originalCategories.value = JSON.parse(JSON.stringify(loadedData))
    categories.value = loadedData

    console.log('加载的数据:', originalCategories.value)
  } catch (error) {
    console.error('加载问题数据失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadProblems()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
})
</script>

<style scoped lang="scss">
.problem-container {
  padding-bottom: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.problem-title {
  padding: 0 20px 20px 20px;
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(90deg, rgba(0, 186, 173, 1) 0%, rgba(5, 122, 255, 1) 100%);
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}

// 搜索栏样式
.search-container {
  padding: 0 20px;

  .search-input {
    :deep(.el-input__wrapper) {
      border-radius: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;

      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
      }

      &.is-focus {
        box-shadow: 0 2px 12px rgba(23, 162, 184, 0.3);
        border-color: #17a2b8;
      }
    }
  }
}

:deep.category-tab {
  &.el-collapse {
    border: none !important;
  }
  .el-collapse-item {
    padding: 0 10px;
    border-radius: 8px;

    margin-bottom: 10px;
    background: rgba(255, 255, 255, 1);

    border: 1px solid;
    box-sizing: border-box;
    border-image: linear-gradient(to right, rgba(59, 102, 247, 1) 0%, rgba(0, 186, 173, 1) 100%) 1;
  }
  .el-collapse-item__title {
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(to right, rgba(0, 186, 173, 1) 0%, rgba(5, 122, 255, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;

    /* 确保渐变效果在所有浏览器中都能正常显示 */
    display: inline-block;

    /* 为不支持背景裁剪的浏览器提供备用颜色 */
    @supports not (-webkit-background-clip: text) {
      color: #17a2b8;
      background: none;
    }
  }
  .el-collapse-item__content {
    padding-bottom: 0 !important;
  }
  .problem-number {
    width: 26px;
    height: 26px;
    border-radius: 59%;
    background: rgba(60, 100, 255, 1);
    font-size: 10px;
    color: #fff;
  }
  .problem-text {
    color: rgba(42, 130, 228, 1);
    cursor: pointer;
    &:hover {
      text-decoration: underline;
    }
  }
  .el-collapse-item__arrow {
    width: 18px;
    height: 18px;
    color: #fff;
    border-radius: 50%;
    background: linear-gradient(90deg, rgba(59, 102, 247, 1) 0%, rgba(0, 186, 173, 1) 100%);
  }
}

// 无结果提示
.no-results {
  padding: 20px;
  text-align: center;
}
</style>
