<template>
  <div class="client-chat" style="height: 100%">
    <!-- 加载更多 -->
    <div v-if="hasMore" class="load-more">
      <el-button v-if="!loading" type="primary" link @click="loadMore">加载更多</el-button>
      <el-icon v-else class="is-loading"><Loading /></el-icon>
    </div>
    <div v-if="msgList.length > 0">
      <div class="qa-item" v-for="(item, index) in msgList" :key="item.id || item.chat_id || index">
        <!-- 问题 -->
        <div class="question-item mb-16px" v-if="item.problem_text">
          <el-icon v-if="item.is_loading" class="qs-loading is-loading"><Loading /></el-icon>

          <div class="question-text" style="max-width: 600px" v-html="renderMarkdown(item.problem_text)" />
          <div class="question-avatar">
            <img class="user-avatar" src="@/assets/images/user.png" />
          </div>
        </div>
        <!-- 答案 -->
        <div class="answer-item" v-if="item.answer_text || item.loading_message || item.isTyped">
          <!-- 头像 -->
          <div class="answer-avatar">
            <img class="robot-avatar" src="@/assets/images/robot.png" />
          </div>
          <!-- 答案信息 -->
          <div class="answer-info" :ref="el => setItemRef(el, item.id)">
            <div class="loading" v-if="item.loading_message">正在思考中</div>
            <!-- Markdown渲染 -->
            <div v-if="item.isTyped" :ref="el => setTypewriterRef(el, item)" class="typewriter-container"></div>
            <div v-else class="answer-md" v-html="renderMarkdown(item.answer_text)" />
          </div>
        </div>
        <!-- 时间戳 -->
        <div class="timestamp mb-16px">
          {{ item.update_time ? formatTime(item.update_time) : '' }}
        </div>
      </div>
    </div>
    <!-- 无数据 -->
    <div v-else class="no-data flex h-full flex-col justify-center pb-60px">
      <div class="top flex items-center justify-center mt-20px">
        <img src="@/assets/images/title.png" alt="" />
      </div>
      <h3 class="introduce flex items-center justify-center">您好!很高兴为您服务，有什么问题您可以问我哦，我会尽力帮您解答!</h3>
      <div class="ai-introduce-box">
        <div class="introduce-info">
          <p>【全天候服务】公共资源交易智能客服，7x24小时在线答疑,开启人机交互全新时代~</p>
          <p>【覆盖全面】政策法规、流程指引、业务办理，一键咨询,秒速响应，让公共资源交易更省心、更便捷!~</p>

          <img src="@/assets/images/divice.png" alt="" class="w-full my-20px" />
          <div class="introduce-bottom flex items-center justify-between">
            <ul class="pt-8px">
              <li class="flex items-center pb-20px">
                <img src="@/assets/images/icon1.png" alt="" class="w-64px" />
                <div class="ml-10px">
                  <div class="text-black font-bold text-18px">办事指南</div>
                  <div class="mt-2px">政策明晰，AI 速答，流程易懂，指引精准。</div>
                </div>
              </li>
              <li class="flex items-center pb-20px">
                <img src="@/assets/images/icon2.png" alt="" class="w-64px" />
                <div class="ml-10px">
                  <div class="text-black font-bold text-18px">信息检索</div>
                  <div class="mt-2px">通过自然语言处理等技术，实现信息的智能检索和精准推送!</div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="ClientChat" lang="ts">
import { inject, nextTick } from 'vue'
import { cloneDeep } from 'lodash-es'
import MarkdownIt from 'markdown-it'
import moment from 'moment'
import elementResizeDetectorMaker from 'element-resize-detector'
import { scrollToBottom } from '@/utils/util'
import { MESSAGE_TYPE, ACCESS_TYPE } from '@/constants'
import { sseCls } from '@/utils/sse'
import Typed from 'typed.js'

// 存储打字机实例的Map，key为消息ID
const typedInstances = new Map()
// 存储打字机元素引用的Map
const typewriterElements = new Map()
// 初始化 markdown-it 并配置 MaxKB 标签支持
const md = new MarkdownIt({
  html: true,
  linkify: false,
  breaks: true,
  typographer: true
})

// 创建 MaxKB 自定义标签插件
const maxkbPlugin = md => {
  // 存储快速问题列表
  const quickQuestions = []

  // 自定义渲染规则
  const originalRender = md.render.bind(md)

  md.render = function (src, env) {
    // 重置快速问题列表
    quickQuestions.length = 0

    // 处理 quick_question 标签
    src = src.replace(/<quick_question>([\s\S]*?)<\/quick_question>/g, (match, content) => {
      const question = content.trim()
      if (question) {
        quickQuestions.push(question)
        return `<div class="maxkb-quick-question problem-button mt-4px mb-8px flex items-center cursor-pointer" data-question="${md.utils.escapeHtml(question)}" onclick="handleQuickQuestion('${md.utils.escapeHtml(question)}')">
                <span class="mr-8px "><svg class="icon"  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="m199.04 672.64 193.984 112 224-387.968-193.92-112-224 388.032zm-23.872 60.16 32.896 148.288 144.896-45.696zM455.04 229.248l193.92 112 56.704-98.112-193.984-112-56.64 98.112zM104.32 708.8l384-665.024 304.768 175.936L409.152 884.8h.064l-248.448 78.336zm384 254.272v-64h448v64h-448z"></path></svg></span>${md.utils.escapeHtml(question)}</div>`
      }
      return ''
    })

    // 处理其他 MaxKB 标签
    src = src.replace(/\{\{([^}]+)\}\}/g, (match, variable) => {
      const trimmed = variable.trim()

      // 处理知识库引用 {{kb:id}}
      if (trimmed.startsWith('kb:')) {
        const kbId = trimmed.substring(3)
        return `<span class="maxkb-kb-ref" data-kb-id="${md.utils.escapeHtml(kbId)}">📚 知识库: ${md.utils.escapeHtml(kbId)}</span>`
      }

      // 处理文档引用 {{doc:id}}
      if (trimmed.startsWith('doc:')) {
        const docId = trimmed.substring(4)
        return `<span class="maxkb-doc-ref" data-doc-id="${md.utils.escapeHtml(docId)}">📄 文档: ${md.utils.escapeHtml(docId)}</span>`
      }

      // 处理普通变量
      return `<span class="maxkb-variable" data-variable="${md.utils.escapeHtml(trimmed)}">${md.utils.escapeHtml(trimmed)}</span>`
    })

    // 处理提示框标签
    src = src.replace(/::: (tip|warning|danger)\s+(.*?)\n([\s\S]*?):::/g, (match, type, title, content) => {
      return `<div class="maxkb-${type}">
        <div class="maxkb-${type}-title">${md.utils.escapeHtml(title.trim())}</div>
        <div class="maxkb-${type}-content">${md.render(content.trim())}</div>
      </div>`
    })

    const result = originalRender(src, env)

    return result
  }
}

// 使用插件
md.use(maxkbPlugin)

// 添加链接新窗口打开
const defaultRender =
  md.renderer.rules.link_open ||
  function (tokens, idx, options, env, self) {
    return self.renderToken(tokens, idx, options)
  }
md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
  tokens[idx].attrPush(['target', '_blank'])
  return defaultRender(tokens, idx, options, env, self)
}

const props = defineProps({
  // 如果需要的话可以定义props
  list: {
    type: Array,
    default: () => []
  }
})

// 新增 emit 定义
const emit = defineEmits(['loadMore', 'typingComplete', 'quickQuestion'])

// 新增响应式状态
const loading = ref(false)
const historyLoading = ref(false)
const timestampGap = ref(5 * 60) // 两次问题间隔大于5min，则展示时间戳
const msgList = ref([])

const chatBoxHeight = ref(document.body.clientHeight)
const jsScrolling = ref(false)
const userScrolling = ref(false)
const hasMore = ref(false)
const isFirstLoad = ref(true)

// 事件总线
const eventHub = inject('eventHub')

// 获取父组件的 scrollbar 实例
const scrollbarRef = inject('scrollbarRef', null)

// 工具方法
const formatTime = timestamp => {
  return moment(new Date(timestamp)).format('YYYY-MM-DD HH:mm')
}

// 安全的滚动操作函数
const safeScrollToBottom = () => {
  if (scrollbarRef.value && scrollbarRef.value.wrapRef) {
    try {
      scrollbarRef.value.setScrollTop(scrollbarRef.value.wrapRef.scrollHeight)
    } catch (error) {
      console.warn('滚动操作失败:', error)
    }
  }
}

// 安全的滚动事件监听器添加/移除
const addScrollListener = () => {
  if (scrollbarRef.value && scrollbarRef.value.wrapRef) {
    try {
      scrollbarRef.value.wrapRef.addEventListener('scroll', handleScroll)
    } catch (error) {
      console.warn('添加滚动监听器失败:', error)
    }
  }
}

const removeScrollListener = () => {
  if (scrollbarRef.value && scrollbarRef.value.wrapRef) {
    try {
      scrollbarRef.value.wrapRef.removeEventListener('scroll', handleScroll)
    } catch (error) {
      console.warn('移除滚动监听器失败:', error)
    }
  }
}

const renderMarkdown = content => {
  return md.render(content || '')
}

const setItemRef = (el, recordId) => {
  if (el) {
    el.setAttribute('id', recordId)
  }
}

// 设置打字机元素引用
const setTypewriterRef = (el, item) => {
  if (el && item.chat_id) {
    typewriterElements.set(item.chat_id, el)
    // 如果元素已经存在且消息需要打字机效果，立即初始化
    if (item.isTyped && item.answer_text) {
      nextTick(() => {
        initTypewriter(item)
      })
    }
  }
}

// 初始化打字机效果
const initTypewriter = item => {
  const element = typewriterElements.get(item.chat_id)
  if (!element || !item.answer_text) return

  // 如果已经有打字机实例，先销毁
  const existingTyped = typedInstances.get(item.chat_id)
  if (existingTyped) {
    existingTyped.destroy()
    typedInstances.delete(item.chat_id)
  }

  // 清空元素内容
  element.innerHTML = ''

  // 创建新的打字机实例
  const typed = new Typed(element, {
    strings: [item.answer_text],
    typeSpeed: 30, // 打字速度 (毫秒)
    backSpeed: 0, // 不需要删除效果
    showCursor: false, // 显示光标
    cursorChar: '|', // 光标字符
    autoInsertCss: true, // 自动插入CSS
    loop: false, // 不循环
    startDelay: 200, // 开始前延迟
    fadeOut: false, // 不淡出
    smartBackspace: false, // 不智能删除
    onComplete: self => {
      // 打字完成后的回调
      console.log('打字机效果完成:', item.chat_id)
      // 打字完成后隐藏光标
      setTimeout(() => {
        if (self.cursor) {
          self.cursor.style.display = 'none'
        }
      }, 1000)

      // 通知父组件打字机效果完成
      emit('typingComplete', item.chat_id)
    },
    onStringTyped: () => {
      // 每个字符串打完后的回调
      console.log('字符串打字完成')
    }
  })

  // 存储实例
  typedInstances.set(item.chat_id, typed)
}

// 清理打字机实例
const destroyTypewriter = chatId => {
  const typed = typedInstances.get(chatId)
  if (typed) {
    typed.destroy()
    typedInstances.delete(chatId)
  }
  typewriterElements.delete(chatId)
}

// 手动触发打字机效果的方法
const triggerTypewriter = (chatId, text) => {
  const element = typewriterElements.get(chatId)
  if (!element) {
    console.warn('找不到打字机元素:', chatId)
    return
  }

  // 创建临时消息对象
  const tempItem = {
    chat_id: chatId,
    problem_text: text,
    isTyped: true
  }

  initTypewriter(tempItem)
}

// 测试历史消息显示的方法
const testHistoryMessage = () => {
  console.log('【client-chat】当前消息列表:', msgList.value)
  console.log('【client-chat】消息列表长度:', msgList.value.length)
  msgList.value.forEach((msg, index) => {
    console.log(`【client-chat】消息 ${index}:`, {
      id: msg.id,
      chat_id: msg.chat_id,
      problem_text: msg.problem_text,
      answer_text: msg.answer_text,
      update_time: msg.update_time,
      isTyped: msg.isTyped
    })
  })
}

// 处理快速问题点击
const handleQuickQuestion = question => {
  console.log('点击快速问题:', question)
  // 发射事件给父组件
  emit('quickQuestion', question)
}

// 将处理函数暴露到全局，供 HTML 中的 onclick 调用
if (typeof window !== 'undefined') {
  window.handleQuickQuestion = handleQuickQuestion
}

// 暴露方法给父组件使用
defineExpose({
  triggerTypewriter,
  destroyTypewriter,
  initTypewriter,
  testHistoryMessage,
  handleQuickQuestion
})

// 监听用户端/管理端体验侧的ws事件
const listenClientAndManageEvent = () => {
  // 从缓存获取机器人信息
  let cachedConfig = null
  if (ACCESS_TYPE === 'sse') {
    cachedConfig = sseCls.sseQueryConfigInfo()
  } else {
  }
  // 监听答案消息队列变更事件
  eventHub.on('client_msgContentChange', res => {
    console.log('【client-chat】收到消息更新事件:', res)
    const { chatsContent, type } = res
    renderMsgList(chatsContent, type)
  })
}

// 监听公共的ws事件
const listenCommonEvent = () => {
  eventHub.on('data_history', () => {
    historyLoading.value = false
  })

  eventHub.on('data_historyError', () => {
    historyLoading.value = false
  })
}

// 渲染消息会话页面
const renderMsgList = (data, type) => {
  console.log('【client-chat】renderMsgList called with type:', type, 'data:', data)
  const noScrollEvt = [MESSAGE_TYPE.HISTORY, MESSAGE_TYPE.STOP, MESSAGE_TYPE.WORKBENCH_HISTORY, MESSAGE_TYPE.FEEDBACK]
  const list = data.map(el => ({ ...el, showPop: true }))
  msgList.value = cloneDeep(list)
  console.log('【client-chat】Updated msgList:', msgList.value)

  nextTick(() => {
    // 处理打字机效果
    list.forEach(item => {
      if (item.isTyped && item.problem_text) {
        // 延迟一点时间确保DOM已经渲染
        setTimeout(() => {
          initTypewriter(item)
        }, 100)
      }
    })

    // 使用 Element Plus scrollbar 的滚动方法
    if (!userScrolling.value && !noScrollEvt.includes(type)) {
      jsScrolling.value = true
      // 滚动到底部 - 使用安全函数
      safeScrollToBottom()
    }
    if (msgList.value.length > 0 && msgList.value[msgList.value.length - 1].is_final === true) {
      userScrolling.value = false
    }
  })
}

// 监听滚动事件
const handleScroll = () => {
  if (!scrollbarRef.value || !scrollbarRef.value.wrapRef) return

  const { scrollTop } = scrollbarRef.value.wrapRef
  // 当滚动到顶部时触发加载更多
  if (scrollTop === 0 && !loading.value && hasMore.value) {
    loadMore()
  }
}

// 加载更多历史记录
const loadMore = async () => {
  if (loading.value) return
  loading.value = true
  emit('loadMore')
}

// 监听消息列表变化
watch(
  () => props.list,
  (newList, oldList) => {
    loading.value = false
    // 判断是否还有更多数据 - 如果数据条数是20的倍数，说明可能还有更多数据
    hasMore.value = newList.length > 0 && newList.length % 20 === 0 && newList.length != oldList.length

    // 只在首次加载时滚动到底部
    if (isFirstLoad.value) {
      isFirstLoad.value = false
      nextTick(() => {
        safeScrollToBottom()
      })
    }
  }
)

onMounted(() => {
  listenClientAndManageEvent()
  listenCommonEvent()

  const erd = elementResizeDetectorMaker()
  const bodyDom = document.body

  erd.listenTo(bodyDom, element => {
    chatBoxHeight.value = element.clientHeight - 113
  })

  // 监听滚动事件 - 使用 nextTick 确保 DOM 已渲染
  nextTick(() => {
    addScrollListener()
  })
})

onUnmounted(() => {
  // 清理事件监听
  eventHub.off('client_msgContentChange')
  eventHub.off('data_history')
  eventHub.off('data_historyError')

  // 清理所有打字机实例
  typedInstances.forEach((typed, chatId) => {
    typed.destroy()
  })
  typedInstances.clear()
  typewriterElements.clear()

  // 移除滚动监听
  removeScrollListener()
})
</script>

<style lang="scss">
.client-chat::-webkit-scrollbar {
  display: none;
}
.robot-avatar,
.user-avatar {
  width: 32px;
  height: 32px;
  margin-top: 5px;
  border-radius: 32px;
  vertical-align: middle;
}
.robot-avatar {
  margin-right: 10px;
}
.user-avatar {
  margin-left: 10px;
}

.client-chat {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: overlay;
  padding: 0 12px;

  .loading {
    margin: 10px 0;
    width: 90px;
    &:after {
      content: '.';
      animation: ellipsis 1.5s steps(1, end) infinite;
    }
  }

  @keyframes ellipsis {
    0% {
      content: '.';
    }
    33% {
      content: '..';
    }
    66% {
      content: '...';
    }
    100% {
      content: '.';
    }
  }

  .qa-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 1, 10, 0.93);

    .timestamp {
      line-height: 16px;
      color: #8f959e;
      margin: 8px 0;
      padding-left: 42px;
    }

    .question-item {
      display: flex;
      align-items: center;
      width: fit-content;
      text-align: center;
      align-self: flex-end;
      padding-left: 44px;

      .qs-error {
        min-width: 16px;
        margin-right: 10px;
        color: #f75559;
      }
      .qs-loading {
        margin-right: 10px;
      }
      .question-text {
        background: linear-gradient(90deg, rgba(73, 91, 254, 1) 0%, rgba(46, 148, 255, 1) 100%); // #dbe8ff // var(--bubble-bg-myself-normal);
        border-radius: 6px;
        padding: 0 12px;
        text-align: left;
        word-break: break-all;
        word-wrap: break-word;
        color: #fff;
        p {
          margin: 10px 0;
        }

        code {
          white-space: break-spaces;
        }
        img {
          max-width: 80%;
        }
      }
    }

    .summary-item {
      align-self: center;
      margin: 12px 0;
    }

    .answer-item {
      display: flex;

      .contacter-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .answer-info {
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 6px 12px;
        background: #fff; // var(--bubble-bg-each-other-normal);
        border-radius: 6px;
        width: fit-content;
        box-shadow: var(--el-box-shadow-light);
        .answer-md {
          font:
            400 14px / 20px -apple-system,
            BlinkMacSystemFont,
            'Segoe UI',
            Roboto,
            'Helvetica Neue',
            Helvetica,
            Arial,
            'PingFang SC',
            'Microsoft YaHei',
            'Noto Sans',
            sans-serif;
          word-break: break-all;
          word-wrap: break-word;
          .table {
            /* 滚动条Track */
            ::-webkit-scrollbar-track {
              background: transparent;
            }

            /* Handle */
            ::-webkit-scrollbar-thumb {
              border-radius: 10px;
              background: rgba(17, 32, 70, 0.13);
            }
          }
          img {
            max-width: 100%;
            cursor: pointer;
          }
          p {
            word-break: break-all;
            word-wrap: break-word;
            margin: 10px 0;
            line-height: 1.6;
          }
          ol,
          ul {
            li {
              line-height: 1.6;
              margin: 7px 0;
            }
          }
          code {
            white-space: break-spaces;
          }
          table {
            // display: inline-block;
            // white-space: nowrap;
            // max-width: 100%;
            // overflow: scroll;
            // background: white;
            // border-bottom: 1px solid rgba(18, 42, 79, 0.08);
            // border-right: 1px solid rgba(18, 42, 79, 0.08);
            // border-spacing: 0;
            // border-collapse: collapse;
            display: inline-block;
            overflow-x: scroll;
            background: white;
            border-spacing: 0;
            border-collapse: collapse;
            border-bottom: 1px solid rgba(18, 42, 79, 0.08);
            border-right: 1px solid rgba(18, 42, 79, 0.08);
            max-width: 100%;
            th {
              background: #eaecef;
              color: rgba(1, 11, 50, 0.41);
              // padding: 12px;
              // font-weight: 400;
              // background: #eaecef;
            }
            td,
            th {
              border-left: 1px solid rgba(18, 42, 79, 0.08);
              border-top: 1px solid rgba(18, 42, 79, 0.08);
            }
            td {
              padding: 8px 12px;
              min-width: 20px;
            }
          }
          .table-style {
            display: inline-block;
            white-space: nowrap;
            max-width: 100%;
            overflow: scroll;
            background: white;
            border-bottom: 1px solid rgba(18, 42, 79, 0.08);
            border-right: 1px solid rgba(18, 42, 79, 0.08);
            border-spacing: 0;
            border-collapse: collapse;
            th {
              color: rgba(1, 11, 50, 0.41);
              padding: 12px;
              font-weight: 400;
              background: #eaecef;
            }
            td,
            th {
              border-left: 1px solid rgba(18, 42, 79, 0.08);
              border-top: 1px solid rgba(18, 42, 79, 0.08);
            }
            td {
              padding: 8px 4px;
              min-width: 45px;
              overflow-wrap: break-word;
              white-space: break-spaces;
            }
          }
        }
        .answer-expand {
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          width: 44px;
          height: 24px;
          margin-bottom: 12px;
          background: #fff;
          box-shadow: var(--shadow-small-light);
          border-radius: 16px;
          align-self: center;
        }
        .stop-ws {
          color: rgba(217, 226, 252, 0.51);
          margin-left: 5px;
        }
        .answer-source {
          margin: 12px 0;
          font-size: 14px;
          color: rgba(217, 226, 252, 0.51);
          text-align: left;

          .v-button {
            text-decoration: none;
            text-align: left;
          }
        }
      }
    }
  }
  .qa-item:last-child {
    padding-bottom: 120px;
  }

  .load-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;

    .el-icon {
      font-size: 20px;
      color: var(--el-color-primary);
    }
  }
}
.no-data {
  .introduce {
    margin: 30px 0 10px 0;
    font-weight: 500;
    font-size: 18px;
    color: rgba(153, 153, 153, 1);
  }
  .ai-introduce-box {
    background-image: url('@/assets/images/ai-introduce.png');
    background-position: 50%;
    background-repeat: no-repeat;
    box-shadow: 0 4px 10px 0 rgba(215, 0, 0, 0.04);
    background-size: 100% 100%;
    .introduce-info {
      padding: 22px 20px 12px;
      color: rgba(102, 102, 102, 1);
      font-size: 15px;
      line-height: 28px;
    }
  }
}

/* 打字机效果样式 */
.typewriter-container {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  margin: 10px 0;

  /* 打字机光标样式 */
  :deep(.typed-cursor) {
    opacity: 1;
    animation: typewriter-blink 0.7s infinite;
    color: #409eff;
    font-weight: bold;
  }

  /* 光标闪烁动画 */
  @keyframes typewriter-blink {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}

/* MaxKB 内置标签样式 */
:deep(.maxkb-tip) {
  padding: 16px;
  margin: 16px 0;
  border-left: 4px solid #409eff;
  background-color: #f0f9ff;
  border-radius: 4px;

  .maxkb-tip-title {
    font-weight: bold;
    color: #409eff;
    margin-bottom: 8px;
  }

  .maxkb-tip-content {
    color: #333;
    line-height: 1.6;
  }
}

:deep(.maxkb-warning) {
  padding: 16px;
  margin: 16px 0;
  border-left: 4px solid #e6a23c;
  background-color: #fdf6ec;
  border-radius: 4px;

  .maxkb-warning-title {
    font-weight: bold;
    color: #e6a23c;
    margin-bottom: 8px;
  }
}

:deep(.maxkb-danger) {
  padding: 16px;
  margin: 16px 0;
  border-left: 4px solid #f56c6c;
  background-color: #fef0f0;
  border-radius: 4px;

  .maxkb-danger-title {
    font-weight: bold;
    color: #f56c6c;
    margin-bottom: 8px;
  }
}

:deep(.maxkb-variable) {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  color: #e96900;
}

:deep(.maxkb-kb-ref) {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 3px;
  padding: 2px 6px;
  color: #1890ff;
  cursor: pointer;

  &:hover {
    background-color: #bae7ff;
  }
}

:deep(.maxkb-doc-ref) {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 3px;
  padding: 2px 6px;
  color: #52c41a;
  cursor: pointer;

  &:hover {
    background-color: #d9f7be;
  }
}

/* MaxKB 快速问题样式 */
:deep(.maxkb-quick-question) {
  display: inline-block;
  background-color: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 16px;
  padding: 6px 12px;
  margin: 4px 8px 4px 0;
  color: #409eff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #409eff;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
  }
}

:deep(.maxkb-quick-questions-container) {
  margin-top: 20px;
  padding: 16px;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e1e8ed;
}

:deep(.maxkb-quick-questions-title) {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;

  &::before {
    content: '💡';
    margin-right: 8px;
  }
}

:deep(.maxkb-quick-questions-list) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.maxkb-quick-question-item) {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 20px;
  padding: 8px 16px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;

  &:hover {
    background-color: #409eff;
    color: white;
    border-color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}
.problem-button {
  width: 100%;
  border: none;
  border-radius: 8px;
  background: #f5f6f7;
  padding: 12px;
  box-sizing: border-box;
  color: #1f2329;
  word-break: break-all;
  .icon {
    width: 16px;
    height: 16px;
    color: #409eff;
  }
  &:hover {
    background: rgb(235.9, 245.3, 255);
  }
}
</style>
