<template>
  <div class="problem-container h-full">
    <div class="flex items-center justify-between pb-20px px-20px">
      <h3 class="problem-title">历史记录</h3>
      <el-button type="primary" plain @click="newChat" size="small" icon="Plus">新建对话</el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-container">
      <el-input v-model="searchKeyword" placeholder="搜索历史..." prefix-icon="Search" clearable @input="handleSearch" @clear="clearSearch" class="search-input" />
    </div>

    <!-- 无搜索结果提示 -->
    <div v-if="displayProblems.length == 0" class="no-results">
      <el-empty description="暂无数据" :image-size="100" />
    </div>
    <!--面板 -->
    <div class="accordion-container w-full p-20px" v-else tabindex="0" @keydown="handleKeydown">
      <div
        v-for="(item, index) in displayProblems"
        :key="index"
        @click="selectProblem(item, index)"
        class="accordion-header flex items-center flex-wrap mb-10px"
        :class="{ active: selectedIndex === index }"
        :tabindex="0"
        @keydown.enter="selectProblem(item, index)"
      >
        <div class="problem-text flex-1 flex flex-wrap">{{ item.abstract }}</div>
        <!-- <div class="expand-icon mx-10px" @click.prevent.stop>
          <el-dropdown class="expand-icon-drop" trigger="click">
            <el-icon><MoreFilled /></el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item class="flex items-center" @click.stop="editProblem(item)">
                  <el-icon class="mr-5px"><EditPen /></el-icon>编辑</el-dropdown-item
                >
                <el-dropdown-item class="flex items-center" @click.stop="deleteProblem(item)">
                  <el-icon class="mr-5px"><Delete /></el-icon>删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="History">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ArrowDown, Search } from '@element-plus/icons-vue'

const props = defineProps({
  // 如果需要的话可以定义props
  list: {
    type: Array,
    default: () => []
  }
})
// 定义数据类型
interface ProblemItem {
  abstract: string
  application_id: string
  client_id: string
  id: string
}

interface Category {
  name: string
  key: string
  data: ProblemItem[]
}

// 定义 emit 事件
const emit = defineEmits<{
  selectProblem: [item: ProblemItem]
  newChat: ''
  editProblem: [item: ProblemItem]
  deleteProblem: [item: ProblemItem]
}>()

// 响应式数据
const categories = ref<Category[]>([])
const originalCategories = ref<Category[]>([]) // 保存原始数据
const searchKeyword = ref<string>('')
const searchResults = ref<Category[]>([])
const selectedIndex = ref<number>(-1) // 当前选中的项目索引

// 防抖定时器
let searchTimer: NodeJS.Timeout | null = null

// 计算显示的问题列表（搜索结果或原始分类）
const displayProblems = computed(() => {
  return searchKeyword.value ? searchResults.value : originalCategories.value
})

// 搜索处理（带防抖）
const handleSearch = (value: string) => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 如果搜索框为空，立即清空结果
  if (!value.trim()) {
    searchResults.value = []
    selectedIndex.value = -1 // 重置选中状态
    return
  }

  // 防抖处理，300ms后执行搜索
  searchTimer = setTimeout(() => {
    performSearch(value)
  }, 300)
}

// 执行搜索
const performSearch = (value: string) => {
  console.log('搜索关键词:', value)

  // 重置选中状态
  selectedIndex.value = -1

  const keyword = value.toLowerCase()
  const filteredCategories: Category[] = []

  // 遍历原始数据进行搜索，不修改原始数据
  originalCategories.value.forEach(item => {
    const matchedProblems: ProblemItem[] = []
    // 搜索问题标题和答案内容
    if (item.abstract.toLowerCase().includes(keyword)) {
      matchedProblems.push(item)
    }
    // 如果该分类下有匹配的问题，创建新的分类对象
    if (matchedProblems.length > 0) {
      filteredCategories.push({
        ...item
      })
    }
  })

  console.log('搜索结果:', filteredCategories)
  searchResults.value = filteredCategories
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  searchResults.value = []
  selectedIndex.value = -1 // 重置选中状态
}

// 选择问题
const selectProblem = (item: ProblemItem, index: number) => {
  console.log('选择的问题:', item.abstract)
  // 设置选中的索引
  selectedIndex.value = index
  // 向父组件传递选中的问题
  emit('selectProblem', item)
}

// 键盘导航
const handleKeydown = (event: KeyboardEvent) => {
  const currentList = displayProblems.value
  if (currentList.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = selectedIndex.value < currentList.length - 1 ? selectedIndex.value + 1 : 0
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = selectedIndex.value > 0 ? selectedIndex.value - 1 : currentList.length - 1
      break
    case 'Enter':
      event.preventDefault()
      if (selectedIndex.value >= 0 && selectedIndex.value < currentList.length) {
        const selectedItem = currentList[selectedIndex.value]
        emit('selectProblem', selectedItem)
      }
      break
    case 'Escape':
      selectedIndex.value = -1
      break
  }
}

//开启新对话
const newChat = () => {
  console.log('开启新对话')
  selectedIndex.value = -1 // 重置选中状态
  emit('newChat')
}

// 重置选中状态
const resetSelection = () => {
  selectedIndex.value = -1
}

// 加载问题数据
const loadProblems = async () => {
  try {
    const loadedData = props.list

    // 保存原始数据的深拷贝，避免被搜索逻辑修改
    originalCategories.value = JSON.parse(JSON.stringify(loadedData))
    categories.value = loadedData

    // 重置选中状态
    selectedIndex.value = -1

    console.log('加载的数据:', originalCategories.value)
  } catch (error) {
    console.error('加载问题数据失败:', error)
  }
}
//编辑标题
const editProblem = (item: ProblemItem) => {
  console.log('编辑的问题:', item.abstract)
  // 向父组件传递选中的问题
  emit('editProblem', item)
}
//删除标题
const deleteProblem = (item: ProblemItem) => {
  console.log('删除的问题:', item)

  // 向父组件传递选中的问题
  emit('deleteProblem', item)
}

// 编辑项目标题
const editItem = async (item: any, index: number) => {
  try {
    // 发送编辑事件
    emit('editProblem', item)
  } catch (error) {}
}

// 删除项目
const deleteItem = async (item: any, index: number) => {
  try {
    // 发送删除事件
    emit('deleteProblem', item)
  } catch (error) {}
}

// 暴露方法给父组件
defineExpose({
  resetSelection,
  editItem,
  deleteItem
})

// 组件挂载时加载数据
onMounted(() => {
  loadProblems()
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})
// 监听消息列表变化
watch(
  () => props.list,
  value => {
    loadProblems()
  }
)
// 组件卸载时清理定时器和事件监听
onUnmounted(() => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped lang="scss">
.problem-container {
  padding-bottom: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.problem-title {
  font-size: 18px;
  font-weight: 600;
  background: linear-gradient(90deg, rgba(0, 186, 173, 1) 0%, rgba(5, 122, 255, 1) 100%);
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}

// 搜索栏样式
.search-container {
  padding: 0 20px;

  .search-input {
    :deep(.el-input__wrapper) {
      border-radius: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      border: 1px solid #e0e0e0;

      &:hover {
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
      }

      &.is-focus {
        box-shadow: 0 2px 12px rgba(23, 162, 184, 0.3);
        border-color: #17a2b8;
      }
    }
  }
}

.accordion-container {
  outline: none;

  &:focus {
    outline: none;
  }
  .accordion-header {
    padding: 0 10px;
    margin-bottom: 10px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid;
    box-sizing: border-box;
    border-image: linear-gradient(to right, rgba(59, 102, 247, 1) 0%, rgba(0, 186, 173, 1) 100%) 1;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    .expand-icon {
      display: none;
      color: #17a2b8;
      cursor: pointer;
      transform: rotate(90deg);
      transition: all 0.2s ease;

      // 确保下拉菜单图标始终可见
      .el-icon {
        transition: all 0.2s ease;
      }
    }

    // 悬停效果
    &:hover:not(.active) {
      background: rgba(240, 248, 255, 0.8);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 102, 247, 0.15);
    }

    // 鼠标经过整个项目时显示图标
    &:hover {
      .expand-icon {
        display: inline-block;
      }
    }

    // 确保鼠标经过下拉菜单区域时图标保持显示
    &:hover .expand-icon,
    .expand-icon:hover,
    .expand-icon-drop:hover .expand-icon {
      display: inline-block !important;
    }

    // 当下拉菜单打开时保持图标显示
    .expand-icon-drop.is-active .expand-icon {
      display: inline-block !important;
    }
    // 焦点样式（键盘导航）
    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(59, 102, 247, 0.4);
    }

    // 选中状态的高亮效果
    &.active {
      border: 2px solid rgba(59, 102, 247, 0.6);
      box-shadow:
        0 4px 20px rgba(59, 102, 247, 0.2),
        0 2px 8px rgba(0, 186, 173, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);

      position: relative;

      .problem-text {
        font-weight: 700;
        // 增强选中状态下的文字渐变效果
        background: linear-gradient(to right, rgba(0, 186, 173, 1) 0%, rgba(5, 122, 255, 1) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }
  }
  .problem-text {
    height: 48px;
    line-height: 48px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(to right, rgba(0, 186, 173, 1) 0%, rgba(5, 122, 255, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    cursor: pointer;

    /* 确保渐变效果在所有浏览器中都能正常显示 */
    display: inline-block;

    /* 为不支持背景裁剪的浏览器提供备用颜色 */
    @supports not (-webkit-background-clip: text) {
      color: #17a2b8;
      background: none;
    }
  }
}

// 无结果提示
.no-results {
  padding: 20px;
  text-align: center;
}
</style>
