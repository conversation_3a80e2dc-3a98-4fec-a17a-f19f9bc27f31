<template>
  <div class="chat-box">
    <!-- 智能链接 -->
    <div class="chat-link">
      <div class="link-box">
        <div class="link-top">
          <div class="imgs flex flex-nowrap">
            <img src="@/assets/images/logo.gif" alt="" class="link-logo" />
            <div class="introduce">
              <div class="mt-5px">HI，我是安徽交易集团智能客服小一 一为您提供公共资源交易服务</div>
            </div>
          </div>
        </div>
        <div class="shortcut-menu">
          <div class="menu-title">联系方式</div>
          <div class="menu-content">
            <el-scrollbar height="100%" class="px-20px">
              <div class="menu-item" v-for="item in menuList" :key="item.id">
                <a :href="item.url" target="_blank">
                  <div class="flex items-center">
                    <img src="@/assets/images/position.png" alt="" class="w-36px" />
                    <span>{{ item.name }}</span>
                  </div>
                </a>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
    <!-- 智能客服问答 -->
    <div class="chat-content">
      <!-- 聊天框 -->
      <div class="chat-wrap">
        <div class="chat-wrap__main">
          <div class="chat-wrap__main-content" :style="{ paddingBottom: `${chatMainMrgBottom}px` }">
            <el-scrollbar ref="scrollbarRef" class="w-full px-10px" height="100%" always>
              <ClientChat :list="chatList" @send="onSendQuestion" @loadMore="loadMoreHistory" @typingComplete="onTypingComplete" @quickQuestion="onQuickQuestion" />
            </el-scrollbar>
          </div>
          <div class="chat-wrap__main-footer">
            <QuestionInput @send="onSendQuestion" :is-typing="isTyping" />
          </div>
        </div>
      </div>
    </div>
    <!-- 常见问题与历史记录 -->
    <div class="chat-history">
      <el-scrollbar ref="scrollbarListRef" class="w-full" height="calc(100vh - 40px)" always>
        <!-- 常见问题 -->
        <Problems @selectProblem="onSelectProblem" />
        <!-- 历史记录 -->
        <History @selectProblem="onSelectHistory" :list="historyList" @newChat="createNewChat" @editProblem="onEditProblem" @deleteProblem="onDeleteProblem" />
      </el-scrollbar>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" title="编辑标题" width="42%" align-center>
    <el-input v-model="historyTitle" class="w-full" :rows="6" type="textarea" placeholder="请输入" maxlength="100" minlength="1" show-word-limit />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveHistoryTitle"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="AICustomerService">
import { ref, provide, onMounted } from 'vue'
import ClientChat from './components/client-chat.vue'
import QuestionInput from './components/question-input.vue'
import Problems from './components/problem.vue'
import History from './components/history.vue'
import { getToken, getAppId } from '@/api/maxkb'
import { getHistory, getNewHistoryId, getChatList, editHistoryTitle } from '@/api/chat'
import { getAccessToken, setToken } from '@/utils/auth'
import { sseCls, parseToTimestamp, formatTime } from '@/utils/sse'
import { MESSAGE_TYPE, ACCESS_TYPE } from '@/constants'
import { fa, fi, lo } from 'element-plus/es/locales.mjs'
import { update } from 'lodash-es'
import { v4 as uuidv4 } from 'uuid'
// 创建 scrollbar 引用
const scrollbarRef = ref()
// 注入依赖
const eventHub = inject('eventHub')
// 提供给子组件
provide('scrollbarRef', scrollbarRef)
//
const appId = ref('') //应用id
const dialogVisible = ref(false)
const historyTitle = ref('')
const historyId = ref(null)
const menuList = ref([
  {
    id: 1,
    name: '安徽公共资源交易集团',
    url: 'https://www.ahggzyjt.com/'
  },
  {
    id: 2,
    name: '安徽公共资源交易集团项目管理有限公司',
    url: 'https://www.ahggzyjt.com/'
  },
  {
    id: 3,
    name: '安徽公共资源交易集团科技有限公司',
    url: 'https://www.huiyicai.cn/'
  },
  {
    id: 4,
    name: '合肥市产权交易中心',
    url: 'https://www.haee.com.cn/'
  },
  {
    id: 5,
    name: '安徽省农村综合产权交易所',
    url: 'https://www.ahaee.com/'
  },
  {
    id: 6,
    name: '安徽联合技术产权交易所',
    url: 'https://www.ahtre.com/'
  }
])
const addNewChatStatus = ref(false)
// 其他必要的状态和方法
const chatList = ref([]) //会话记录
const chatMainMrgBottom = ref(0)

const historyList = ref([]) //历史记录
const isWelcome = ref(true)
const isAvailable = ref(true)
const isTyping = ref(false) // 标记是否正在进行打字机效果
// 发送问题的处理函数
const onSendQuestion = async (question: string) => {
  // 处理发送问题的逻辑
  console.log('发送问题:', question)
  // 重置打字状态，确保用户手动发送问题时不会被锁定
  isTyping.value = false
  await sseCls.triggerSendMsg(question)
  // 等待DOM更新后滚动到底部
  nextTick(() => {
    if (addNewChatStatus.value) {
      getHistoryList()
    }
    scrollbarRef.value?.setScrollTop(scrollbarRef.value.wrapRef.scrollHeight)
  })
}

// 加载更多历史记录的处理函数
const loadMoreHistory = () => {
  // 处理加载更多历史记录的逻辑
  console.log('加载更多历史记录')
}
//获取token
const getChatToken = async () => {
  try {
    //
    console.log('获取token', getAccessToken())
    if (getAccessToken()) return
    const data = await getToken()
    console.log(data)
    setToken(data)
  } catch (error: any) {
    console.error('获取token失败:', error)
  } finally {
    getHistoryList()
    getNewChatId()
  }
}
const initSocket = () => {
  // 初始化数据
  sseCls.init()
}
const getHistoryList = async () => {
  try {
    const res = await getHistory(appId.value)
    console.log(res)

    historyList.value = res.data.records
  } catch (error: any) {
    console.error('获取失败:', error)
  } finally {
  }
}
// 格式化日期为 ISO 8601 格式，带东八区时区信息和微秒
const formatToISO8601WithTimezone = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  const milliseconds = now.getMilliseconds().toString().padStart(3, '0')
  // 生成随机的微秒部分（3位数字）以匹配目标格式
  const microseconds = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, '0')

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}${microseconds}+08:00`
}

//选择问题
const onSelectProblem = (problem: any) => {
  console.log('选择的问题:', problem)

  // 设置打字机状态为正在进行，禁用输入框
  isTyping.value = true

  const formattedTime = formatToISO8601WithTimezone()
  console.log('生成的时间格式:', formattedTime)

  // 演示时间戳转换功能
  const timestamp = parseToTimestamp(formattedTime)
  console.log('转换为时间戳:', timestamp)
  console.log('格式化显示时间:', formatTime(formattedTime))

  const data = {
    id: uuidv4(),
    chat_id: localStorage.getItem('appSessionId'),
    problem_text: problem.problem,
    answer_text: problem.answer,
    update_time: formattedTime,
    timestamp: timestamp, // 添加时间戳字段
    isTyped: true,
    is_loading: false,
    is_history: true
  }

  console.log('【index】准备发送历史消息:', data)
  sseCls.assembleMsgContent(data, MESSAGE_TYPE.HISTORY)

  // 设置安全超时，防止打字状态一直被锁定
  setTimeout(() => {
    if (isTyping.value) {
      console.log('【index】打字机效果超时，自动重置状态')
      isTyping.value = false
    }
  }, 10000) // 10秒超时
  nextTick(() => {
    scrollToBottom()
  })
  // 延迟一点时间后检查消息是否正确添加
  setTimeout(() => {
    console.log('【index】检查SSE缓存中的消息:', sseCls.cache?.chatsContent)
  }, 100)
}
//选择历史记录
const onSelectHistory = (data: any) => {
  console.log('选择的历史记录:', data)
  addNewChatStatus.value = false
  localStorage.setItem('appSessionId', data.id)
  getList(data.id, false)
}
//获取新对话的id
const getNewChatId = async () => {
  try {
    const data = await getNewHistoryId(appId.value)
    console.log(data)
    localStorage.setItem('appSessionId', data.data)
  } catch (error: any) {
    console.error('获取失败:', error)
  } finally {
    addNewChatStatus.value = true
    initSocket()
  }
}
// 打字机效果完成回调
const onTypingComplete = (chatId: string) => {
  console.log('【index】打字机效果完成:', chatId)
  // 重置打字状态，允许用户输入
  isTyping.value = false
}

// 处理快速问题点击
const onQuickQuestion = async (question: string) => {
  console.log('【index】快速问题被点击:', question)

  try {
    // 重置打字状态
    isTyping.value = false

    // 发送问题
    await sseCls.triggerSendMsg(question)

    // 滚动到底部
    nextTick(() => {
      if (addNewChatStatus.value) {
        getHistoryList()
      }
      scrollbarRef.value?.setScrollTop(scrollbarRef.value.wrapRef.scrollHeight)
    })
  } catch (error) {
    console.error('【index】发送快速问题失败:', error)
  }
}

// 调试方法：检查打字状态
const debugTypingState = () => {
  console.log('【index】当前打字状态:', isTyping.value)
  return isTyping.value
}

// 测试 MaxKB 标签的方法
const testMaxKBTags = () => {
  const testContent = `
# MaxKB 标签测试

这是一个测试 MaxKB 内置标签的示例：

## 快速问题标签
<quick_question>什么是人工智能？</quick_question>
<quick_question>如何学习机器学习？</quick_question>
<quick_question>深度学习的应用场景有哪些？</quick_question>

## 变量标签
当前用户：{{user_name}}
系统时间：{{current_time}}

## 知识库引用
相关知识库：{{kb:ai_knowledge_base}}
参考文档：{{doc:ml_tutorial_001}}

## 提示框标签
::: tip 提示
这是一个有用的提示信息。
:::

::: warning 警告
请注意这个重要信息。
:::

::: danger 危险
这是一个危险警告！
:::

感谢使用 MaxKB！
  `

  // 模拟添加一条包含 MaxKB 标签的消息
  const testMessage = {
    id: 'test-maxkb-' + Date.now(),
    chat_id: 'test-maxkb-' + Date.now(),
    problem_text: '测试 MaxKB 标签',
    answer_text: testContent,
    create_time: new Date().toISOString(),
    isTyped: false // 不使用打字机效果，直接显示
  }

  console.log('【index】测试 MaxKB 标签:', testMessage)

  // 添加到聊天列表
  sseCls.assembleMsgContent(testMessage, 'HISTORY')

  // 滚动到底部
  nextTick(() => {
    scrollbarRef.value?.setScrollTop(scrollbarRef.value.wrapRef.scrollHeight)
  })

  return testMessage
}

// 监听用户端事件
const listenClientEvent = () => {
  // 监听配置信息，获取机器人和问答库可用状态
  eventHub.on('client_configChange', res => {
    isAvailable.value = res.is_available
  })

  // 监听历史记录拉取成功事件
  eventHub.on('client_msgContentChange', res => {
    const { chatsContent, type } = res
    console.log(chatsContent)

    if (type === MESSAGE_TYPE.ANSWER && chatsContent.length > 0) {
      isWelcome.value = false
      // 答案消息时滚动到底部
      nextTick(() => {
        scrollbarRef.value?.setScrollTop(scrollbarRef.value.wrapRef.scrollHeight)
      })
    }
    if (type === MESSAGE_TYPE.HISTORY && chatsContent.length > 0) {
      isWelcome.value = false
    }
  })
}
//获取历史记录的对话记录
const getList = async (id: string, isLoadMore = false) => {
  try {
    const res = await getChatList(appId.value, id, false)
    console.log(res)

    if (isLoadMore) {
      chatList.value = [...res.data.records, ...chatList.value]
      sseCls.assembleMsgContent(res.data.records, MESSAGE_TYPE.HISTORY)
    } else {
      if (sseCls.cache) {
        sseCls.cache.chatsContent = []
        sseCls.assembleMsgContent([], MESSAGE_TYPE.HISTORY)
      }
      chatList.value = res.data.records
      sseCls.assembleMsgContent(res.data.records, MESSAGE_TYPE.HISTORY)
      nextTick(() => {
        scrollToBottom()
      })
    }
  } catch (error: any) {
    console.error('获取失败:', error)
  } finally {
  }
}
// 滚动到底部
const scrollToBottom = () => {
  scrollbarRef.value?.setScrollTop(scrollbarRef.value.wrapRef.scrollHeight)
}
//获取appid
const getChatAppId = async () => {
  try {
    const res = await getAppId()
    console.log(res)
    appId.value = res
  } catch (error: any) {}
}
onMounted(async () => {
  await Promise.all([getChatAppId()])

  getChatToken()
  listenClientEvent()
  // 暴露调试方法到全局
  if (typeof window !== 'undefined') {
    ;(window as any).debugTypingState = debugTypingState
    ;(window as any).testMaxKBTags = testMaxKBTags
    console.log('调试方法已暴露: window.debugTypingState() 和 window.testMaxKBTags()')
  }
})
//创建新对话
const createNewChat = () => {
  // 重置打字状态
  isTyping.value = false
  getNewChatId()
  // 清空聊天内容并重置历史
  sseCls.cache.chatsContent = []
  sseCls.assembleMsgContent([], MESSAGE_TYPE.HISTORY)
}
//编辑历史数据
const onEditProblem = (data: any) => {
  console.log('编辑的问题:', data)
  dialogVisible.value = true
  historyTitle.value = data.abstract
  historyId.value = data.id
}
//保存历史记录修改标题
const saveHistoryTitle = async () => {
  try {
    const data = await editHistoryTitle(appId.value, historyId.value, historyTitle.value)
    console.log(data)
  } finally {
    dialogVisible.value = false
  }
}
//删除历史数据
const onDeleteProblem = (data: any) => {
  console.log('删除的问题:', data)
}
// 组件卸载时清理事件监听
onUnmounted(() => {
  eventHub.off('client_configChange')
  eventHub.off('client_msgContentChange')
})
</script>

<style scoped lang="scss">
.chat-box {
  width: 100%;
  height: 100vh;
  display: flex;
  position: relative;
  background: url('@/assets/images/bg.png') no-repeat;
  background-size: 100% 100%;
  background-position: 50%;
  box-sizing: border-box;
  .chat-content {
    flex: 1;
    height: 100%;
    padding: 30px 40px 0;
    box-sizing: border-box;
  }
  .chat-history {
    flex-shrink: 0;
    width: 300px;
    height: 100%;
    // border-radius: 24px 0 0 24px;
    // border: 2px solid #fff;
    padding: 20px 0;
    background: rgba(255, 255, 255, 0.25);
    box-shadow: -12px 0px 6px rgba(146, 221, 252, 0.1);
  }
}
.chat-link {
  height: 100%;
  padding: 30px 0 30px 30px;
  .link-box {
    height: 100%;
    flex-shrink: 0;
    width: 380px;
    background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.3), hsla(0, 0%, 100%, 0.5));
    box-shadow: 0 0 10px 0 rgba(215, 0, 0, 0.1);
    border-radius: 16px 16px 16px 16px;
    border: 2px solid hsla(0, 0%, 100%, 0.8);
    padding: 20px 0;
    .link-top {
      padding: 0 20px;
    }
    .menu-content {
      height: calc(100vh - 410px);
    }
  }
  .link-logo {
    width: 130px;
  }
  .introduce {
    flex-shrink: 0;
    width: 210px;
    height: 142px;
    background: url('@/assets/images/tip-bg.png') no-repeat;
    background-size: 200px auto;
    background-position: 50%;
    color: rgba(5, 86, 222, 1);
    padding: 0 20px;
    display: flex;
    flex-wrap: wrap;
    align-content: center;
    font-size: 12px;
  }
  .menu-title {
    margin-top: 30px;
    padding: 20px;
    font-size: 26px;
    background: linear-gradient(90deg, rgba(42, 130, 228, 1), rgba(9, 205, 213, 1));
    -webkit-background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
  }
  .menu-item {
    margin-bottom: 20px;
    padding: 16px 20px;
    background: url('@/assets/images/menu-bg.png') no-repeat center;
    background-size: 100% 100%;
    font-size: 16px;
    color: rgba(1, 42, 246, 0.45);
  }
}
.chat-wrap {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  &__main {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    min-height: 400px;
    overflow: hidden;

    &-content {
      height: calc(100vh - 160px);
      display: flex;
      justify-content: flex-end;
      align-items: flex-end;
    }
    &-footer {
      position: relative;
      z-index: 3;
    }
  }
}
</style>
