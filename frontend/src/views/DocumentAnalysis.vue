<template>
  <div class="document-analysis" :class="{ fullscreen: uiStore.layout.fullscreen }">
    <!-- 全局加载遮罩 -->
    <el-loading v-loading="uiStore.isAnyLoading" :text="uiStore.currentLoadingMessage" element-loading-background="rgba(0, 0, 0, 0.8)" class="global-loading" />

    <!-- 工作流程进度条 -->

    <el-container>
      <!-- Header -->
      <el-header class="header">
        <div class="header-content">
          <div class="header-left">
            <h1 class="title">
              <!-- <el-icon><Document /></el-icon> -->
              安徽交易集团文档智能分析系统
            </h1>
            <el-tag v-if="documentStore.currentDocument" type="success" size="small">
              {{ documentStore.currentDocument.originalName }}
            </el-tag>
          </div>

          <div class="header-center">
            <!-- 文档统计信息 -->
            <div v-if="documentStore.currentDocument" class="document-stats">
              <span class="doc-name">{{ documentStore.currentDocument.originalName }}</span>
              <div class="stats-tags">
                <el-tag size="small" type="info">本文网页数 {{ totalPages }}页</el-tag>
                <el-tag size="small" type="success">分析完成数 {{ analysisStore.totalCount }}条</el-tag>
                <el-tag size="small" type="warning">高亮显示数 {{ analysisStore.extractCount }}处</el-tag>
                <el-tag size="small" type="danger">关键词检索 {{ analysisStore.detectCount }}项</el-tag>
              </div>
            </div>
          </div>

          <div class="header-actions">
            <el-button-group>
              <el-button @click="uiStore.toggleNavigator()" size="small">
                {{ uiStore.layout.showNavigator ? '隐藏导航' : '显示导航' }}
              </el-button>
              <el-button @click="uiStore.toggleFullscreen()" size="small">
                {{ uiStore.layout.fullscreen ? '退出全屏' : '全屏' }}
              </el-button>
            </el-button-group>

            <el-button type="primary" :icon="Upload" @click="showUploadDialog = true" :disabled="uiStore.isAnyLoading"> 上传文档 </el-button>
          </div>
        </div>
      </el-header>

      <el-container class="main-layout">
        <!-- 左侧分析结果面板 -->
        <el-aside width="300px" class="left-panel">
          <div class="panel-header">
            <div class="panel-title">
              <img src="@/assets/images/robot.png" alt="" class="w-24px" />
              <span class="font-bold">AI检测结果</span>
              <el-tag v-if="uiStore.isAnyLoading && documentParsed" size="small" type="info" class="status-tag">
                <el-icon class="is-loading"><Loading /></el-icon>
                分析中...
              </el-tag>
              <el-tag v-else-if="!hasAnalysisResults && documentParsed" size="small" type="warning" class="status-tag"> 待分析 </el-tag>
              <el-tag v-else-if="hasAnalysisResults" size="small" type="success" class="status-tag"> 已完成 </el-tag>
            </div>
            <div class="panel-actions">
              <el-button size="small" :icon="Search" @click="triggerAnalysis" :loading="uiStore.isAnyLoading" title="开始分析" circle />
              <el-button size="small" :icon="Refresh" @click="refreshResults" circle title="刷新结果" />
            </div>
          </div>

          <div class="analysis-results-container">
            <!-- 结构化分析结果组件 -->
            <StructuredAnalysisResults
              v-if="useStructuredResults"
              ref="analysisResultsRef"
              :document-id="documentStore.currentDocument?.id"
              :auto-load="documentParsed"
              @item-click="handleAnalysisItemClick"
              @locate-request="handleLocateRequest"
              @item-select="handleAnalysisItemSelect"
              @source-click="handleSourceClick"
            />

            <!-- 传统分析结果组件 -->
            <AnalysisResults
              v-else
              ref="analysisResultsRef"
              :document-id="documentStore.currentDocument?.id"
              :auto-load="documentParsed"
              @item-click="handleAnalysisItemClick"
              @locate-request="handleLocateRequest"
              @item-select="handleAnalysisItemSelect"
            />
          </div>
        </el-aside>

        <!-- 中间文档展示区域 -->
        <el-main class="center-panel">
          <!-- 文档工具栏 -->
          <div class="document-toolbar">
            <div class="toolbar-left">
              <el-radio-group v-model="previewType" size="small">
                <el-radio-button label="PDF" value="pdf" />
                <el-radio-button label="HTML" value="html" />
              </el-radio-group>
              <el-button-group size="small">
                <el-button :icon="ZoomOut" @click="zoomOut">缩小</el-button>
                <el-button @click="resetZoom">{{ Math.round(zoomLevel * 100) }}%</el-button>
                <el-button :icon="ZoomIn" @click="zoomIn">放大</el-button>
              </el-button-group>
              <el-button size="small" :icon="FullScreen" @click="fitToWidth">适应窗口</el-button>
            </div>

            <div class="toolbar-center">
              <span v-if="documentStore.currentDocument" class="document-title">
                {{ documentStore.currentDocument.originalName }}
              </span>
            </div>

            <div class="toolbar-right">
              <el-input v-model="searchKeyword" placeholder="搜索文档内容..." size="small" style="width: 200px" :prefix-icon="Search" clearable />
              <el-button size="small" :icon="Download">下载</el-button>
            </div>
          </div>

          <!-- 文档内容区域 -->
          <div class="document-content-area">
            <div v-if="!documentStore.hasDocument" class="empty-state">
              <el-empty description="请上传DOCX文档开始分析" :image-size="200">
                <el-button type="primary" @click="showUploadDialog = true">上传文档</el-button>
              </el-empty>
            </div>

            <div v-else-if="!documentParsed" class="document-info">
              <div class="info-card">
                <h3>{{ documentStore.currentDocument?.originalName }}</h3>
                <p>文件大小: {{ formatFileSize(documentStore.currentDocument?.fileSize) }}</p>
                <p>上传时间: {{ formatDateTime(documentStore.currentDocument?.uploadTime) }}</p>
                <p>状态: {{ getStatusText(documentStore.currentDocument?.status) }}</p>

                <div class="action-buttons">
                  <el-button type="primary" @click="parseDocument" :loading="parsing"> 解析文档 </el-button>
                </div>
              </div>
            </div>
            <!-- 文档已解析，pdf直接显示文档内容 -->
            <DocumentPdfViewer v-else-if="documentParsed && previewType === 'pdf'" ref="documentPdfViewerRef" :document-id="documentStore.currentDocument?.id" :auto-load="true" />
            <!-- 文档已解析，html直接显示文档内容 -->
            <DocumentViewer
              v-else-if="documentParsed && previewType === 'html'"
              ref="documentViewerRef"
              :document-id="documentStore.currentDocument?.id"
              :auto-load="true"
              @content-click="handleContentClick"
              @marker-click="handleMarkerClick"
              @section-change="handleSectionChange"
            />

            <!-- 备用：手动分析提示（如果需要的话） -->
            <div v-else-if="documentParsed && !hasAnalysisResults && !uiStore.isAnyLoading" class="analysis-prompt" style="display: none">
              <div class="prompt-card">
                <el-icon class="prompt-icon" size="48"><DocumentCopy /></el-icon>
                <h3>文档解析完成</h3>
                <p>文档已成功解析，现在可以开始AI智能分析</p>
                <div class="prompt-actions">
                  <el-button type="primary" size="large" :icon="Search" @click="triggerAnalysis" :loading="uiStore.isAnyLoading"> 开始AI分析 </el-button>
                  <el-button size="large" @click="skipAnalysis"> 跳过分析，直接查看文档 </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-main>

        <!-- 右侧MaxKB聊天机器人 -->
        <el-aside width="350px" class="right-panel">
          <div class="chat-panel">
            <div class="chat-header">
              <div class="chat-title">
                <img src="@/assets/images/robot.png" alt="" class="w-24px" />
                <span class="font-bold">AI智能助手</span>
              </div>
              <div class="chat-status">
                <el-tag size="small" type="success">在线</el-tag>
              </div>
            </div>
            <div class="chat-container">
              <iframe src="https://dify.1997303.xyz/ui/chat/a69936d9f7978fff" style="width: 100%; height: 100%; border: none" frameborder="0" allow="microphone" title="MaxKB智能助手"></iframe>
            </div>
          </div>
        </el-aside>
      </el-container>
      <!-- Footer -->
      <el-footer height="60px" class="footer">
        <div class="footer-content">
          <span>状态: 就绪</span>
        </div>
      </el-footer>
    </el-container>

    <!-- 文档上传对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传文档" width="600px" :close-on-click-modal="false" align-center>
      <DocumentUpload @upload-success="handleUploadSuccess" @upload-error="handleUploadError" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { Document, Upload, List, Avatar, Refresh, ZoomOut, ZoomIn, FullScreen, Search, Download, Loading, DocumentCopy } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import DocumentUpload from '@/components/DocumentUpload.vue'
import DocumentViewer from '@/components/DocumentViewer.vue' //文档转html分析预览
import DocumentPdfViewer from '@/components/DocumentPdfViewer.vue' //文档转pdf分析预览
import DocumentNavigator from '@/components/DocumentNavigator.vue'
import AnalysisResults from '@/components/AnalysisResults.vue'
import StructuredAnalysisResults from '@/components/StructuredAnalysisResults.vue'
import { useDocumentStore } from '@/stores/document'
import { useAnalysisStore } from '@/stores/analysis'
import { useUIStore } from '@/stores/ui'
import { parseDocument as parseDocumentAPI } from '@/api/document'
import { handleError, handleApiError } from '@/utils/errorHandler'



const activeTab = ref('extract')
const showUploadDialog = ref(false)
const parsing = ref(false)
const documentContent = ref('')
const currentPage = ref(1)
const totalPages = ref(1)
const documentViewerRef = ref()
const analysisResultsRef = ref()
const zoomLevel = ref(1)
const searchKeyword = ref('')
const useStructuredResults = ref(true) // 使用新的结构化组件
const documentStore = useDocumentStore()
const analysisStore = useAnalysisStore()
const uiStore = useUIStore()
//文档分析预览模式
const previewType = ref('pdf')

// Computed
const documentParsed = computed(() => {
  return documentStore.currentDocument?.status === 'COMPLETED'
})

const hasAnalysisResults = computed(() => {
  return analysisStore.totalCount > 0
})

// 处理上传成功
const handleUploadSuccess = async (data: any) => {
  console.log('文档上传成功:', data)
  ElMessage.success('文档上传成功!')
  showUploadDialog.value = false
  uiStore.setWorkflowStep('parse')
  uiStore.showNotification('success', '上传成功', '文档已成功上传，正在自动解析...')

  // 自动触发文档解析
  await nextTick()
  console.log('当前文档状态:', documentStore.currentDocument)
  if (documentStore.currentDocument?.id) {
    console.log('开始自动解析文档...')
    await parseDocument()
  } else {
    console.warn('文档ID不存在，无法自动解析')
  }
}

// 处理上传失败
const handleUploadError = (error: Error) => {
  handleError(error, 'upload')
  uiStore.setUploadLoading(false)
}

// 解析文档
const parseDocument = async () => {
  if (!documentStore.currentDocument?.id) {
    console.warn('文档ID不存在，无法解析')
    return
  }

  console.log('开始解析文档:', documentStore.currentDocument.id)
  parsing.value = true
  uiStore.setParseLoading(true, '正在解析文档...')

  try {
    const response = await parseDocumentAPI(documentStore.currentDocument.id)
    console.log('文档解析响应:', response)

    if (response.success) {
      ElMessage.success('文档解析成功!')
      documentStore.updateDocumentStatus('COMPLETED')
      uiStore.setWorkflowStep('analyze')
      uiStore.showNotification('success', '解析完成', '文档解析成功，正在启动AI分析...')

      console.log('文档解析完成，开始自动触发AI分析...')
      // 自动触发AI分析
      await nextTick()
      await triggerAnalysis()
    } else {
      console.error('文档解析失败:', response.message)
      handleApiError(response.message || '文档解析失败', 'parse')
    }
  } catch (error: any) {
    console.error('文档解析异常:', error)
    handleApiError(error, 'parse')
  } finally {
    parsing.value = false
    uiStore.setParseLoading(false)
  }
}

// 文档查看器事件处理
const handleContentClick = (event: MouseEvent, element: HTMLElement) => {
  console.log('Content clicked:', element)
}

const handleMarkerClick = (marker: any) => {
  console.log('Marker clicked:', marker)
}

const handleSectionChange = (section: number) => {
  currentPage.value = section
}

// 导航事件处理
const handleGoToElement = (elementId: string) => {
  if (documentViewerRef.value) {
    documentViewerRef.value.scrollToSection(elementId)
  }
}

const handleGoToPage = (page: number) => {
  currentPage.value = page
}

const handleSearchHighlight = (text: string, index: number) => {
  console.log('Search highlight:', text, index)
}

const handleBookmarkAdded = (bookmark: any) => {
  ElMessage.success('书签添加成功')
}

// 分析结果事件处理
const handleAnalysisItemClick = (item: any) => {
  console.log('Analysis item clicked:', item)
  analysisStore.setSelectedItem(item)
}

const handleLocateRequest = (item: any) => {
  console.log('Locate request:', item)
  if (item.position && documentViewerRef.value) {
    // 使用新的高亮功能
    documentViewerRef.value.highlightResultItem(item)
  }
}

const handleAnalysisItemSelect = (item: any) => {
  analysisStore.setSelectedItem(item)
}

// 处理源引用点击事件
const handleSourceClick = (source: any, item: any) => {
  console.log('Source click received:', source, item)

  if (!documentViewerRef.value) {
    console.warn('DocumentViewer 组件未准备就绪')
    ElMessage.warning('文档查看器未准备就绪，请稍后再试')
    return
  }

  // 生成唯一的高亮ID
  const highlightId = `source-${item.id}-${source.referenceIndex}`

  // 调用文档查看器的定位和高亮方法
  const success = documentViewerRef.value.locateAndHighlightText(source.content, highlightId)

  if (success) {
    ElMessage.success(`已定位到 ${source.referenceIndex}`)
  } else {
    ElMessage.warning(`未能在文档中找到对应内容`)
  }
}

// 监听全局高亮事件
const handleGlobalHighlightEvent = (event: CustomEvent) => {
  const { item } = event.detail
  if (documentViewerRef.value) {
    documentViewerRef.value.highlightResultItem(item)
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('highlight-result-item', handleGlobalHighlightEvent)
})

onUnmounted(() => {
  window.removeEventListener('highlight-result-item', handleGlobalHighlightEvent)
  window.removeEventListener('resize', uiStore.updateScreenSize)
})

// 工作流程相关方法
const getWorkflowStepText = () => {
  const stepTexts = {
    upload: '上传文档',
    parse: '解析文档',
    analyze: '智能分析',
    display: '展示结果',
    interact: '交互操作'
  }
  return stepTexts[uiStore.workflowStep] || ''
}

const getActiveStep = () => {
  const steps = ['upload', 'parse', 'analyze', 'display', 'interact']
  return steps.indexOf(uiStore.workflowStep)
}

// 文档工具栏方法
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 3)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const fitToWidth = () => {
  zoomLevel.value = 1
  // 这里可以添加适应窗口宽度的逻辑
}

const refreshResults = () => {
  if (analysisResultsRef.value) {
    analysisResultsRef.value.refreshResults()
  }
}

// 手动触发分析
const triggerAnalysis = async () => {
  if (!documentStore.currentDocument?.id) {
    ElMessage.warning('请先上传并解析文档')
    return
  }

  if (!documentParsed.value) {
    ElMessage.warning('请等待文档解析完成后再进行分析')
    return
  }

  try {
    if (analysisResultsRef.value) {
      await analysisResultsRef.value.manualTriggerAnalysis()
    }
  } catch (error) {
    console.error('触发分析失败:', error)
    ElMessage.error('触发分析失败')
  }
}

// 跳过分析，直接查看文档
const skipAnalysis = () => {
  ElMessage.info('已跳过AI分析，您可以随时点击左侧的分析按钮开始分析')
  // 可以设置一个标记，表示用户选择跳过分析
}

// 响应式处理
window.addEventListener('resize', uiStore.updateScreenSize)
uiStore.updateScreenSize()

// 格式化文件大小
const formatFileSize = (size?: number) => {
  if (!size) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let unitIndex = 0
  let fileSize = size

  while (fileSize >= 1024 && unitIndex < units.length - 1) {
    fileSize /= 1024
    unitIndex++
  }

  return `${fileSize.toFixed(1)} ${units[unitIndex]}`
}

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取状态文本
const getStatusText = (status?: string) => {
  const statusMap: Record<string, string> = {
    UPLOADED: '已上传',
    PROCESSING: '处理中',
    COMPLETED: '已完成',
    FAILED: '处理失败'
  }
  return statusMap[status || ''] || status || '未知'
}
</script>

<style scoped>
.document-analysis {
  height: 100vh;
  background: url('@/assets/images/bg.png') no-repeat;
  background-size: 100% 100%;
  background-position: 50%;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
}

.document-analysis.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.global-loading {
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
}

.workflow-progress {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1001;
  background: white;
  padding: 8px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.header {
  background-color: rgba(255, 255, 255, 0.3);
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  gap: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 600px;
}

.document-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.doc-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.stats-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.title {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sidebar {
  background-color: #fff;
  border-right: 1px solid #e4e7ed;
}

.analysis-tabs {
  height: 100%;
}

.results-container {
  padding: 20px;
  height: calc(100vh - 180px);
  overflow-y: auto;
}

.main-content {
  background-color: #fff;
  padding: 20px;
}

.document-viewer {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  background-color: #fafafa;
}

.footer {
  background-color: #fff;
  border-top: 1px solid #e4e7ed;
  padding: 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 20px;
  color: #909399;
  font-size: 14px;
}

.document-content {
  height: 100%;
  padding: 20px;
}

.document-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.document-info h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.document-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.content-layout {
  display: flex;
  height: 100%;
  gap: 0;
}

.navigation-panel {
  width: 300px;
  min-width: 250px;
  max-width: 400px;
  height: 100%;
  border-right: 1px solid #e4e7ed;
  background: white;
  overflow: hidden;
}

.document-display {
  flex: 1;
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
}

.document-display.full-width {
  width: 100%;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: white;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.info-card h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.info-card p {
  margin: 12px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.6;
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .navigation-panel {
    width: 250px;
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .content-layout {
    flex-direction: column;
  }

  .navigation-panel {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
  }

  .document-display {
    height: calc(100% - 200px);
  }

  .info-card {
    margin: 10px;
    padding: 16px;
  }
}

/* 新的三栏布局样式 */
.main-layout {
  height: calc(100vh - 60px);
}

/* 左侧分析结果面板 */
.left-panel,
.right-panel {
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.3), hsla(0, 0%, 100%, 0.5));
  box-shadow: 0 0 10px 0 rgba(215, 0, 0, 0.1);
  border-radius: 6px;
  margin: 10px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 16px;
}

.status-tag {
  margin-left: 8px;
  font-size: 11px;
}

.panel-actions {
  display: flex;
  gap: 8px;
}

.analysis-results-container {
  flex: 1;
  overflow: hidden;
  padding: 0 10px;
  display: flex;
  flex-direction: column;
}

.results-mode-switch {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

/* 中间文档展示面板 */
.center-panel {
  display: flex;
  flex-direction: column;
  padding: 0;
  background: #fff;
  margin: 10px;
  border-radius: 6px;
}

.document-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.document-title {
  font-weight: 500;
  color: #303133;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-content-area {
  flex: 1;
  overflow: hidden;
  background: white;
  margin: 8px;
  /* border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}

/* 分析提示卡片样式 */
.analysis-prompt {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
}

.prompt-card {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.prompt-icon {
  margin-bottom: 20px;
  opacity: 0.9;
}

.prompt-card h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: white;
}

.prompt-card p {
  font-size: 16px;
  margin-bottom: 30px;
  opacity: 0.9;
  line-height: 1.6;
}

.prompt-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.prompt-actions .el-button {
  min-width: 200px;
}

.prompt-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 500;
}

.prompt-actions .el-button--primary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.prompt-actions .el-button:not(.el-button--primary) {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
}

.prompt-actions .el-button:not(.el-button--primary):hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* 右侧聊天机器人面板 */
.right-panel {
}

.chat-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.chat-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-size: 16px;
}

.chat-status {
  display: flex;
  align-items: center;
}

.chat-container {
  flex: 1;
  overflow: hidden;
  background: white;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .left-panel {
    width: 280px !important;
  }

  .right-panel {
    width: 320px !important;
  }
}

@media (max-width: 1200px) {
  .left-panel {
    width: 260px !important;
  }

  .right-panel {
    width: 300px !important;
  }

  .document-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .toolbar-center {
    order: -1;
  }
}

@media (max-width: 768px) {
  .left-panel {
    width: 240px !important;
  }

  .right-panel {
    width: 280px !important;
  }

  .toolbar-left,
  .toolbar-right {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
