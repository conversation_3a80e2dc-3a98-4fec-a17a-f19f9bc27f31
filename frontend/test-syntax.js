// 快速语法验证脚本
const fs = require('fs')
const path = require('path')

console.log('🔍 检查Vue模板语法...')

const componentPath = path.join(__dirname, 'src/components/DocumentPdfViewer.vue')
const content = fs.readFileSync(componentPath, 'utf8')

// 检查常见的Vue模板语法错误
const checks = [
  {
    name: '检查:class属性语法',
    pattern: /:class="[^"]*"[^>]*>/g,
    test: (matches) => {
      return matches.every(match => {
        // 检查是否有未闭合的大括号
        const openBraces = (match.match(/{/g) || []).length
        const closeBraces = (match.match(/}/g) || []).length
        return openBraces === closeBraces
      })
    }
  },
  {
    name: '检查模板插值语法',
    pattern: /\{\{[^}]*\}\}/g,
    test: (matches) => {
      return matches.every(match => {
        return match.startsWith('{{') && match.endsWith('}}')
      })
    }
  },
  {
    name: '检查v-指令语法',
    pattern: /v-[a-zA-Z-]+="[^"]*"/g,
    test: (matches) => {
      return matches.every(match => {
        return match.includes('="') && match.endsWith('"')
      })
    }
  }
]

let allPassed = true

checks.forEach(check => {
  const matches = content.match(check.pattern) || []
  const passed = check.test(matches)
  
  console.log(`${passed ? '✅' : '❌'} ${check.name}: ${matches.length} 个匹配项`)
  
  if (!passed) {
    allPassed = false
    console.log('   失败的匹配项:', matches.slice(0, 3))
  }
})

// 特别检查我们修复的行
const targetLine = content.split('\n')[2] // 第3行 (索引2)
if (targetLine && targetLine.includes(':class=')) {
  const hasProperSyntax = targetLine.match(/:class="\{[^}]*\}"/) !== null
  console.log(`${hasProperSyntax ? '✅' : '❌'} 目标行语法检查: ${targetLine.trim()}`)
  if (!hasProperSyntax) allPassed = false
}

if (allPassed) {
  console.log('🎉 所有语法检查通过！')
  process.exit(0)
} else {
  console.log('❌ 发现语法错误，请检查上述问题')
  process.exit(1)
}
