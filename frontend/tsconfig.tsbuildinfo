{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./src/app.vue.ts", "./node_modules/element-plus/es/utils/vue3.3.polyfill.d.ts", "./node_modules/dayjs/locale/types.d.ts", "./node_modules/dayjs/locale/index.d.ts", "./node_modules/dayjs/index.d.ts", "./node_modules/@types/lodash/common/common.d.ts", "./node_modules/@types/lodash/common/array.d.ts", "./node_modules/@types/lodash/common/collection.d.ts", "./node_modules/@types/lodash/common/date.d.ts", "./node_modules/@types/lodash/common/function.d.ts", "./node_modules/@types/lodash/common/lang.d.ts", "./node_modules/@types/lodash/common/math.d.ts", "./node_modules/@types/lodash/common/number.d.ts", "./node_modules/@types/lodash/common/object.d.ts", "./node_modules/@types/lodash/common/seq.d.ts", "./node_modules/@types/lodash/common/string.d.ts", "./node_modules/@types/lodash/common/util.d.ts", "./node_modules/@types/lodash/index.d.ts", "./node_modules/async-validator/dist-types/interface.d.ts", "./node_modules/async-validator/dist-types/index.d.ts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/@vueuse/shared/index.d.ts", "./node_modules/@vueuse/core/index.d.ts", "./node_modules/memoize-one/dist/memoize-one.d.ts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.d.mts", "./node_modules/@floating-ui/core/dist/floating-ui.core.d.mts", "./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.d.mts", "./node_modules/@floating-ui/dom/dist/floating-ui.dom.d.mts", "./node_modules/vue-router/dist/vue-router.d.ts", "./node_modules/@ctrl/tinycolor/dist/interfaces.d.ts", "./node_modules/@ctrl/tinycolor/dist/index.d.ts", "./node_modules/@ctrl/tinycolor/dist/css-color-names.d.ts", "./node_modules/@ctrl/tinycolor/dist/readability.d.ts", "./node_modules/@ctrl/tinycolor/dist/to-ms-filter.d.ts", "./node_modules/@ctrl/tinycolor/dist/from-ratio.d.ts", "./node_modules/@ctrl/tinycolor/dist/format-input.d.ts", "./node_modules/@ctrl/tinycolor/dist/random.d.ts", "./node_modules/@ctrl/tinycolor/dist/conversion.d.ts", "./node_modules/@ctrl/tinycolor/dist/public_api.d.ts", "./node_modules/element-plus/es/index.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/add-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/aim.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/alarm-clock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/apple.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-down-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-down.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-left-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-right-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-up-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/arrow-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/avatar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/back.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/baseball.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/basketball.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bell-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bell.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bicycle.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bottom.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/bowl.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/briefcase.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/brush-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/brush.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/burger.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/calendar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/camera-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/camera.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-bottom.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/caret-top.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cellphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-dot-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-dot-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-line-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-line-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chat-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/check.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cherry.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chicken.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/chrome-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-check-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-check.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-close-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-close.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-plus-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/circle-plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/clock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/close-bold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/close.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coffee-cup.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coffee.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coin.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cold-drink.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/collection-tag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/collection.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/comment.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/compass.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/connection.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/coordinate.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/copy-document.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/cpu.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/credit-card.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/crop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-arrow-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-arrow-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/d-caret.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-analysis.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-board.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/data-line.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dessert.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/discount.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dish-dot.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/dish.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-add.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-copy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document-remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/document.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/download.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/drizzling.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/edit-pen.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/edit.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/eleme-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/eleme.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/element-plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/expand.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/failed.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/female.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/files.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/film.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/filter.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/finished.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/first-aid-kit.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/flag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fold.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-add.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-checked.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-delete.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-opened.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder-remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/folder.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/food.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/football.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fork-spoon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/fries.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/full-screen.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-square-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goblet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/gold-medal.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goods-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/goods.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/grape.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/grid.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/guide.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/handbag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/headset.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/help-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/help.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/hide.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/histogram.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/home-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/hot-water.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/house.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream-round.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream-square.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-cream.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-drink.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ice-tea.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/info-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/iphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/key.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/knife-fork.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lightning.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/link.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/list.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/loading.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location-information.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/lollipop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/magic-stick.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/magnet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/male.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/management.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/map-location.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/medal.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/memo.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/menu.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/message-box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/message.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mic.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/microphone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/milk-tea.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/minus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/money.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/monitor.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/moon-night.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/moon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/more-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/more.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mostly-cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mouse.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mug.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mute-notification.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/mute.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/no-smoking.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/notebook.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/notification.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/odometer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/office-building.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/open.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/operation.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/opportunity.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/orange.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/paperclip.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/partly-cloudy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pear.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/phone-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/phone.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture-rounded.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/picture.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pie-chart.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/place.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/platform.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/plus.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pointer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/position.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/postcard.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/pouring.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/present.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/price-tag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/printer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/promotion.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/quartz-watch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/question-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/rank.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/reading-lamp.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/reading.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refresh.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/refrigerator.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/remove-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/remove.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/scale-to-original.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/school.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/scissor.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/search.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/select.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sell.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/semi-select.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/service.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/set-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/setting.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/share.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ship.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shop.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-bag.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-cart-full.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-cart.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/shopping-trolley.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/smoking.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/soccer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sold-out.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort-down.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort-up.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sort.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/stamp.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/star-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/star.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/stopwatch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/success-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sugar.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/suitcase-line.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/suitcase.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunny.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunrise.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/sunset.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch-button.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/switch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/takeaway-box.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/ticket.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/tickets.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/timer.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/toilet-paper.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/tools.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top-left.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top-right.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/top.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trend-charts.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trophy-base.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/trophy.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/turn-off.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/umbrella.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/unlock.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/upload-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/upload.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/user-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/user.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/van.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-camera-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-camera.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-pause.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/video-play.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/view.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wallet-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wallet.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warn-triangle-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warning-filled.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/warning.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/watch.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/watermelon.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/wind-power.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/zoom-in.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/zoom-out.vue.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/components/index.d.ts", "./node_modules/@element-plus/icons-vue/dist/types/index.d.ts", "./node_modules/axios/index.d.ts", "./node_modules/web-storage-cache/src/web-storage-cache.d.ts", "./src/hooks/web/usecache.ts", "./src/utils/auth.ts", "./src/api/request.ts", "./src/types/index.ts", "./src/api/analysis.ts", "./src/components/analysisresults.vue.ts", "./src/components/documentnavigator.vue.ts", "./node_modules/pinia/dist/pinia.d.ts", "./src/stores/document.ts", "./src/components/documentupload.vue.ts", "./src/api/document.ts", "./src/utils/positionmapper.ts", "./src/utils/highlight.ts", "./src/components/highlightinteraction.vue.ts", "./node_modules/pdfjs-dist/types/src/shared/util.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/tools.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/toolbar.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/editor.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/freetext.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/highlight.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/draw.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/drawers/outline.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/drawers/inkdraw.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/ink.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/stamp.d.ts", "./node_modules/pdfjs-dist/types/src/display/display_utils.d.ts", "./node_modules/pdfjs-dist/types/web/text_accessibility.d.ts", "./node_modules/pdfjs-dist/types/src/display/optional_content_config.d.ts", "./node_modules/pdfjs-dist/types/src/display/annotation_storage.d.ts", "./node_modules/pdfjs-dist/types/src/display/canvas_factory.d.ts", "./node_modules/pdfjs-dist/types/src/display/cmap_reader_factory.d.ts", "./node_modules/pdfjs-dist/types/src/display/filter_factory.d.ts", "./node_modules/pdfjs-dist/types/src/display/standard_fontdata_factory.d.ts", "./node_modules/@napi-rs/canvas/index.d.ts", "./node_modules/pdfjs-dist/types/src/display/node_utils.d.ts", "./node_modules/pdfjs-dist/types/src/display/metadata.d.ts", "./node_modules/pdfjs-dist/types/src/shared/message_handler.d.ts", "./node_modules/pdfjs-dist/types/src/display/api.d.ts", "./node_modules/pdfjs-dist/types/web/interfaces.d.ts", "./node_modules/pdfjs-dist/types/web/struct_tree_layer_builder.d.ts", "./node_modules/pdfjs-dist/types/src/display/annotation_layer.d.ts", "./node_modules/pdfjs-dist/types/src/display/draw_layer.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/annotation_editor_layer.d.ts", "./node_modules/pdfjs-dist/types/src/display/editor/color_picker.d.ts", "./node_modules/pdfjs-dist/types/src/display/svg_factory.d.ts", "./node_modules/pdfjs-dist/types/src/display/worker_options.d.ts", "./node_modules/pdfjs-dist/types/src/display/text_layer.d.ts", "./node_modules/pdfjs-dist/types/src/display/touch_manager.d.ts", "./node_modules/pdfjs-dist/types/src/display/xfa_layer.d.ts", "./node_modules/pdfjs-dist/types/src/pdf.d.ts", "./node_modules/vue-pdf-embed/dist/types/types.d.ts", "./node_modules/vue-pdf-embed/dist/types/composables.d.ts", "./node_modules/pdfjs-dist/types/web/annotation_layer_builder.d.ts", "./node_modules/pdfjs-dist/types/web/download_manager.d.ts", "./node_modules/pdfjs-dist/types/web/event_utils.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_find_controller.d.ts", "./node_modules/pdfjs-dist/types/web/l10n.d.ts", "./node_modules/pdfjs-dist/types/web/genericl10n.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_link_service.d.ts", "./node_modules/pdfjs-dist/types/web/ui_utils.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_history.d.ts", "./node_modules/pdfjs-dist/types/web/text_highlighter.d.ts", "./node_modules/pdfjs-dist/types/web/text_layer_builder.d.ts", "./node_modules/pdfjs-dist/types/web/xfa_layer_builder.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_scripting_manager.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_viewer.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_thumbnail_viewer.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_rendering_queue.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_page_view.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_scripting_manager.component.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_single_page_viewer.d.ts", "./node_modules/pdfjs-dist/types/web/pdf_viewer.component.d.ts", "./node_modules/pdfjs-dist/web/pdf_viewer.d.mts", "./node_modules/vue-pdf-embed/dist/types/vuepdfembed.vue.d.ts", "./node_modules/vue-pdf-embed/dist/types/index.d.ts", "./src/types/pdf.ts", "./src/utils/pdfconfig.ts", "./src/components/documentviewer.vue.ts", "./src/components/structuredanalysisresults.vue.ts", "./src/stores/analysis.ts", "./src/stores/ui.ts", "./src/utils/errorhandler.ts", "./src/views/documentanalysis.vue.ts", "./node_modules/@types/lodash-es/add.d.ts", "./node_modules/@types/lodash-es/after.d.ts", "./node_modules/@types/lodash-es/ary.d.ts", "./node_modules/@types/lodash-es/assign.d.ts", "./node_modules/@types/lodash-es/assignin.d.ts", "./node_modules/@types/lodash-es/assigninwith.d.ts", "./node_modules/@types/lodash-es/assignwith.d.ts", "./node_modules/@types/lodash-es/at.d.ts", "./node_modules/@types/lodash-es/attempt.d.ts", "./node_modules/@types/lodash-es/before.d.ts", "./node_modules/@types/lodash-es/bind.d.ts", "./node_modules/@types/lodash-es/bindall.d.ts", "./node_modules/@types/lodash-es/bindkey.d.ts", "./node_modules/@types/lodash-es/camelcase.d.ts", "./node_modules/@types/lodash-es/capitalize.d.ts", "./node_modules/@types/lodash-es/castarray.d.ts", "./node_modules/@types/lodash-es/ceil.d.ts", "./node_modules/@types/lodash-es/chain.d.ts", "./node_modules/@types/lodash-es/chunk.d.ts", "./node_modules/@types/lodash-es/clamp.d.ts", "./node_modules/@types/lodash-es/clone.d.ts", "./node_modules/@types/lodash-es/clonedeep.d.ts", "./node_modules/@types/lodash-es/clonedeepwith.d.ts", "./node_modules/@types/lodash-es/clonewith.d.ts", "./node_modules/@types/lodash-es/compact.d.ts", "./node_modules/@types/lodash-es/concat.d.ts", "./node_modules/@types/lodash-es/cond.d.ts", "./node_modules/@types/lodash-es/conforms.d.ts", "./node_modules/@types/lodash-es/conformsto.d.ts", "./node_modules/@types/lodash-es/constant.d.ts", "./node_modules/@types/lodash-es/countby.d.ts", "./node_modules/@types/lodash-es/create.d.ts", "./node_modules/@types/lodash-es/curry.d.ts", "./node_modules/@types/lodash-es/curryright.d.ts", "./node_modules/@types/lodash-es/debounce.d.ts", "./node_modules/@types/lodash-es/deburr.d.ts", "./node_modules/@types/lodash-es/defaults.d.ts", "./node_modules/@types/lodash-es/defaultsdeep.d.ts", "./node_modules/@types/lodash-es/defaultto.d.ts", "./node_modules/@types/lodash-es/defer.d.ts", "./node_modules/@types/lodash-es/delay.d.ts", "./node_modules/@types/lodash-es/difference.d.ts", "./node_modules/@types/lodash-es/differenceby.d.ts", "./node_modules/@types/lodash-es/differencewith.d.ts", "./node_modules/@types/lodash-es/divide.d.ts", "./node_modules/@types/lodash-es/drop.d.ts", "./node_modules/@types/lodash-es/dropright.d.ts", "./node_modules/@types/lodash-es/droprightwhile.d.ts", "./node_modules/@types/lodash-es/dropwhile.d.ts", "./node_modules/@types/lodash-es/each.d.ts", "./node_modules/@types/lodash-es/eachright.d.ts", "./node_modules/@types/lodash-es/endswith.d.ts", "./node_modules/@types/lodash-es/entries.d.ts", "./node_modules/@types/lodash-es/entriesin.d.ts", "./node_modules/@types/lodash-es/eq.d.ts", "./node_modules/@types/lodash-es/escape.d.ts", "./node_modules/@types/lodash-es/escaperegexp.d.ts", "./node_modules/@types/lodash-es/every.d.ts", "./node_modules/@types/lodash-es/extend.d.ts", "./node_modules/@types/lodash-es/extendwith.d.ts", "./node_modules/@types/lodash-es/fill.d.ts", "./node_modules/@types/lodash-es/filter.d.ts", "./node_modules/@types/lodash-es/find.d.ts", "./node_modules/@types/lodash-es/findindex.d.ts", "./node_modules/@types/lodash-es/findkey.d.ts", "./node_modules/@types/lodash-es/findlast.d.ts", "./node_modules/@types/lodash-es/findlastindex.d.ts", "./node_modules/@types/lodash-es/findlastkey.d.ts", "./node_modules/@types/lodash-es/first.d.ts", "./node_modules/@types/lodash-es/flatmap.d.ts", "./node_modules/@types/lodash-es/flatmapdeep.d.ts", "./node_modules/@types/lodash-es/flatmapdepth.d.ts", "./node_modules/@types/lodash-es/flatten.d.ts", "./node_modules/@types/lodash-es/flattendeep.d.ts", "./node_modules/@types/lodash-es/flattendepth.d.ts", "./node_modules/@types/lodash-es/flip.d.ts", "./node_modules/@types/lodash-es/floor.d.ts", "./node_modules/@types/lodash-es/flow.d.ts", "./node_modules/@types/lodash-es/flowright.d.ts", "./node_modules/@types/lodash-es/foreach.d.ts", "./node_modules/@types/lodash-es/foreachright.d.ts", "./node_modules/@types/lodash-es/forin.d.ts", "./node_modules/@types/lodash-es/forinright.d.ts", "./node_modules/@types/lodash-es/forown.d.ts", "./node_modules/@types/lodash-es/forownright.d.ts", "./node_modules/@types/lodash-es/frompairs.d.ts", "./node_modules/@types/lodash-es/functions.d.ts", "./node_modules/@types/lodash-es/functionsin.d.ts", "./node_modules/@types/lodash-es/get.d.ts", "./node_modules/@types/lodash-es/groupby.d.ts", "./node_modules/@types/lodash-es/gt.d.ts", "./node_modules/@types/lodash-es/gte.d.ts", "./node_modules/@types/lodash-es/has.d.ts", "./node_modules/@types/lodash-es/hasin.d.ts", "./node_modules/@types/lodash-es/head.d.ts", "./node_modules/@types/lodash-es/identity.d.ts", "./node_modules/@types/lodash-es/includes.d.ts", "./node_modules/@types/lodash-es/indexof.d.ts", "./node_modules/@types/lodash-es/initial.d.ts", "./node_modules/@types/lodash-es/inrange.d.ts", "./node_modules/@types/lodash-es/intersection.d.ts", "./node_modules/@types/lodash-es/intersectionby.d.ts", "./node_modules/@types/lodash-es/intersectionwith.d.ts", "./node_modules/@types/lodash-es/invert.d.ts", "./node_modules/@types/lodash-es/invertby.d.ts", "./node_modules/@types/lodash-es/invoke.d.ts", "./node_modules/@types/lodash-es/invokemap.d.ts", "./node_modules/@types/lodash-es/isarguments.d.ts", "./node_modules/@types/lodash-es/isarray.d.ts", "./node_modules/@types/lodash-es/isarraybuffer.d.ts", "./node_modules/@types/lodash-es/isarraylike.d.ts", "./node_modules/@types/lodash-es/isarraylikeobject.d.ts", "./node_modules/@types/lodash-es/isboolean.d.ts", "./node_modules/@types/lodash-es/isbuffer.d.ts", "./node_modules/@types/lodash-es/isdate.d.ts", "./node_modules/@types/lodash-es/iselement.d.ts", "./node_modules/@types/lodash-es/isempty.d.ts", "./node_modules/@types/lodash-es/isequal.d.ts", "./node_modules/@types/lodash-es/isequalwith.d.ts", "./node_modules/@types/lodash-es/iserror.d.ts", "./node_modules/@types/lodash-es/isfinite.d.ts", "./node_modules/@types/lodash-es/isfunction.d.ts", "./node_modules/@types/lodash-es/isinteger.d.ts", "./node_modules/@types/lodash-es/islength.d.ts", "./node_modules/@types/lodash-es/ismap.d.ts", "./node_modules/@types/lodash-es/ismatch.d.ts", "./node_modules/@types/lodash-es/ismatchwith.d.ts", "./node_modules/@types/lodash-es/isnan.d.ts", "./node_modules/@types/lodash-es/isnative.d.ts", "./node_modules/@types/lodash-es/isnil.d.ts", "./node_modules/@types/lodash-es/isnull.d.ts", "./node_modules/@types/lodash-es/isnumber.d.ts", "./node_modules/@types/lodash-es/isobject.d.ts", "./node_modules/@types/lodash-es/isobjectlike.d.ts", "./node_modules/@types/lodash-es/isplainobject.d.ts", "./node_modules/@types/lodash-es/isregexp.d.ts", "./node_modules/@types/lodash-es/issafeinteger.d.ts", "./node_modules/@types/lodash-es/isset.d.ts", "./node_modules/@types/lodash-es/isstring.d.ts", "./node_modules/@types/lodash-es/issymbol.d.ts", "./node_modules/@types/lodash-es/istypedarray.d.ts", "./node_modules/@types/lodash-es/isundefined.d.ts", "./node_modules/@types/lodash-es/isweakmap.d.ts", "./node_modules/@types/lodash-es/isweakset.d.ts", "./node_modules/@types/lodash-es/iteratee.d.ts", "./node_modules/@types/lodash-es/join.d.ts", "./node_modules/@types/lodash-es/kebabcase.d.ts", "./node_modules/@types/lodash-es/keyby.d.ts", "./node_modules/@types/lodash-es/keys.d.ts", "./node_modules/@types/lodash-es/keysin.d.ts", "./node_modules/@types/lodash-es/last.d.ts", "./node_modules/@types/lodash-es/lastindexof.d.ts", "./node_modules/@types/lodash-es/lowercase.d.ts", "./node_modules/@types/lodash-es/lowerfirst.d.ts", "./node_modules/@types/lodash-es/lt.d.ts", "./node_modules/@types/lodash-es/lte.d.ts", "./node_modules/@types/lodash-es/map.d.ts", "./node_modules/@types/lodash-es/mapkeys.d.ts", "./node_modules/@types/lodash-es/mapvalues.d.ts", "./node_modules/@types/lodash-es/matches.d.ts", "./node_modules/@types/lodash-es/matchesproperty.d.ts", "./node_modules/@types/lodash-es/max.d.ts", "./node_modules/@types/lodash-es/maxby.d.ts", "./node_modules/@types/lodash-es/mean.d.ts", "./node_modules/@types/lodash-es/meanby.d.ts", "./node_modules/@types/lodash-es/memoize.d.ts", "./node_modules/@types/lodash-es/merge.d.ts", "./node_modules/@types/lodash-es/mergewith.d.ts", "./node_modules/@types/lodash-es/method.d.ts", "./node_modules/@types/lodash-es/methodof.d.ts", "./node_modules/@types/lodash-es/min.d.ts", "./node_modules/@types/lodash-es/minby.d.ts", "./node_modules/@types/lodash-es/mixin.d.ts", "./node_modules/@types/lodash-es/multiply.d.ts", "./node_modules/@types/lodash-es/negate.d.ts", "./node_modules/@types/lodash-es/noop.d.ts", "./node_modules/@types/lodash-es/now.d.ts", "./node_modules/@types/lodash-es/nth.d.ts", "./node_modules/@types/lodash-es/ntharg.d.ts", "./node_modules/@types/lodash-es/omit.d.ts", "./node_modules/@types/lodash-es/omitby.d.ts", "./node_modules/@types/lodash-es/once.d.ts", "./node_modules/@types/lodash-es/orderby.d.ts", "./node_modules/@types/lodash-es/over.d.ts", "./node_modules/@types/lodash-es/overargs.d.ts", "./node_modules/@types/lodash-es/overevery.d.ts", "./node_modules/@types/lodash-es/oversome.d.ts", "./node_modules/@types/lodash-es/pad.d.ts", "./node_modules/@types/lodash-es/padend.d.ts", "./node_modules/@types/lodash-es/padstart.d.ts", "./node_modules/@types/lodash-es/parseint.d.ts", "./node_modules/@types/lodash-es/partial.d.ts", "./node_modules/@types/lodash-es/partialright.d.ts", "./node_modules/@types/lodash-es/partition.d.ts", "./node_modules/@types/lodash-es/pick.d.ts", "./node_modules/@types/lodash-es/pickby.d.ts", "./node_modules/@types/lodash-es/property.d.ts", "./node_modules/@types/lodash-es/propertyof.d.ts", "./node_modules/@types/lodash-es/pull.d.ts", "./node_modules/@types/lodash-es/pullall.d.ts", "./node_modules/@types/lodash-es/pullallby.d.ts", "./node_modules/@types/lodash-es/pullallwith.d.ts", "./node_modules/@types/lodash-es/pullat.d.ts", "./node_modules/@types/lodash-es/random.d.ts", "./node_modules/@types/lodash-es/range.d.ts", "./node_modules/@types/lodash-es/rangeright.d.ts", "./node_modules/@types/lodash-es/rearg.d.ts", "./node_modules/@types/lodash-es/reduce.d.ts", "./node_modules/@types/lodash-es/reduceright.d.ts", "./node_modules/@types/lodash-es/reject.d.ts", "./node_modules/@types/lodash-es/remove.d.ts", "./node_modules/@types/lodash-es/repeat.d.ts", "./node_modules/@types/lodash-es/replace.d.ts", "./node_modules/@types/lodash-es/rest.d.ts", "./node_modules/@types/lodash-es/result.d.ts", "./node_modules/@types/lodash-es/reverse.d.ts", "./node_modules/@types/lodash-es/round.d.ts", "./node_modules/@types/lodash-es/sample.d.ts", "./node_modules/@types/lodash-es/samplesize.d.ts", "./node_modules/@types/lodash-es/set.d.ts", "./node_modules/@types/lodash-es/setwith.d.ts", "./node_modules/@types/lodash-es/shuffle.d.ts", "./node_modules/@types/lodash-es/size.d.ts", "./node_modules/@types/lodash-es/slice.d.ts", "./node_modules/@types/lodash-es/snakecase.d.ts", "./node_modules/@types/lodash-es/some.d.ts", "./node_modules/@types/lodash-es/sortby.d.ts", "./node_modules/@types/lodash-es/sortedindex.d.ts", "./node_modules/@types/lodash-es/sortedindexby.d.ts", "./node_modules/@types/lodash-es/sortedindexof.d.ts", "./node_modules/@types/lodash-es/sortedlastindex.d.ts", "./node_modules/@types/lodash-es/sortedlastindexby.d.ts", "./node_modules/@types/lodash-es/sortedlastindexof.d.ts", "./node_modules/@types/lodash-es/sorteduniq.d.ts", "./node_modules/@types/lodash-es/sorteduniqby.d.ts", "./node_modules/@types/lodash-es/split.d.ts", "./node_modules/@types/lodash-es/spread.d.ts", "./node_modules/@types/lodash-es/startcase.d.ts", "./node_modules/@types/lodash-es/startswith.d.ts", "./node_modules/@types/lodash-es/stubarray.d.ts", "./node_modules/@types/lodash-es/stubfalse.d.ts", "./node_modules/@types/lodash-es/stubobject.d.ts", "./node_modules/@types/lodash-es/stubstring.d.ts", "./node_modules/@types/lodash-es/stubtrue.d.ts", "./node_modules/@types/lodash-es/subtract.d.ts", "./node_modules/@types/lodash-es/sum.d.ts", "./node_modules/@types/lodash-es/sumby.d.ts", "./node_modules/@types/lodash-es/tail.d.ts", "./node_modules/@types/lodash-es/take.d.ts", "./node_modules/@types/lodash-es/takeright.d.ts", "./node_modules/@types/lodash-es/takerightwhile.d.ts", "./node_modules/@types/lodash-es/takewhile.d.ts", "./node_modules/@types/lodash-es/tap.d.ts", "./node_modules/@types/lodash-es/template.d.ts", "./node_modules/@types/lodash-es/templatesettings.d.ts", "./node_modules/@types/lodash-es/throttle.d.ts", "./node_modules/@types/lodash-es/thru.d.ts", "./node_modules/@types/lodash-es/times.d.ts", "./node_modules/@types/lodash-es/toarray.d.ts", "./node_modules/@types/lodash-es/tofinite.d.ts", "./node_modules/@types/lodash-es/tointeger.d.ts", "./node_modules/@types/lodash-es/tolength.d.ts", "./node_modules/@types/lodash-es/tolower.d.ts", "./node_modules/@types/lodash-es/tonumber.d.ts", "./node_modules/@types/lodash-es/topairs.d.ts", "./node_modules/@types/lodash-es/topairsin.d.ts", "./node_modules/@types/lodash-es/topath.d.ts", "./node_modules/@types/lodash-es/toplainobject.d.ts", "./node_modules/@types/lodash-es/tosafeinteger.d.ts", "./node_modules/@types/lodash-es/tostring.d.ts", "./node_modules/@types/lodash-es/toupper.d.ts", "./node_modules/@types/lodash-es/transform.d.ts", "./node_modules/@types/lodash-es/trim.d.ts", "./node_modules/@types/lodash-es/trimend.d.ts", "./node_modules/@types/lodash-es/trimstart.d.ts", "./node_modules/@types/lodash-es/truncate.d.ts", "./node_modules/@types/lodash-es/unary.d.ts", "./node_modules/@types/lodash-es/unescape.d.ts", "./node_modules/@types/lodash-es/union.d.ts", "./node_modules/@types/lodash-es/unionby.d.ts", "./node_modules/@types/lodash-es/unionwith.d.ts", "./node_modules/@types/lodash-es/uniq.d.ts", "./node_modules/@types/lodash-es/uniqby.d.ts", "./node_modules/@types/lodash-es/uniqueid.d.ts", "./node_modules/@types/lodash-es/uniqwith.d.ts", "./node_modules/@types/lodash-es/unset.d.ts", "./node_modules/@types/lodash-es/unzip.d.ts", "./node_modules/@types/lodash-es/unzipwith.d.ts", "./node_modules/@types/lodash-es/update.d.ts", "./node_modules/@types/lodash-es/updatewith.d.ts", "./node_modules/@types/lodash-es/uppercase.d.ts", "./node_modules/@types/lodash-es/upperfirst.d.ts", "./node_modules/@types/lodash-es/values.d.ts", "./node_modules/@types/lodash-es/valuesin.d.ts", "./node_modules/@types/lodash-es/without.d.ts", "./node_modules/@types/lodash-es/words.d.ts", "./node_modules/@types/lodash-es/wrap.d.ts", "./node_modules/@types/lodash-es/xor.d.ts", "./node_modules/@types/lodash-es/xorby.d.ts", "./node_modules/@types/lodash-es/xorwith.d.ts", "./node_modules/@types/lodash-es/zip.d.ts", "./node_modules/@types/lodash-es/zipobject.d.ts", "./node_modules/@types/lodash-es/zipobjectdeep.d.ts", "./node_modules/@types/lodash-es/zipwith.d.ts", "./node_modules/@types/lodash-es/index.d.ts", "./node_modules/moment/ts3.1-typings/moment.d.ts", "./src/utils/util.ts", "./src/constants/chat.ts", "./src/constants/static.ts", "./src/constants/index.ts", "./node_modules/@microsoft/fetch-event-source/lib/cjs/parse.d.ts", "./node_modules/@microsoft/fetch-event-source/lib/cjs/fetch.d.ts", "./node_modules/@microsoft/fetch-event-source/lib/cjs/index.d.ts", "./node_modules/mitt/index.d.ts", "./src/utils/eventhub.ts", "./src/utils/sse.ts", "./node_modules/typed.js/index.d.ts", "./src/views/ai/components/client-chat.vue.ts", "./src/views/ai/components/problem.json", "./src/views/ai/components/problem.vue.ts", "./src/views/ai/components/history.vue.ts", "./src/api/maxkb.ts", "./src/utils/request-chat.ts", "./src/api/chat.ts", "./node_modules/element-plus/es/locale/lang/af.d.ts", "./node_modules/element-plus/es/locale/lang/ar-eg.d.ts", "./node_modules/element-plus/es/locale/lang/ar.d.ts", "./node_modules/element-plus/es/locale/lang/az.d.ts", "./node_modules/element-plus/es/locale/lang/bg.d.ts", "./node_modules/element-plus/es/locale/lang/bn.d.ts", "./node_modules/element-plus/es/locale/lang/ca.d.ts", "./node_modules/element-plus/es/locale/lang/ckb.d.ts", "./node_modules/element-plus/es/locale/lang/cs.d.ts", "./node_modules/element-plus/es/locale/lang/da.d.ts", "./node_modules/element-plus/es/locale/lang/de.d.ts", "./node_modules/element-plus/es/locale/lang/el.d.ts", "./node_modules/element-plus/es/locale/lang/en.d.ts", "./node_modules/element-plus/es/locale/lang/eo.d.ts", "./node_modules/element-plus/es/locale/lang/es.d.ts", "./node_modules/element-plus/es/locale/lang/et.d.ts", "./node_modules/element-plus/es/locale/lang/eu.d.ts", "./node_modules/element-plus/es/locale/lang/fa.d.ts", "./node_modules/element-plus/es/locale/lang/fi.d.ts", "./node_modules/element-plus/es/locale/lang/fr.d.ts", "./node_modules/element-plus/es/locale/lang/he.d.ts", "./node_modules/element-plus/es/locale/lang/hi.d.ts", "./node_modules/element-plus/es/locale/lang/hr.d.ts", "./node_modules/element-plus/es/locale/lang/hu.d.ts", "./node_modules/element-plus/es/locale/lang/hy-am.d.ts", "./node_modules/element-plus/es/locale/lang/id.d.ts", "./node_modules/element-plus/es/locale/lang/it.d.ts", "./node_modules/element-plus/es/locale/lang/ja.d.ts", "./node_modules/element-plus/es/locale/lang/kk.d.ts", "./node_modules/element-plus/es/locale/lang/km.d.ts", "./node_modules/element-plus/es/locale/lang/ko.d.ts", "./node_modules/element-plus/es/locale/lang/ku.d.ts", "./node_modules/element-plus/es/locale/lang/ky.d.ts", "./node_modules/element-plus/es/locale/lang/lo.d.ts", "./node_modules/element-plus/es/locale/lang/lt.d.ts", "./node_modules/element-plus/es/locale/lang/lv.d.ts", "./node_modules/element-plus/es/locale/lang/mg.d.ts", "./node_modules/element-plus/es/locale/lang/mn.d.ts", "./node_modules/element-plus/es/locale/lang/ms.d.ts", "./node_modules/element-plus/es/locale/lang/my.d.ts", "./node_modules/element-plus/es/locale/lang/nb-no.d.ts", "./node_modules/element-plus/es/locale/lang/nl.d.ts", "./node_modules/element-plus/es/locale/lang/no.d.ts", "./node_modules/element-plus/es/locale/lang/pa.d.ts", "./node_modules/element-plus/es/locale/lang/pl.d.ts", "./node_modules/element-plus/es/locale/lang/pt-br.d.ts", "./node_modules/element-plus/es/locale/lang/pt.d.ts", "./node_modules/element-plus/es/locale/lang/ro.d.ts", "./node_modules/element-plus/es/locale/lang/ru.d.ts", "./node_modules/element-plus/es/locale/lang/sk.d.ts", "./node_modules/element-plus/es/locale/lang/sl.d.ts", "./node_modules/element-plus/es/locale/lang/sr.d.ts", "./node_modules/element-plus/es/locale/lang/sv.d.ts", "./node_modules/element-plus/es/locale/lang/sw.d.ts", "./node_modules/element-plus/es/locale/lang/ta.d.ts", "./node_modules/element-plus/es/locale/lang/te.d.ts", "./node_modules/element-plus/es/locale/lang/th.d.ts", "./node_modules/element-plus/es/locale/lang/tk.d.ts", "./node_modules/element-plus/es/locale/lang/tr.d.ts", "./node_modules/element-plus/es/locale/lang/ug-cn.d.ts", "./node_modules/element-plus/es/locale/lang/uk.d.ts", "./node_modules/element-plus/es/locale/lang/uz-uz.d.ts", "./node_modules/element-plus/es/locale/lang/vi.d.ts", "./node_modules/element-plus/es/locale/lang/zh-cn.d.ts", "./node_modules/element-plus/es/locale/lang/zh-tw.d.ts", "./node_modules/element-plus/es/locale/lang/zh-hk.d.ts", "./node_modules/element-plus/es/locale/lang/zh-mo.d.ts", "./node_modules/element-plus/es/locale/index.d.ts", "./node_modules/element-plus/es/locales.d.ts", "./node_modules/uuid/dist/esm-browser/types.d.ts", "./node_modules/uuid/dist/esm-browser/max.d.ts", "./node_modules/uuid/dist/esm-browser/nil.d.ts", "./node_modules/uuid/dist/esm-browser/parse.d.ts", "./node_modules/uuid/dist/esm-browser/stringify.d.ts", "./node_modules/uuid/dist/esm-browser/v1.d.ts", "./node_modules/uuid/dist/esm-browser/v1tov6.d.ts", "./node_modules/uuid/dist/esm-browser/v35.d.ts", "./node_modules/uuid/dist/esm-browser/v3.d.ts", "./node_modules/uuid/dist/esm-browser/v4.d.ts", "./node_modules/uuid/dist/esm-browser/v5.d.ts", "./node_modules/uuid/dist/esm-browser/v6.d.ts", "./node_modules/uuid/dist/esm-browser/v6tov1.d.ts", "./node_modules/uuid/dist/esm-browser/v7.d.ts", "./node_modules/uuid/dist/esm-browser/validate.d.ts", "./node_modules/uuid/dist/esm-browser/version.d.ts", "./node_modules/uuid/dist/esm-browser/index.d.ts", "./src/views/ai/index.vue.ts", "./__vls_types.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./env.d.ts", "./src/router/index.ts", "./src/main.ts", "./src/hooks/event/usescrollto.ts", "./src/utils/resultfilter.ts", "./src/utils/virtualscroll.ts", "./node_modules/element-plus/global.d.ts", "./src/app.vue", "./src/views/documentanalysis.vue", "./src/views/ai/index.vue"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0"], "root": [57, [394, 400], [402, 407], [469, 476], [783, 786], 791, 792, 794, [796, 800], 887, 888, [895, 900]], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[48, 50, 51, 56, 58, 85, 401, 901], [56, 58, 85, 401, 894, 901], [52], [86], [87], [86, 87, 88, 89, 90, 91, 92, 93, 94], [56, 58, 85, 401, 901], [97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389], [390], [81], [82, 83], [787], [787, 788], [74], [477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780], [62, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], [62, 63, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], [63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74], [62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74], [62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74], [62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74], [62, 63, 64, 65, 66, 67, 69, 70, 71, 72, 73, 74], [62, 63, 64, 65, 66, 67, 68, 70, 71, 72, 73, 74], [62, 63, 64, 65, 66, 67, 68, 69, 71, 72, 73, 74], [62, 63, 64, 65, 66, 67, 68, 69, 70, 72, 73, 74], [62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 73, 74], [62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 74], [62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73], [46, 52, 53], [54], [46], [46, 47, 48, 50], [47, 48, 49, 50], [77, 78], [77], [75], [60], [59], [49, 56, 58, 61, 74, 76, 79, 80, 82, 84, 85, 95, 401, 901], [801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 865, 866, 867], [868], [56, 58, 85, 96, 401], [419, 420, 422, 431, 432, 433], [419, 421, 422, 423, 424, 425, 426, 428, 429, 430], [409, 411, 412, 413, 417, 418, 419, 420, 432, 434, 435], [411], [415], [409, 410, 436], [411, 436], [414, 416], [423, 424, 425, 426, 427], [419, 431], [419, 422, 432], [408, 409, 419, 431, 434, 435, 436, 437, 438, 439, 440, 441, 442], [408], [409, 419, 420, 422, 431, 432, 443], [432], [432, 450], [431, 432, 448], [432, 448], [419, 421, 432, 443, 446, 448, 451, 456, 457, 461], [432, 459, 460], [458], [448], [459], [431, 432, 448, 461], [433, 446, 447, 448, 449, 451, 452, 453, 454, 456, 457, 459, 462, 463, 464], [419, 421, 431, 432, 448, 449, 451, 452, 458, 461], [448, 449], [419, 420, 431, 455], [419, 422, 431, 432], [465], [56, 58, 77, 85, 901], [870, 871, 872, 873, 874, 875, 876, 878, 879, 880, 881, 882, 883, 884, 885], [870], [870, 877], [893], [889], [890], [891, 892], [56, 58, 85, 401, 431, 443, 444, 901], [445, 467], [443], [56, 58, 85, 401, 431, 444, 466, 901], [50, 55], [50], [51, 396, 397], [51, 397, 799], [51, 96, 392, 395], [51, 56, 58, 85, 401, 901], [51, 56, 58, 85, 96, 391, 398, 401, 901], [51, 56, 58, 85, 96, 391, 401, 901], [51, 56, 58, 85, 96, 391, 396, 401, 402, 901], [51, 56, 58, 85, 96, 391, 397, 401, 402, 404, 407, 468, 469, 470, 901], [51, 56, 58, 85, 96, 391, 401, 405, 406, 901], [51, 56, 58, 85, 96, 391, 397, 398, 401, 901], [51], [51, 784, 785], [51, 393], [51, 56, 57, 58, 85, 96, 391, 401, 791, 894, 896, 901], [51, 85, 476, 887], [51, 56, 58, 85, 398, 401, 901], [51, 394], [51, 96], [51, 56, 58, 85, 401, 790, 901], [51, 405], [51, 443, 469], [51, 96, 395, 782, 783, 786, 789, 791], [51, 56, 58, 85, 401, 781, 782, 783, 786, 792, 793, 901], [51, 56, 58, 85, 391, 401, 901], [51, 56, 58, 85, 391, 401, 795, 901], [51, 56, 58, 85, 395, 401, 781, 786, 792, 794, 796, 797, 798, 800, 869, 886, 895, 901], [51, 56, 58, 85, 96, 391, 399, 400, 401, 402, 403, 404, 471, 472, 473, 474, 475, 901], [894], [56, 58, 85, 401], [49, 56, 58, 61, 74, 76, 79, 80, 82, 84, 85, 95, 401], [56, 58, 77, 85], [892, 893], [48, 50, 51, 56, 58, 85, 401], [891], [51, 56, 58, 85, 96, 391, 401, 791, 895, 897, 902], [51, 85, 903, 904], [56, 58, 85, 401, 895], [51, 56, 58, 85, 398, 401], [51, 56, 58, 85, 401], [51, 56, 58, 85, 401, 790]], "referencedMap": [[888, 1], [895, 2], [53, 3], [94, 4], [92, 4], [91, 5], [87, 4], [95, 6], [93, 5], [89, 5], [90, 5], [97, 7], [98, 7], [99, 7], [100, 7], [101, 7], [102, 7], [103, 7], [104, 7], [105, 7], [106, 7], [107, 7], [108, 7], [109, 7], [110, 7], [111, 7], [112, 7], [113, 7], [114, 7], [115, 7], [116, 7], [117, 7], [118, 7], [119, 7], [120, 7], [121, 7], [122, 7], [123, 7], [124, 7], [125, 7], [126, 7], [127, 7], [128, 7], [129, 7], [130, 7], [131, 7], [132, 7], [133, 7], [134, 7], [135, 7], [136, 7], [137, 7], [138, 7], [139, 7], [140, 7], [141, 7], [142, 7], [143, 7], [144, 7], [145, 7], [146, 7], [147, 7], [148, 7], [149, 7], [150, 7], [151, 7], [152, 7], [153, 7], [154, 7], [155, 7], [156, 7], [157, 7], [158, 7], [159, 7], [160, 7], [161, 7], [162, 7], [163, 7], [164, 7], [165, 7], [166, 7], [167, 7], [168, 7], [169, 7], [170, 7], [171, 7], [172, 7], [173, 7], [174, 7], [175, 7], [176, 7], [177, 7], [178, 7], [179, 7], [180, 7], [181, 7], [182, 7], [183, 7], [184, 7], [185, 7], [186, 7], [187, 7], [188, 7], [189, 7], [190, 7], [191, 7], [192, 7], [193, 7], [194, 7], [195, 7], [196, 7], [197, 7], [198, 7], [199, 7], [200, 7], [201, 7], [202, 7], [203, 7], [204, 7], [205, 7], [206, 7], [207, 7], [208, 7], [209, 7], [210, 7], [211, 7], [212, 7], [213, 7], [214, 7], [215, 7], [216, 7], [217, 7], [218, 7], [219, 7], [220, 7], [221, 7], [222, 7], [223, 7], [224, 7], [225, 7], [226, 7], [227, 7], [228, 7], [229, 7], [230, 7], [231, 7], [232, 7], [233, 7], [234, 7], [235, 7], [236, 7], [237, 7], [238, 7], [390, 8], [239, 7], [240, 7], [241, 7], [242, 7], [243, 7], [244, 7], [245, 7], [246, 7], [247, 7], [248, 7], [249, 7], [250, 7], [251, 7], [252, 7], [253, 7], [254, 7], [255, 7], [256, 7], [257, 7], [258, 7], [259, 7], [260, 7], [261, 7], [262, 7], [263, 7], [264, 7], [265, 7], [266, 7], [267, 7], [268, 7], [269, 7], [270, 7], [271, 7], [272, 7], [273, 7], [274, 7], [275, 7], [276, 7], [277, 7], [278, 7], [279, 7], [280, 7], [281, 7], [282, 7], [283, 7], [284, 7], [285, 7], [286, 7], [287, 7], [288, 7], [289, 7], [290, 7], [291, 7], [292, 7], [293, 7], [294, 7], [295, 7], [296, 7], [297, 7], [298, 7], [299, 7], [300, 7], [301, 7], [302, 7], [303, 7], [304, 7], [305, 7], [306, 7], [307, 7], [308, 7], [309, 7], [310, 7], [311, 7], [312, 7], [313, 7], [314, 7], [315, 7], [316, 7], [317, 7], [318, 7], [319, 7], [320, 7], [321, 7], [322, 7], [323, 7], [324, 7], [325, 7], [326, 7], [327, 7], [328, 7], [329, 7], [330, 7], [331, 7], [332, 7], [333, 7], [334, 7], [335, 7], [336, 7], [337, 7], [338, 7], [339, 7], [340, 7], [341, 7], [342, 7], [343, 7], [344, 7], [345, 7], [346, 7], [347, 7], [348, 7], [349, 7], [350, 7], [351, 7], [352, 7], [353, 7], [354, 7], [355, 7], [356, 7], [357, 7], [358, 7], [359, 7], [360, 7], [361, 7], [362, 7], [363, 7], [364, 7], [365, 7], [366, 7], [367, 7], [368, 7], [369, 7], [370, 7], [371, 7], [372, 7], [373, 7], [374, 7], [375, 7], [376, 7], [377, 7], [378, 7], [379, 7], [380, 7], [381, 7], [382, 7], [383, 7], [384, 7], [385, 7], [386, 7], [387, 7], [388, 7], [389, 7], [391, 9], [82, 10], [84, 11], [788, 12], [789, 13], [477, 14], [478, 14], [479, 14], [480, 14], [481, 14], [482, 14], [483, 14], [484, 14], [485, 14], [486, 14], [487, 14], [488, 14], [489, 14], [490, 14], [491, 14], [492, 14], [493, 14], [494, 14], [495, 14], [496, 14], [497, 14], [498, 14], [499, 14], [500, 14], [501, 14], [502, 14], [503, 14], [504, 14], [505, 14], [506, 14], [507, 14], [508, 14], [509, 14], [510, 14], [511, 14], [512, 14], [513, 14], [514, 14], [515, 14], [516, 14], [517, 14], [518, 14], [519, 14], [520, 14], [521, 14], [522, 14], [523, 14], [524, 14], [525, 14], [526, 14], [527, 14], [528, 14], [529, 14], [530, 14], [531, 14], [532, 14], [533, 14], [534, 14], [535, 14], [536, 14], [537, 14], [538, 14], [539, 14], [540, 14], [541, 14], [542, 14], [543, 14], [544, 14], [545, 14], [546, 14], [547, 14], [548, 14], [549, 14], [550, 14], [551, 14], [552, 14], [553, 14], [554, 14], [555, 14], [556, 14], [557, 14], [558, 14], [559, 14], [560, 14], [561, 14], [562, 14], [563, 14], [564, 14], [565, 14], [566, 14], [567, 14], [568, 14], [569, 14], [570, 14], [571, 14], [572, 14], [573, 14], [781, 15], [574, 14], [575, 14], [576, 14], [577, 14], [578, 14], [579, 14], [580, 14], [581, 14], [582, 14], [583, 14], [584, 14], [585, 14], [586, 14], [587, 14], [588, 14], [589, 14], [590, 14], [591, 14], [592, 14], [593, 14], [594, 14], [595, 14], [596, 14], [597, 14], [598, 14], [599, 14], [600, 14], [601, 14], [602, 14], [603, 14], [604, 14], [605, 14], [606, 14], [607, 14], [608, 14], [609, 14], [610, 14], [611, 14], [612, 14], [613, 14], [614, 14], [615, 14], [616, 14], [617, 14], [618, 14], [619, 14], [620, 14], [621, 14], [622, 14], [623, 14], [624, 14], [625, 14], [626, 14], [627, 14], [628, 14], [629, 14], [630, 14], [631, 14], [632, 14], [633, 14], [634, 14], [635, 14], [636, 14], [637, 14], [638, 14], [639, 14], [640, 14], [641, 14], [642, 14], [643, 14], [644, 14], [645, 14], [646, 14], [647, 14], [648, 14], [649, 14], [650, 14], [651, 14], [652, 14], [653, 14], [654, 14], [655, 14], [656, 14], [657, 14], [658, 14], [659, 14], [660, 14], [661, 14], [662, 14], [663, 14], [664, 14], [665, 14], [666, 14], [667, 14], [668, 14], [669, 14], [670, 14], [671, 14], [672, 14], [673, 14], [674, 14], [675, 14], [676, 14], [677, 14], [678, 14], [679, 14], [680, 14], [681, 14], [682, 14], [683, 14], [684, 14], [685, 14], [686, 14], [687, 14], [688, 14], [689, 14], [690, 14], [691, 14], [692, 14], [693, 14], [694, 14], [695, 14], [696, 14], [697, 14], [698, 14], [699, 14], [700, 14], [701, 14], [702, 14], [703, 14], [704, 14], [705, 14], [706, 14], [707, 14], [708, 14], [709, 14], [710, 14], [711, 14], [712, 14], [713, 14], [714, 14], [715, 14], [716, 14], [717, 14], [718, 14], [719, 14], [720, 14], [721, 14], [722, 14], [723, 14], [724, 14], [725, 14], [726, 14], [727, 14], [728, 14], [729, 14], [730, 14], [731, 14], [732, 14], [733, 14], [734, 14], [735, 14], [736, 14], [737, 14], [738, 14], [739, 14], [740, 14], [741, 14], [742, 14], [743, 14], [744, 14], [745, 14], [746, 14], [747, 14], [748, 14], [749, 14], [750, 14], [751, 14], [752, 14], [753, 14], [754, 14], [755, 14], [756, 14], [757, 14], [758, 14], [759, 14], [760, 14], [761, 14], [762, 14], [763, 14], [764, 14], [765, 14], [766, 14], [767, 14], [768, 14], [769, 14], [770, 14], [771, 14], [772, 14], [773, 14], [774, 14], [775, 14], [776, 14], [777, 14], [778, 14], [779, 14], [780, 14], [63, 16], [64, 17], [62, 18], [65, 19], [66, 20], [67, 21], [68, 22], [69, 23], [70, 24], [71, 25], [72, 26], [73, 27], [74, 28], [54, 29], [55, 30], [47, 31], [48, 32], [50, 33], [79, 34], [78, 35], [76, 36], [61, 37], [60, 38], [96, 39], [868, 40], [869, 41], [58, 7], [901, 42], [434, 43], [431, 44], [436, 45], [414, 46], [416, 47], [411, 48], [412, 49], [413, 46], [417, 50], [418, 46], [409, 49], [428, 51], [440, 52], [442, 53], [443, 54], [430, 55], [446, 56], [447, 57], [451, 58], [432, 52], [450, 57], [449, 59], [454, 60], [452, 60], [462, 61], [461, 62], [463, 63], [458, 64], [464, 65], [460, 66], [465, 67], [459, 68], [455, 69], [456, 70], [457, 71], [466, 72], [401, 73], [886, 74], [875, 75], [878, 76], [877, 75], [879, 75], [880, 76], [881, 75], [883, 75], [894, 77], [890, 78], [891, 79], [893, 80], [77, 7], [445, 81], [468, 82], [444, 83], [467, 84], [85, 7], [56, 85], [51, 86], [398, 87], [800, 88], [404, 87], [798, 87], [396, 89], [57, 90], [399, 91], [400, 92], [403, 93], [471, 94], [407, 95], [472, 96], [784, 97], [786, 98], [785, 97], [898, 97], [394, 99], [897, 100], [896, 101], [473, 102], [402, 90], [474, 90], [397, 97], [469, 97], [395, 103], [475, 104], [791, 105], [406, 106], [470, 107], [405, 97], [799, 89], [899, 97], [792, 108], [783, 97], [900, 97], [794, 109], [797, 110], [795, 97], [796, 111], [887, 112], [476, 113]], "exportedModulesMap": [[888, 1], [895, 114], [53, 3], [94, 4], [92, 4], [91, 5], [87, 4], [95, 6], [93, 5], [89, 5], [90, 5], [97, 115], [98, 115], [99, 115], [100, 115], [101, 115], [102, 115], [103, 115], [104, 115], [105, 115], [106, 115], [107, 115], [108, 115], [109, 115], [110, 115], [111, 115], [112, 115], [113, 115], [114, 115], [115, 115], [116, 115], [117, 115], [118, 115], [119, 115], [120, 115], [121, 115], [122, 115], [123, 115], [124, 115], [125, 115], [126, 115], [127, 115], [128, 115], [129, 115], [130, 115], [131, 115], [132, 115], [133, 115], [134, 115], [135, 115], [136, 115], [137, 115], [138, 115], [139, 115], [140, 115], [141, 115], [142, 115], [143, 115], [144, 115], [145, 115], [146, 115], [147, 115], [148, 115], [149, 115], [150, 115], [151, 115], [152, 115], [153, 115], [154, 115], [155, 115], [156, 115], [157, 115], [158, 115], [159, 115], [160, 115], [161, 115], [162, 115], [163, 115], [164, 115], [165, 115], [166, 115], [167, 115], [168, 115], [169, 115], [170, 115], [171, 115], [172, 115], [173, 115], [174, 115], [175, 115], [176, 115], [177, 115], [178, 115], [179, 115], [180, 115], [181, 115], [182, 115], [183, 115], [184, 115], [185, 115], [186, 115], [187, 115], [188, 115], [189, 115], [190, 115], [191, 115], [192, 115], [193, 115], [194, 115], [195, 115], [196, 115], [197, 115], [198, 115], [199, 115], [200, 115], [201, 115], [202, 115], [203, 115], [204, 115], [205, 115], [206, 115], [207, 115], [208, 115], [209, 115], [210, 115], [211, 115], [212, 115], [213, 115], [214, 115], [215, 115], [216, 115], [217, 115], [218, 115], [219, 115], [220, 115], [221, 115], [222, 115], [223, 115], [224, 115], [225, 115], [226, 115], [227, 115], [228, 115], [229, 115], [230, 115], [231, 115], [232, 115], [233, 115], [234, 115], [235, 115], [236, 115], [237, 115], [238, 115], [390, 8], [239, 115], [240, 115], [241, 115], [242, 115], [243, 115], [244, 115], [245, 115], [246, 115], [247, 115], [248, 115], [249, 115], [250, 115], [251, 115], [252, 115], [253, 115], [254, 115], [255, 115], [256, 115], [257, 115], [258, 115], [259, 115], [260, 115], [261, 115], [262, 115], [263, 115], [264, 115], [265, 115], [266, 115], [267, 115], [268, 115], [269, 115], [270, 115], [271, 115], [272, 115], [273, 115], [274, 115], [275, 115], [276, 115], [277, 115], [278, 115], [279, 115], [280, 115], [281, 115], [282, 115], [283, 115], [284, 115], [285, 115], [286, 115], [287, 115], [288, 115], [289, 115], [290, 115], [291, 115], [292, 115], [293, 115], [294, 115], [295, 115], [296, 115], [297, 115], [298, 115], [299, 115], [300, 115], [301, 115], [302, 115], [303, 115], [304, 115], [305, 115], [306, 115], [307, 115], [308, 115], [309, 115], [310, 115], [311, 115], [312, 115], [313, 115], [314, 115], [315, 115], [316, 115], [317, 115], [318, 115], [319, 115], [320, 115], [321, 115], [322, 115], [323, 115], [324, 115], [325, 115], [326, 115], [327, 115], [328, 115], [329, 115], [330, 115], [331, 115], [332, 115], [333, 115], [334, 115], [335, 115], [336, 115], [337, 115], [338, 115], [339, 115], [340, 115], [341, 115], [342, 115], [343, 115], [344, 115], [345, 115], [346, 115], [347, 115], [348, 115], [349, 115], [350, 115], [351, 115], [352, 115], [353, 115], [354, 115], [355, 115], [356, 115], [357, 115], [358, 115], [359, 115], [360, 115], [361, 115], [362, 115], [363, 115], [364, 115], [365, 115], [366, 115], [367, 115], [368, 115], [369, 115], [370, 115], [371, 115], [372, 115], [373, 115], [374, 115], [375, 115], [376, 115], [377, 115], [378, 115], [379, 115], [380, 115], [381, 115], [382, 115], [383, 115], [384, 115], [385, 115], [386, 115], [387, 115], [388, 115], [389, 115], [391, 9], [82, 10], [84, 11], [788, 12], [789, 13], [477, 14], [478, 14], [479, 14], [480, 14], [481, 14], [482, 14], [483, 14], [484, 14], [485, 14], [486, 14], [487, 14], [488, 14], [489, 14], [490, 14], [491, 14], [492, 14], [493, 14], [494, 14], [495, 14], [496, 14], [497, 14], [498, 14], [499, 14], [500, 14], [501, 14], [502, 14], [503, 14], [504, 14], [505, 14], [506, 14], [507, 14], [508, 14], [509, 14], [510, 14], [511, 14], [512, 14], [513, 14], [514, 14], [515, 14], [516, 14], [517, 14], [518, 14], [519, 14], [520, 14], [521, 14], [522, 14], [523, 14], [524, 14], [525, 14], [526, 14], [527, 14], [528, 14], [529, 14], [530, 14], [531, 14], [532, 14], [533, 14], [534, 14], [535, 14], [536, 14], [537, 14], [538, 14], [539, 14], [540, 14], [541, 14], [542, 14], [543, 14], [544, 14], [545, 14], [546, 14], [547, 14], [548, 14], [549, 14], [550, 14], [551, 14], [552, 14], [553, 14], [554, 14], [555, 14], [556, 14], [557, 14], [558, 14], [559, 14], [560, 14], [561, 14], [562, 14], [563, 14], [564, 14], [565, 14], [566, 14], [567, 14], [568, 14], [569, 14], [570, 14], [571, 14], [572, 14], [573, 14], [781, 15], [574, 14], [575, 14], [576, 14], [577, 14], [578, 14], [579, 14], [580, 14], [581, 14], [582, 14], [583, 14], [584, 14], [585, 14], [586, 14], [587, 14], [588, 14], [589, 14], [590, 14], [591, 14], [592, 14], [593, 14], [594, 14], [595, 14], [596, 14], [597, 14], [598, 14], [599, 14], [600, 14], [601, 14], [602, 14], [603, 14], [604, 14], [605, 14], [606, 14], [607, 14], [608, 14], [609, 14], [610, 14], [611, 14], [612, 14], [613, 14], [614, 14], [615, 14], [616, 14], [617, 14], [618, 14], [619, 14], [620, 14], [621, 14], [622, 14], [623, 14], [624, 14], [625, 14], [626, 14], [627, 14], [628, 14], [629, 14], [630, 14], [631, 14], [632, 14], [633, 14], [634, 14], [635, 14], [636, 14], [637, 14], [638, 14], [639, 14], [640, 14], [641, 14], [642, 14], [643, 14], [644, 14], [645, 14], [646, 14], [647, 14], [648, 14], [649, 14], [650, 14], [651, 14], [652, 14], [653, 14], [654, 14], [655, 14], [656, 14], [657, 14], [658, 14], [659, 14], [660, 14], [661, 14], [662, 14], [663, 14], [664, 14], [665, 14], [666, 14], [667, 14], [668, 14], [669, 14], [670, 14], [671, 14], [672, 14], [673, 14], [674, 14], [675, 14], [676, 14], [677, 14], [678, 14], [679, 14], [680, 14], [681, 14], [682, 14], [683, 14], [684, 14], [685, 14], [686, 14], [687, 14], [688, 14], [689, 14], [690, 14], [691, 14], [692, 14], [693, 14], [694, 14], [695, 14], [696, 14], [697, 14], [698, 14], [699, 14], [700, 14], [701, 14], [702, 14], [703, 14], [704, 14], [705, 14], [706, 14], [707, 14], [708, 14], [709, 14], [710, 14], [711, 14], [712, 14], [713, 14], [714, 14], [715, 14], [716, 14], [717, 14], [718, 14], [719, 14], [720, 14], [721, 14], [722, 14], [723, 14], [724, 14], [725, 14], [726, 14], [727, 14], [728, 14], [729, 14], [730, 14], [731, 14], [732, 14], [733, 14], [734, 14], [735, 14], [736, 14], [737, 14], [738, 14], [739, 14], [740, 14], [741, 14], [742, 14], [743, 14], [744, 14], [745, 14], [746, 14], [747, 14], [748, 14], [749, 14], [750, 14], [751, 14], [752, 14], [753, 14], [754, 14], [755, 14], [756, 14], [757, 14], [758, 14], [759, 14], [760, 14], [761, 14], [762, 14], [763, 14], [764, 14], [765, 14], [766, 14], [767, 14], [768, 14], [769, 14], [770, 14], [771, 14], [772, 14], [773, 14], [774, 14], [775, 14], [776, 14], [777, 14], [778, 14], [779, 14], [780, 14], [63, 16], [64, 17], [62, 18], [65, 19], [66, 20], [67, 21], [68, 22], [69, 23], [70, 24], [71, 25], [72, 26], [73, 27], [74, 28], [54, 29], [55, 30], [47, 31], [48, 32], [50, 33], [79, 34], [78, 35], [76, 36], [61, 37], [60, 38], [96, 116], [868, 40], [869, 41], [58, 115], [901, 97], [434, 43], [431, 44], [436, 45], [414, 46], [416, 47], [411, 48], [412, 49], [413, 46], [417, 50], [418, 46], [409, 49], [428, 51], [440, 52], [442, 53], [443, 54], [430, 55], [446, 56], [447, 57], [451, 58], [432, 52], [450, 57], [449, 59], [454, 60], [452, 60], [462, 61], [461, 62], [463, 63], [458, 64], [464, 65], [460, 66], [465, 67], [459, 68], [455, 69], [456, 70], [457, 71], [466, 72], [401, 117], [886, 74], [875, 75], [878, 76], [877, 75], [879, 75], [880, 76], [881, 75], [883, 75], [894, 118], [889, 119], [891, 79], [892, 120], [77, 115], [445, 81], [468, 82], [444, 83], [467, 84], [85, 115], [56, 85], [51, 86], [398, 87], [800, 88], [404, 87], [798, 87], [396, 89], [57, 90], [399, 91], [400, 92], [403, 93], [471, 94], [407, 95], [472, 96], [784, 97], [786, 98], [785, 97], [898, 121], [394, 99], [897, 122], [896, 123], [473, 124], [402, 125], [474, 125], [397, 97], [469, 97], [395, 103], [475, 104], [791, 126], [406, 106], [470, 107], [405, 97], [799, 89], [899, 97], [792, 108], [783, 97], [900, 97], [794, 109], [797, 110], [795, 97], [796, 111], [887, 112], [476, 113]], "semanticDiagnosticsPerFile": [888, 895, 53, 52, 94, 88, 92, 91, 87, 86, 95, 93, 89, 90, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 390, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 391, 82, 84, 81, 83, 788, 789, 787, 427, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 781, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 693, 694, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 714, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 63, 64, 62, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 54, 55, 47, 48, 50, 46, 79, 78, 76, 75, 392, 49, 61, 60, 59, 96, 868, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 866, 867, 865, 869, 58, 901, 80, 790, 782, 434, 422, 431, 423, 424, 419, 435, 436, 437, 414, 416, 415, 411, 412, 413, 417, 418, 410, 409, 425, 429, 428, 421, 426, 438, 440, 441, 439, 442, 443, 430, 408, 446, 447, 448, 451, 432, 450, 449, 454, 452, 462, 461, 463, 458, 464, 460, 465, 459, 433, 420, 455, 456, 453, 457, 466, 401, 793, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 886, 871, 872, 873, 874, 870, 875, 876, 878, 877, 879, 880, 881, 882, 883, 884, 885, 894, 890, 889, 891, 892, 893, 77, 445, 468, 444, 467, 85, 56, 51, 393, 398, 800, [404, [{"file": "./src/api/document.ts", "start": 2995, "length": 15, "messageText": "Cannot find name 'DocumentContent'.", "category": 1, "code": 2304}, {"file": "./src/api/document.ts", "start": 3155, "length": 15, "messageText": "Cannot find name 'DocumentContent'.", "category": 1, "code": 2304}, {"file": "./src/api/document.ts", "start": 3313, "length": 15, "messageText": "Cannot find name 'DocumentContent'.", "category": 1, "code": 2304}]], [798, [{"file": "./src/api/maxkb.ts", "start": 3846, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<AnalysisStatus>'."}, {"file": "./src/api/maxkb.ts", "start": 4143, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<MaxKBAnalysisResult>'."}, {"file": "./src/api/maxkb.ts", "start": 5033, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<{ documentId: number; status: string; message: string; }>'."}]], [396, [{"file": "./src/api/request.ts", "start": 833, "length": 33, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '(config: AxiosRequestConfig) => AxiosRequestConfig<any>' is not assignable to parameter of type '(value: InternalAxiosRequestConfig<any>) => InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'AxiosRequestConfig<any>' is not assignable to type 'InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'AxiosRequestConfig<any>' is not assignable to type 'InternalAxiosRequestConfig<any>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'headers' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'AxiosHeaders | (Partial<RawAxiosHeaders & { Accept: AxiosHeaderValue; \"Content-Length\": AxiosHeaderValue; \"User-Agent\": AxiosHeaderValue; \"Content-Encoding\": AxiosHeaderValue; Authorization: AxiosHeaderValue; } & { ...; }> & Partial<...>) | undefined' is not assignable to type 'AxiosRequestHeaders'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'AxiosRequestHeaders'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'undefined' is not assignable to type 'Partial<RawAxiosHeaders & { Accept: AxiosHeaderValue; \"Content-Length\": AxiosHeaderValue; \"User-Agent\": AxiosHeaderValue; \"Content-Encoding\": AxiosHeaderValue; Authorization: AxiosHeaderValue; } & { ...; }>'.", "category": 1, "code": 2322}]}]}]}]}]}]}}]], 57, [399, [{"file": "./src/components/analysisresults.vue", "start": 10112, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<{ documentId: number; status: string; isProcessing: boolean; isCompleted: boolean; }>'."}, {"file": "./src/components/analysisresults.vue", "start": 10889, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<{ documentId: number; status: string; message: string; }>'."}, {"file": "./src/components/analysisresults.vue", "start": 11466, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<{ documentId: number; status: string; isProcessing: boolean; isCompleted: boolean; }>'."}, {"file": "./src/components/analysisresults.vue", "start": 12277, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<{ data: AnalysisResult[]; count: number; }>'."}, {"file": "./src/components/analysisresults.vue", "start": 13895, "length": 16, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 3, '(evt: \"item-click\", item: ResultItem): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"results-loaded\"' is not assignable to parameter of type '\"item-click\"'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 3, '(evt: \"locate-request\", item: ResultItem): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"results-loaded\"' is not assignable to parameter of type '\"locate-request\"'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 3 of 3, '(evt: \"item-select\", item: ResultItem | null): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"results-loaded\"' is not assignable to parameter of type '\"item-select\"'.", "category": 1, "code": 2345}]}]}, "relatedInformation": []}, {"file": "./src/components/analysisresults.vue", "start": 938, "length": 0, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(tab: string) => void' is not assignable to type '(name: TabPaneName) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'tab' and 'name' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'TabPaneName' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}]], [400, [{"file": "./src/components/documentnavigator.vue", "start": 1934, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(show: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'show' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}]], [403, [{"file": "./src/components/documentupload.vue", "start": 3563, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'AxiosResponse<any, any>'."}, {"file": "./src/components/documentupload.vue", "start": 5232, "length": 42, "messageText": "Expected 3 arguments, but got 2.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 1326705, "length": 24, "messageText": "An argument for 'uploadFiles' was not provided.", "category": 3, "code": 6210}]}, {"file": "./src/components/documentupload.vue", "start": 6030, "length": 7, "messageText": "Expected 1 arguments, but got 0.", "category": 1, "code": 2554, "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 398158, "length": 16, "messageText": "An argument for 'file' was not provided.", "category": 3, "code": 6210}]}]], [471, [{"file": "./src/components/documentviewer.vue", "start": 9299, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<DocumentContent>'."}, {"file": "./src/components/documentviewer.vue", "start": 9462, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'pdfAvailable' does not exist on type '{ documentId: number; originalName: string; htmlContent: string; textContent: string; cssStyles: string; metadata: { [x: string]: any; title?: string | undefined; creator?: string | undefined; description?: string | undefined; paragraphCount?: number | undefined; tableCount?: number | undefined; characterCount?: num...'."}, {"file": "./src/components/documentviewer.vue", "start": 630, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'pdfAvailable' does not exist on type '{ documentId: number; originalName: string; htmlContent: string; textContent: string; cssStyles: string; metadata: { [x: string]: any; title?: string | undefined; creator?: string | undefined; description?: string | undefined; paragraphCount?: number | undefined; tableCount?: number | undefined; characterCount?: num...'."}, {"file": "./src/components/documentviewer.vue", "start": 3200, "length": 8, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(progress: number) => void' is not assignable to type '(value: OnProgressParameters) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'progress' and 'value' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'OnProgressParameters' is not assignable to type 'number'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}]], [407, [{"file": "./src/components/highlightinteraction.vue", "start": 4823, "length": 81, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof HTMLElementEventMap, listener: (this: HTMLElement, ev: Event | MouseEvent | UIEvent | FocusEvent | CompositionEvent | ... 13 more ... | WheelEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"highlight-click\"' is not assignable to parameter of type 'keyof HTMLElementEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(event: CustomEvent) => void' is not assignable to type 'EventListener'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'event' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Event' is missing the following properties from type 'CustomEvent<any>': detail, initCustomEvent", "category": 1, "code": 2739}]}]}]}]}]}, "relatedInformation": []}, {"file": "./src/components/highlightinteraction.vue", "start": 8065, "length": 84, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof HTMLElementEventMap, listener: (this: HTMLElement, ev: Event | MouseEvent | UIEvent | FocusEvent | CompositionEvent | ... 13 more ... | WheelEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"highlight-click\"' is not assignable to parameter of type 'keyof HTMLElementEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345}]}]}, "relatedInformation": []}, {"file": "./src/components/highlightinteraction.vue", "start": 1305, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(value: number) => void' is not assignable to type '(value: Arrayable<number>) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'value' and 'value' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Arrayable<number>' is not assignable to type 'number'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'number[]' is not assignable to type 'number'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"file": "./src/components/highlightinteraction.vue", "start": 1519, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(enabled: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'enabled' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}, {"file": "./src/components/highlightinteraction.vue", "start": 1732, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(enabled: boolean) => void' is not assignable to type '(val: string | number | boolean) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'enabled' and 'val' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'string | number | boolean' is not assignable to type 'boolean'.", "category": 1, "code": 2322}]}]}, "relatedInformation": []}]], [472, [{"file": "./src/components/structuredanalysisresults.vue", "start": 21033, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<StructuredAnalysisResult>'."}, {"file": "./src/components/structuredanalysisresults.vue", "start": 23574, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<{ documentId: number; status: string; message: string; }>'."}, {"file": "./src/components/structuredanalysisresults.vue", "start": 24131, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<{ documentId: number; status: string; isProcessing: boolean; isCompleted: boolean; }>'."}, {"file": "./src/components/structuredanalysisresults.vue", "start": 1567, "length": 0, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(tab: string) => void' is not assignable to type '(name: TabPaneName) => any'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'tab' and 'name' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'TabPaneName' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"file": "./src/components/structuredanalysisresults.vue", "start": 3493, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"primary\" | \"success\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 319704, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"primary\" | \"success\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/components/structuredanalysisresults.vue", "start": 7321, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"primary\" | \"success\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 319704, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"primary\" | \"success\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/components/structuredanalysisresults.vue", "start": 10818, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"primary\" | \"success\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 319704, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"primary\" | \"success\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/components/structuredanalysisresults.vue", "start": 10992, "length": 10, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'importance' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'importance' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 7 more ...; type: strin...'.", "category": 1, "code": 2339}]}}, {"file": "./src/components/structuredanalysisresults.vue", "start": 13997, "length": 6, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'result' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'result' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339}]}}, {"file": "./src/components/structuredanalysisresults.vue", "start": 14082, "length": 6, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'result' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'result' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339}]}}, {"file": "./src/components/structuredanalysisresults.vue", "start": 14136, "length": 5, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'EpPropMergeType<StringConstructor, \"primary\" | \"success\" | \"warning\" | \"info\" | \"danger\", unknown> | undefined'.", "relatedInformation": [{"file": "./node_modules/element-plus/es/index.d.ts", "start": 319704, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Partial<{ readonly round: boolean; readonly type: EpPropMergeType<StringConstructor, \"primary\" | \"success\" | \"warning\" | \"info\" | \"danger\", unknown>; readonly effect: EpPropMergeType<...>; readonly closable: boolean; readonly disableTransitions: boolean; readonly hit: boolean; }> & Omit<...> & Record<...>'", "category": 3, "code": 6500}]}, {"file": "./src/components/structuredanalysisresults.vue", "start": 14172, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'severity' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'severity' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339}]}}, {"file": "./src/components/structuredanalysisresults.vue", "start": 14215, "length": 8, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'severity' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'severity' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339}]}}, {"file": "./src/components/structuredanalysisresults.vue", "start": 14385, "length": 6, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'reason' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'reason' does not exist on type '{ id: string; title: string; sources: { referenceIndex: string; location: string; content: string; position?: { page?: number | undefined; paragraph?: number | undefined; startOffset?: number | undefined; endOffset?: number | undefined; keywords?: string[] | undefined; } | undefined; }[]; ... 5 more ...; type: strin...'.", "category": 1, "code": 2339}]}}]], 784, 786, 785, [898, [{"file": "./src/hooks/event/usescrollto.ts", "start": 405, "length": 12, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type 'HTMLElement'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type 'HTMLElement'.", "category": 1, "code": 7054}]}}, {"file": "./src/hooks/event/usescrollto.ts", "start": 581, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/hooks/event/usescrollto.ts", "start": 609, "length": 12, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type 'HTMLElement'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type 'HTMLElement'.", "category": 1, "code": 7054}]}}, {"file": "./src/hooks/event/usescrollto.ts", "start": 740, "length": 5, "messageText": "Cannot find name 'unref'.", "category": 1, "code": 2304}, {"file": "./src/hooks/event/usescrollto.ts", "start": 946, "length": 5, "messageText": "Cannot find name 'unref'.", "category": 1, "code": 2304}]], 394, 897, 896, 473, 402, 474, 397, 469, 395, 475, [791, [{"file": "./src/utils/eventhub.ts", "start": 527, "length": 7, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'import(\"D:/git-zj/zj-server-hfggzy/frontend/node_modules/mitt/index\").Emitter<Events>' is not assignable to type 'Emitter<Events>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'all' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'EventHandlerMap<Events>' is not assignable to type 'Map<string, EventHandler<any>[]>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'keyof Events' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}]}}, {"file": "./src/utils/eventhub.ts", "start": 559, "length": 6, "code": 2344, "category": 1, "messageText": {"messageText": "Type 'Events' does not satisfy the constraint 'Record<EventType, unknown>'.", "category": 1, "code": 2344, "next": [{"messageText": "Index signature for type 'symbol' is missing in type 'Events'.", "category": 1, "code": 2329}]}}]], 406, 470, 405, [799, [{"file": "./src/utils/request-chat.ts", "start": 891, "length": 33, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '(config: AxiosRequestConfig) => AxiosRequestConfig<any>' is not assignable to parameter of type '(value: InternalAxiosRequestConfig<any>) => InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'AxiosRequestConfig<any>' is not assignable to type 'InternalAxiosRequestConfig<any> | Promise<InternalAxiosRequestConfig<any>>'.", "category": 1, "code": 2322}]}}, {"file": "./src/utils/request-chat.ts", "start": 1109, "length": 10, "messageText": "Cannot find name 'Recordable'.", "category": 1, "code": 2304}]], 899, [792, [{"file": "./src/utils/sse.ts", "start": 445, "length": 10, "messageText": "Parameter 'dateString' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/utils/sse.ts", "start": 806, "length": 9, "messageText": "Parameter 'timestamp' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/utils/sse.ts", "start": 1089, "length": 5, "messageText": "Parameter 'date1' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/utils/sse.ts", "start": 1096, "length": 5, "messageText": "Parameter 'date2' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/utils/sse.ts", "start": 2825, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}, {"file": "./src/utils/sse.ts", "start": 3100, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 3440, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 4720, "length": 9, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | null' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"file": "./src/utils/sse.ts", "start": 4849, "length": 7, "code": 2739, "category": 1, "messageText": "Type '{ code: number; data: { name: string; avatar: string; is_available: boolean; bot_biz_id: string; }; }' is missing the following properties from type 'BotInfo': name, avatar, is_available, bot_biz_id"}, {"file": "./src/utils/sse.ts", "start": 5178, "length": 16, "messageText": "'cache.configInfo' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/utils/sse.ts", "start": 5739, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 5919, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 5936, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 6120, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 6140, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'content' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 7017, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(response: Response) => void' is not assignable to type '(response: Response) => Promise<void>'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'void' is not assignable to type 'Promise<void>'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"file": "./node_modules/@microsoft/fetch-event-source/lib/cjs/fetch.d.ts", "start": 219, "length": 6, "messageText": "The expected type comes from property 'onopen' which is declared here on type 'FetchEventSourceInit'", "category": 3, "code": 6500}]}, {"file": "./src/utils/sse.ts", "start": 7138, "length": 9, "code": 2322, "category": 1, "messageText": {"messageText": "Type '(rsp: MessageEvent) => void' is not assignable to type '(ev: EventSourceMessage) => void'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'rsp' and 'ev' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'EventSourceMessage' is missing the following properties from type 'MessageEvent<any>': lastEventId, origin, ports, source, and 23 more.", "category": 1, "code": 2740}]}]}, "relatedInformation": [{"file": "./node_modules/@microsoft/fetch-event-source/lib/cjs/fetch.d.ts", "start": 272, "length": 9, "messageText": "The expected type comes from property 'onmessage' which is declared here on type 'FetchEventSourceInit'", "category": 3, "code": 6500}]}, {"file": "./src/utils/sse.ts", "start": 7475, "length": 5, "messageText": "'cache' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/utils/sse.ts", "start": 8411, "length": 9, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'string | null' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"file": "./src/utils/sse.ts", "start": 8473, "length": 5, "messageText": "'cache' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/utils/sse.ts", "start": 8899, "length": 6, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'failed' does not exist in type 'ChatMessage | ChatMessage[]'."}, {"file": "./src/utils/sse.ts", "start": 10292, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'chat_record_id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 10694, "length": 6, "messageText": "'answer' is possibly 'undefined'.", "category": 1, "code": 18048}, {"file": "./src/utils/sse.ts", "start": 10751, "length": 15, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'loading_message' does not exist in type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 11040, "length": 8, "messageText": "Property 'transfer' does not exist on type 'ChatMessage'.", "category": 1, "code": 2339}, {"file": "./src/utils/sse.ts", "start": 11050, "length": 4, "messageText": "Property 'quit' does not exist on type 'ChatMessage'.", "category": 1, "code": 2339}, {"file": "./src/utils/sse.ts", "start": 11056, "length": 13, "messageText": "Property 'transferRobot' does not exist on type 'ChatMessage'.", "category": 1, "code": 2339}, {"file": "./src/utils/sse.ts", "start": 11229, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 11293, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 11592, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'is_evil' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 12261, "length": 15, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'loading_message' does not exist in type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 12519, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'loading_message' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 14790, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 14844, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 15376, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}, {"file": "./src/utils/sse.ts", "start": 15613, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type 'ChatMessage'."}]], [783, [{"file": "./src/utils/util.ts", "start": 35, "length": 6, "messageText": "Parameter 'origin' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/utils/util.ts", "start": 107, "length": 12, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{}'.", "category": 1, "code": 7054}]}}, {"file": "./src/utils/util.ts", "start": 139, "length": 12, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{}'.", "category": 1, "code": 7054}]}}, {"file": "./src/utils/util.ts", "start": 221, "length": 12, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{}'.", "category": 1, "code": 7054}]}}, {"file": "./src/utils/util.ts", "start": 297, "length": 12, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{}'.", "category": 1, "code": 7054}]}}, {"file": "./src/utils/util.ts", "start": 347, "length": 12, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{}'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{}'.", "category": 1, "code": 7054}]}}, {"file": "./src/utils/util.ts", "start": 530, "length": 15, "messageText": "Cannot find name 'getCurrentPages'.", "category": 1, "code": 2304}, {"file": "./src/utils/util.ts", "start": 2356, "length": 12, "messageText": "Type 'T' is generic and can only be indexed for reading.", "category": 1, "code": 2862}]], [900, [{"file": "./src/utils/virtualscroll.ts", "start": 6329, "length": 21, "code": 2415, "category": 1, "messageText": {"messageText": "Class 'DocumentVirtualScroll' incorrectly extends base class 'VirtualScrollManager'.", "category": 1, "code": 2415, "next": [{"messageText": "Property 'createElement' is private in type 'VirtualScrollManager' but not in type 'DocumentVirtualScroll'.", "category": 1, "code": 2325}]}}, {"file": "./src/utils/virtualscroll.ts", "start": 7167, "length": 10, "messageText": "Property 'itemHeight' is private and only accessible within class 'VirtualScrollManager'.", "category": 1, "code": 2341}, {"file": "./src/utils/virtualscroll.ts", "start": 7231, "length": 5, "messageText": "Property 'items' is private and only accessible within class 'VirtualScrollManager'.", "category": 1, "code": 2341}, {"file": "./src/utils/virtualscroll.ts", "start": 7254, "length": 11, "messageText": "Property 'totalHeight' is private and only accessible within class 'VirtualScrollManager'.", "category": 1, "code": 2341}, {"file": "./src/utils/virtualscroll.ts", "start": 7327, "length": 9, "messageText": "Property 'container' is private and only accessible within class 'VirtualScrollManager'.", "category": 1, "code": 2341}, {"file": "./src/utils/virtualscroll.ts", "start": 7360, "length": 11, "messageText": "Property 'totalHeight' is private and only accessible within class 'VirtualScrollManager'.", "category": 1, "code": 2341}, {"file": "./src/utils/virtualscroll.ts", "start": 7390, "length": 18, "messageText": "Property 'updateVisibleItems' is private and only accessible within class 'VirtualScrollManager'.", "category": 1, "code": 2341}]], [794, [{"file": "./src/views/ai/components/client-chat.vue", "start": 3462, "length": 13, "code": 7016, "category": 1, "messageText": {"messageText": "Could not find a declaration file for module 'markdown-it'. 'D:/git-zj/zj-server-hfggzy/frontend/node_modules/markdown-it/index.mjs' implicitly has an 'any' type.", "category": 1, "code": 7016, "next": [{"info": {"moduleReference": "markdown-it"}}]}}, {"file": "./src/views/ai/components/client-chat.vue", "start": 3545, "length": 25, "code": 7016, "category": 1, "messageText": {"messageText": "Could not find a declaration file for module 'element-resize-detector'. 'D:/git-zj/zj-server-hfggzy/frontend/node_modules/element-resize-detector/src/element-resize-detector.js' implicitly has an 'any' type.", "category": 1, "code": 7016, "next": [{"info": {"moduleReference": "element-resize-detector"}}]}}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4071, "length": 6, "messageText": "Parameter 'tokens' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4079, "length": 3, "messageText": "Parameter 'idx' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4084, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4093, "length": 3, "messageText": "Parameter 'env' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4098, "length": 4, "messageText": "Parameter 'self' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4203, "length": 6, "messageText": "Parameter 'tokens' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4211, "length": 3, "messageText": "Parameter 'idx' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4216, "length": 7, "messageText": "Parameter 'options' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4225, "length": 3, "messageText": "Parameter 'env' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4230, "length": 4, "messageText": "Parameter 'self' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4546, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4581, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4614, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4666, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4699, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4752, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4786, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4814, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 4846, "length": 3, "messageText": "Cannot find name 'ref'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5012, "length": 9, "messageText": "Parameter 'timestamp' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5154, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5176, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5224, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5256, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5437, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5459, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5507, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5701, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5723, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5771, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 5947, "length": 7, "messageText": "Parameter 'content' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 6021, "length": 2, "messageText": "Parameter 'el' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 6025, "length": 8, "messageText": "Parameter 'recordId' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 6141, "length": 2, "messageText": "Parameter 'el' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 6145, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 6427, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 7571, "length": 6, "messageText": "Parameter 'chatId' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 7791, "length": 6, "messageText": "Parameter 'chatId' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 7799, "length": 4, "messageText": "Parameter 'text' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 8273, "length": 3, "messageText": "Parameter 'msg' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 8278, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 8897, "length": 8, "messageText": "'eventHub' is of type 'unknown'.", "category": 1, "code": 18046}, {"file": "./src/views/ai/components/client-chat.vue", "start": 8936, "length": 3, "messageText": "Parameter 'res' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 9135, "length": 8, "messageText": "'eventHub' is of type 'unknown'.", "category": 1, "code": 18046}, {"file": "./src/views/ai/components/client-chat.vue", "start": 9216, "length": 8, "messageText": "'eventHub' is of type 'unknown'.", "category": 1, "code": 18046}, {"file": "./src/views/ai/components/client-chat.vue", "start": 9339, "length": 4, "messageText": "Parameter 'data' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 9345, "length": 4, "messageText": "Parameter 'type' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 9585, "length": 2, "messageText": "Parameter 'el' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 9773, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 10351, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 10374, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 10436, "length": 12, "messageText": "'scrollbarRef' is possibly 'null'.", "category": 1, "code": 18047}, {"file": "./src/views/ai/components/client-chat.vue", "start": 10707, "length": 5, "messageText": "Cannot find name 'watch'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 10739, "length": 7, "messageText": "Parameter 'newList' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 10748, "length": 7, "messageText": "Parameter 'oldList' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 11107, "length": 9, "messageText": "Cannot find name 'onMounted'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 11286, "length": 7, "messageText": "Parameter 'element' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/components/client-chat.vue", "start": 11457, "length": 11, "messageText": "Cannot find name 'onUnmounted'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/components/client-chat.vue", "start": 11493, "length": 8, "messageText": "'eventHub' is of type 'unknown'.", "category": 1, "code": 18046}, {"file": "./src/views/ai/components/client-chat.vue", "start": 11536, "length": 8, "messageText": "'eventHub' is of type 'unknown'.", "category": 1, "code": 18046}, {"file": "./src/views/ai/components/client-chat.vue", "start": 11568, "length": 8, "messageText": "'eventHub' is of type 'unknown'.", "category": 1, "code": 18046}]], [797, [{"file": "./src/views/ai/components/history.vue", "start": 2067, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}, {"file": "./src/views/ai/components/history.vue", "start": 2984, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'abstract' does not exist on type '{ name: string; key: string; data: { problem: string; answer: string; }[]; }'."}, {"file": "./src/views/ai/components/history.vue", "start": 3056, "length": 4, "code": 2345, "category": 1, "messageText": "Argument of type '{ name: string; key: string; data: { problem: string; answer: string; }[]; }' is not assignable to parameter of type 'ProblemItem'."}, {"file": "./src/views/ai/components/history.vue", "start": 3545, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'abstract' does not exist on type 'ProblemItem'."}, {"file": "./src/views/ai/components/history.vue", "start": 4367, "length": 35, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(evt: \"selectProblem\", item: ProblemItem): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '{ name: string; key: string; data: { problem: string; answer: string; }[]; }' is not assignable to parameter of type 'ProblemItem'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ name: string; key: string; data: { problem: string; answer: string; }[]; }' is missing the following properties from type 'ProblemItem': problem, answer", "category": 1, "code": 2739}]}]}, {"messageText": "Overload 2 of 2, '(evt: \"newChat\", ...args: \"\"): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"selectProblem\"' is not assignable to parameter of type '\"newChat\"'.", "category": 1, "code": 2345}]}]}, "relatedInformation": []}, {"file": "./src/views/ai/components/history.vue", "start": 4599, "length": 15, "code": 2345, "category": 1, "messageText": "Argument of type '[]' is not assignable to parameter of type '\"\"'."}, {"file": "./src/views/ai/components/history.vue", "start": 4894, "length": 16, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'unknown[]' is not assignable to type 'Category[] | { name: string; key: string; data: { problem: string; answer: string; }[]; }[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'unknown[]' is not assignable to type 'Category[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'unknown' is not assignable to type 'Category'.", "category": 1, "code": 2322}]}]}}, {"file": "./src/views/ai/components/history.vue", "start": 891, "length": 4, "code": 2345, "category": 1, "messageText": "Argument of type '{ name: string; key: string; data: { problem: string; answer: string; }[]; }' is not assignable to parameter of type 'ProblemItem'."}, {"file": "./src/views/ai/components/history.vue", "start": 1091, "length": 4, "code": 2345, "category": 1, "messageText": "Argument of type '{ name: string; key: string; data: { problem: string; answer: string; }[]; }' is not assignable to parameter of type 'ProblemItem'."}, {"file": "./src/views/ai/components/history.vue", "start": 1179, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'abstract' does not exist on type '{ name: string; key: string; data: { problem: string; answer: string; }[]; }'."}]], 795, [796, [{"file": "./src/views/ai/components/problem.vue", "start": 1935, "length": 6, "messageText": "Cannot find namespace 'NodeJS'.", "category": 1, "code": 2503}]], [887, [{"file": "./src/views/ai/index.vue", "start": 2949, "length": 6, "messageText": "Cannot find name 'inject'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/index.vue", "start": 3970, "length": 8, "messageText": "Cannot find name 'nextTick'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/index.vue", "start": 4460, "length": 4, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'ApiResponse<string>' is not assignable to parameter of type 'TokenType'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'ApiResponse<string>' is missing the following properties from type 'TokenType': id, accessToken, refreshToken, userId, and 3 more.", "category": 1, "code": 2740}]}}, {"file": "./src/views/ai/index.vue", "start": 6388, "length": 4, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; chat_id: string | null; problem_text: any; answer_text: any; update_time: string; timestamp: number; isTyped: boolean; is_loading: boolean; is_history: boolean; }' is not assignable to parameter of type 'ChatMessage | ChatMessage[]'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ id: string; chat_id: string | null; problem_text: any; answer_text: any; update_time: string; timestamp: number; isTyped: boolean; is_loading: boolean; is_history: boolean; }' is not assignable to type 'ChatMessage'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of property 'chat_id' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string | null' is not assignable to type 'string'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}]}]}]}}, {"file": "./src/views/ai/index.vue", "start": 7168, "length": 3, "messageText": "Parameter 'res' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/index.vue", "start": 7288, "length": 3, "messageText": "Parameter 'res' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"file": "./src/views/ai/index.vue", "start": 7497, "length": 8, "messageText": "Cannot find name 'nextTick'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/index.vue", "start": 7850, "length": 2, "code": 2345, "category": 1, "messageText": "Argument of type 'string' is not assignable to parameter of type 'number'."}, {"file": "./src/views/ai/index.vue", "start": 7915, "length": 14, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'any[]' is not assignable to type 'never[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'any' is not assignable to type 'never'.", "category": 1, "code": 2322}]}}, {"file": "./src/views/ai/index.vue", "start": 8318, "length": 8, "messageText": "Cannot find name 'nextTick'.", "category": 1, "code": 2304}, {"file": "./src/views/ai/index.vue", "start": 8833, "length": 11, "messageText": "Cannot find name 'onUnmounted'.", "category": 1, "code": 2304}]], [476, [{"file": "./src/views/documentanalysis.vue", "start": 12062, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'totalCount' does not exist on type 'Store<\"analysis\", Pick<{ currentDocumentId: Ref<number | null, number | null>; analysisResults: Ref<{ id: number; documentId: number; agentType: string; resultContent: Record<string, any>; processingTime: number; apiResponseCode: number; createdTime: string; }[], AnalysisResult[] | { ...; }[]>; ... 29 more ...; proc...'."}, {"file": "./src/views/documentanalysis.vue", "start": 13155, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'success' does not exist on type 'ApiResponse<DocumentContent>'."}, {"file": "./src/views/documentanalysis.vue", "start": 15890, "length": 76, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof WindowEventMap, listener: (this: Window, ev: Event | MouseEvent | UIEvent | FocusEvent | CompositionEvent | ... 24 more ... | StorageEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"highlight-result-item\"' is not assignable to parameter of type 'keyof WindowEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(event: CustomEvent) => void' is not assignable to type 'EventListener'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'event' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Event' is not assignable to type 'CustomEvent<any>'.", "category": 1, "code": 2322}]}]}]}]}]}, "relatedInformation": []}, {"file": "./src/views/documentanalysis.vue", "start": 15997, "length": 79, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof WindowEventMap, listener: (this: Window, ev: Event | MouseEvent | UIEvent | FocusEvent | CompositionEvent | ... 24 more ... | StorageEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"highlight-result-item\"' is not assignable to parameter of type 'keyof WindowEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345}]}]}, "relatedInformation": []}, {"file": "./src/views/documentanalysis.vue", "start": 1251, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'totalCount' does not exist on type 'Store<\"analysis\", Pick<{ currentDocumentId: Ref<number | null, number | null>; analysisResults: Ref<{ id: number; documentId: number; agentType: string; resultContent: Record<string, any>; processingTime: number; apiResponseCode: number; createdTime: string; }[], AnalysisResult[] | { ...; }[]>; ... 29 more ...; proc...'."}]]], "affectedFilesPendingEmit": [398, 800, 404, 798, 396, 57, 399, 400, 403, 471, 407, 472, 784, 786, 785, 898, 394, 897, 896, 473, 402, 474, 397, 469, 395, 475, 791, 406, 470, 405, 799, 899, 792, 783, 900, 794, 797, 796, 887, 476], "emitSignatures": [57, 394, 395, 396, 397, 398, 399, 400, 402, 403, 404, 405, 406, 407, 469, 470, 471, 472, 473, 474, 475, 476, 783, 784, 785, 786, 791, 792, 794, 796, 797, 798, 799, 800, 887, 897, 898, 899, 900]}, "version": "5.3.3"}