// 前端 JavaScript 示例：如何使用修改后的 getPdfFile API

/**
 * 获取PDF文件并处理为ArrayBuffer
 * @param {number} documentId - 文档ID
 * @returns {Promise<ArrayBuffer>} PDF文件的ArrayBuffer
 */
async function getPdfAsArrayBuffer(documentId) {
    try {
        const response = await fetch(`/documents/${documentId}/pdf`, {
            method: 'GET',
            headers: {
                'Accept': 'application/pdf',
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 获取响应头中的文件信息
        const contentType = response.headers.get('Content-Type');
        const contentLength = response.headers.get('Content-Length');
        const contentDisposition = response.headers.get('Content-Disposition');
        
        console.log('PDF文件信息:', {
            contentType,
            contentLength: contentLength ? `${contentLength} bytes` : 'unknown',
            contentDisposition
        });

        // 将响应转换为ArrayBuffer
        const arrayBuffer = await response.arrayBuffer();
        
        console.log('PDF文件加载完成:', {
            size: arrayBuffer.byteLength,
            type: 'ArrayBuffer'
        });

        return arrayBuffer;

    } catch (error) {
        console.error('获取PDF文件失败:', error);
        throw error;
    }
}

/**
 * 获取PDF文件并创建Blob URL用于预览
 * @param {number} documentId - 文档ID
 * @returns {Promise<string>} Blob URL
 */
async function getPdfBlobUrl(documentId) {
    try {
        const arrayBuffer = await getPdfAsArrayBuffer(documentId);
        
        // 创建Blob对象
        const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
        
        // 创建Blob URL
        const blobUrl = URL.createObjectURL(blob);
        
        console.log('PDF Blob URL创建成功:', blobUrl);
        
        return blobUrl;

    } catch (error) {
        console.error('创建PDF Blob URL失败:', error);
        throw error;
    }
}

/**
 * 在新窗口中预览PDF
 * @param {number} documentId - 文档ID
 */
async function previewPdfInNewWindow(documentId) {
    try {
        const blobUrl = await getPdfBlobUrl(documentId);
        
        // 在新窗口中打开PDF
        const newWindow = window.open(blobUrl, '_blank');
        
        if (!newWindow) {
            alert('无法打开新窗口，请检查浏览器弹窗设置');
            return;
        }

        // 可选：在窗口关闭时清理Blob URL
        newWindow.addEventListener('beforeunload', () => {
            URL.revokeObjectURL(blobUrl);
        });

    } catch (error) {
        console.error('预览PDF失败:', error);
        alert('预览PDF失败: ' + error.message);
    }
}

/**
 * 下载PDF文件
 * @param {number} documentId - 文档ID
 * @param {string} fileName - 可选的文件名
 */
async function downloadPdf(documentId, fileName = null) {
    try {
        const arrayBuffer = await getPdfAsArrayBuffer(documentId);
        
        // 创建Blob对象
        const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
        
        // 创建下载链接
        const downloadUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = fileName || `document_${documentId}.pdf`;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 清理Blob URL
        URL.revokeObjectURL(downloadUrl);
        
        console.log('PDF下载完成');

    } catch (error) {
        console.error('下载PDF失败:', error);
        alert('下载PDF失败: ' + error.message);
    }
}

/**
 * 在iframe中嵌入显示PDF
 * @param {number} documentId - 文档ID
 * @param {string} iframeId - iframe元素的ID
 */
async function embedPdfInIframe(documentId, iframeId) {
    try {
        const blobUrl = await getPdfBlobUrl(documentId);
        
        const iframe = document.getElementById(iframeId);
        if (!iframe) {
            throw new Error(`找不到ID为 ${iframeId} 的iframe元素`);
        }
        
        iframe.src = blobUrl;
        
        console.log('PDF已嵌入到iframe中');

    } catch (error) {
        console.error('嵌入PDF失败:', error);
        alert('嵌入PDF失败: ' + error.message);
    }
}

/**
 * 使用PDF.js库渲染PDF（需要先引入PDF.js）
 * @param {number} documentId - 文档ID
 * @param {string} canvasId - canvas元素的ID
 */
async function renderPdfWithPdfJs(documentId, canvasId) {
    try {
        // 检查PDF.js是否可用
        if (typeof pdfjsLib === 'undefined') {
            throw new Error('PDF.js库未加载，请先引入PDF.js');
        }

        const arrayBuffer = await getPdfAsArrayBuffer(documentId);
        
        // 加载PDF文档
        const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
        
        // 获取第一页
        const page = await pdf.getPage(1);
        
        // 设置渲染参数
        const scale = 1.5;
        const viewport = page.getViewport({ scale });
        
        // 获取canvas元素
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            throw new Error(`找不到ID为 ${canvasId} 的canvas元素`);
        }
        
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        
        // 渲染页面
        const renderContext = {
            canvasContext: context,
            viewport: viewport
        };
        
        await page.render(renderContext).promise;
        
        console.log('PDF渲染完成');

    } catch (error) {
        console.error('渲染PDF失败:', error);
        alert('渲染PDF失败: ' + error.message);
    }
}

// 使用示例
/*
// 预览PDF
document.getElementById('previewBtn').addEventListener('click', () => {
    previewPdfInNewWindow(123);
});

// 下载PDF
document.getElementById('downloadBtn').addEventListener('click', () => {
    downloadPdf(123, 'my-document.pdf');
});

// 在iframe中显示PDF
document.getElementById('embedBtn').addEventListener('click', () => {
    embedPdfInIframe(123, 'pdfIframe');
});

// 使用PDF.js渲染
document.getElementById('renderBtn').addEventListener('click', () => {
    renderPdfWithPdfJs(123, 'pdfCanvas');
});
*/
