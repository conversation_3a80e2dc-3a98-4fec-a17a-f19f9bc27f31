
# 查看更多 .gitignore 配置 -> https://help.github.com/articles/ignoring-files/


target/
!.mvn/wrapper/maven-wrapper.jar

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
*.class
target/*
*.7z
dist
dist-ssr
/dist*

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
/build/



### admin-web ###

# dependencies
**/node_modules

# roadhog-api-doc ignore
/src/utils/request-temp.js
_roadhog-api-doc

# production
/dist
/.vscode

# misc
.DS_Store
npm-debug.log*
yarn-error.log

/coverage
.idea
yarn.lock
package-lock.json
*bak
.vscode

# visual studio code
.history
*.log

functions/mock
.temp/**

# umi
.umi
.umi-production

# screenshot
screenshot
.firebase
sessionStore

.flattened-pom.xml
/zj-framework/zj-spring-boot-starter-banner/.flattened-pom.xml

./frontend/node_modules
./frontend/dist
./frontend/.vscode
./frontend/.idea
./frontend/.history
./frontend/*.log
./frontend/*.bak
./frontend/*.tmp
./frontend/*.swp
./frontend/*.swo
./frontend/*.sublime-workspace
./frontend/*.sublime-project
./frontend/*.iml
./frontend/*.ipr
./frontend/*.iws
./frontend/*.class
./frontend/*.7z

kkFileViewer/**
backend/src/main/cache/**
backend/LibreOfficePortable/**