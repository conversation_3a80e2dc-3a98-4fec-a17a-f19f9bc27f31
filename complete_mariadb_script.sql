-- ================================================================================================
-- 文档智能分析系统 - 完整 MariaDB 数据库脚本
-- Document Intelligence Analysis System - Complete MariaDB Database Script
-- ================================================================================================
-- Version: 1.0.0
-- Created: 2025-08-12
-- Compatible: MariaDB 10.5+ / MySQL 8.0+
-- Character Set: utf8mb4
-- Collation: utf8mb4_unicode_ci
-- ================================================================================================

-- 设置字符集和SQL模式
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- ================================================================================================
-- 第一部分：数据库和用户创建
-- ================================================================================================

-- 创建数据库
DROP DATABASE IF EXISTS doc_analysis;
CREATE DATABASE doc_analysis 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci
COMMENT '文档智能分析系统数据库';

-- 使用数据库
USE doc_analysis;

-- 创建数据库用户（可选，根据需要调整）
-- CREATE USER IF NOT EXISTS 'doc_user'@'%' IDENTIFIED BY 'DocAnalysis@2025';
-- GRANT ALL PRIVILEGES ON doc_analysis.* TO 'doc_user'@'%';
-- FLUSH PRIVILEGES;

-- ================================================================================================
-- 第二部分：核心表结构创建
-- ================================================================================================

-- 1. 文档表：存储上传文档的基本信息
CREATE TABLE documents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文档ID，主键自增',
    filename VARCHAR(255) NOT NULL COMMENT '存储文件名（系统生成的唯一文件名）',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名（用户上传时的文件名）',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_hash VARCHAR(64) DEFAULT NULL COMMENT '文件MD5哈希值',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    status ENUM('UPLOADED', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'UPLOADED' COMMENT '文档处理状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
    deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标记：0-未删除，1-已删除',
    
    -- 索引
    INDEX idx_documents_upload_time (upload_time),
    INDEX idx_documents_status (status),
    INDEX idx_documents_file_size (file_size),
    INDEX idx_documents_status_upload_time (status, upload_time DESC),
    INDEX idx_documents_original_name (original_name),
    INDEX idx_documents_created_time (created_time),
    INDEX idx_documents_updated_time (updated_time),
    INDEX idx_documents_deleted (deleted),
    UNIQUE KEY uk_documents_filename (filename)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='文档基本信息表';

-- 2. 分析结果表：存储MaxKB智能体的分析结果
CREATE TABLE analysis_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分析结果ID，主键自增',
    document_id BIGINT NOT NULL COMMENT '关联的文档ID',
    agent_type ENUM('EXTRACT', 'DETECT') NOT NULL COMMENT '智能体类型：EXTRACT-提取智能体，DETECT-检测智能体',
    result_content JSON NOT NULL COMMENT '分析结果内容（JSON格式）',
    processing_time INT DEFAULT NULL COMMENT '处理耗时（毫秒）',
    api_response_code INT DEFAULT NULL COMMENT 'API响应状态码',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分析完成时间',
    
    -- 外键约束
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- 索引
    INDEX idx_analysis_document_id (document_id),
    INDEX idx_analysis_agent_type (agent_type),
    INDEX idx_analysis_created_time (created_time),
    INDEX idx_analysis_processing_time (processing_time),
    INDEX idx_analysis_api_response_code (api_response_code),
    INDEX idx_analysis_document_agent (document_id, agent_type),
    INDEX idx_analysis_agent_time (agent_type, created_time DESC)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='AI分析结果表';

-- 为JSON字段创建虚拟列和索引
ALTER TABLE analysis_results 
ADD COLUMN result_status VARCHAR(20) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(result_content, '$.status'))) VIRTUAL,
ADD INDEX idx_analysis_result_status (result_status);

-- 3. 用户交互记录表：记录用户与系统的交互行为
CREATE TABLE user_interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '交互记录ID，主键自增',
    document_id BIGINT NOT NULL COMMENT '关联的文档ID',
    interaction_type VARCHAR(50) NOT NULL COMMENT '交互类型：highlight-高亮，click-点击，search-搜索等',
    content TEXT COMMENT '交互内容描述',
    position_info JSON COMMENT '位置信息（JSON格式，包含坐标、页码等）',
    session_id VARCHAR(100) DEFAULT NULL COMMENT '用户会话ID',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理信息',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '用户IP地址',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '交互发生时间',
    
    -- 外键约束
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- 索引
    INDEX idx_interactions_document_id (document_id),
    INDEX idx_interactions_type (interaction_type),
    INDEX idx_interactions_document_time (document_id, created_time DESC),
    INDEX idx_interactions_session_id (session_id),
    INDEX idx_interactions_created_time (created_time),
    INDEX idx_interactions_ip_address (ip_address),
    INDEX idx_interactions_type_time (interaction_type, created_time DESC)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='用户交互记录表';

-- 4. 分析任务状态表：跟踪MaxKB智能体分析任务的实时状态
CREATE TABLE analysis_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    document_id BIGINT NOT NULL COMMENT '关联文档ID',
    agent_type ENUM('EXTRACT', 'DETECT') NOT NULL COMMENT '智能体类型',
    task_status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    
    -- MaxKB相关信息
    chat_id VARCHAR(255) COMMENT 'MaxKB会话ID',
    file_id VARCHAR(255) COMMENT 'MaxKB文件ID',
    
    -- 时间信息
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    started_time TIMESTAMP NULL COMMENT '开始处理时间',
    completed_time TIMESTAMP NULL COMMENT '完成时间',
    
    -- 进度和状态信息
    progress_percentage INT DEFAULT 0 COMMENT '进度百分比(0-100)',
    status_message VARCHAR(500) COMMENT '状态描述信息',
    error_message TEXT COMMENT '错误信息',
    
    -- 性能统计
    processing_duration_ms BIGINT COMMENT '处理耗时(毫秒)',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    
    -- 结果信息
    result_id BIGINT COMMENT '关联的分析结果ID',
    
    -- 索引
    INDEX idx_document_agent (document_id, agent_type),
    INDEX idx_status (task_status),
    INDEX idx_created_time (created_time),
    
    -- 外键约束
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES analysis_results(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析任务状态跟踪表';

-- 5. 系统配置表：存储系统运行时配置
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT '配置类型',
    description VARCHAR(255) DEFAULT NULL COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_config_type (config_type),
    INDEX idx_config_active (is_active),
    INDEX idx_config_created_time (created_time),
    INDEX idx_config_updated_time (updated_time)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='系统配置表';

-- 6. 操作日志表：记录重要的系统操作
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(255) DEFAULT NULL COMMENT '操作描述',
    document_id BIGINT DEFAULT NULL COMMENT '关联文档ID（如果有）',
    request_params JSON DEFAULT NULL COMMENT '请求参数（JSON格式）',
    response_data JSON DEFAULT NULL COMMENT '响应数据（JSON格式）',
    execution_time INT DEFAULT NULL COMMENT '执行耗时（毫秒）',
    status ENUM('SUCCESS', 'FAILED', 'PENDING') DEFAULT 'SUCCESS' COMMENT '执行状态',
    error_message TEXT DEFAULT NULL COMMENT '错误信息（如果有）',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    
    -- 外键约束
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL ON UPDATE CASCADE,
    
    -- 索引
    INDEX idx_logs_operation_type (operation_type),
    INDEX idx_logs_document_id (document_id),
    INDEX idx_logs_status (status),
    INDEX idx_logs_created_time (created_time),
    INDEX idx_logs_execution_time (execution_time),
    INDEX idx_logs_type_time (operation_type, created_time DESC),
    INDEX idx_logs_ip_address (ip_address)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='操作日志表';

-- ================================================================================================
-- 第三部分：视图创建
-- ================================================================================================

-- 1. 分析任务进度视图
CREATE VIEW v_analysis_task_progress AS
SELECT
    at.id as task_id,
    at.document_id,
    d.original_name as document_name,
    at.agent_type,
    at.task_status,
    at.progress_percentage,
    at.status_message,
    at.created_time,
    at.started_time,
    at.completed_time,
    at.processing_duration_ms,
    CASE
        WHEN at.task_status = 'COMPLETED' THEN '已完成'
        WHEN at.task_status = 'PROCESSING' THEN '处理中'
        WHEN at.task_status = 'PENDING' THEN '等待中'
        WHEN at.task_status = 'FAILED' THEN '失败'
        ELSE '未知'
    END as status_display,
    CASE
        WHEN at.agent_type = 'EXTRACT' THEN '数据提取'
        WHEN at.agent_type = 'DETECT' THEN '异常检测'
        ELSE '未知类型'
    END as agent_display
FROM analysis_tasks at
LEFT JOIN documents d ON at.document_id = d.id;

-- 2. 文档分析概览视图
CREATE VIEW v_document_analysis_overview AS
SELECT
    d.id as document_id,
    d.original_name,
    d.file_size,
    d.upload_time,
    d.status as document_status,

    -- 提取任务状态
    extract_task.task_status as extract_status,
    extract_task.progress_percentage as extract_progress,
    extract_task.completed_time as extract_completed_time,

    -- 检测任务状态
    detect_task.task_status as detect_status,
    detect_task.progress_percentage as detect_progress,
    detect_task.completed_time as detect_completed_time,

    -- 分析结果统计
    COALESCE(result_stats.extract_count, 0) as extract_result_count,
    COALESCE(result_stats.detect_count, 0) as detect_result_count,
    COALESCE(result_stats.total_processing_time, 0) as total_processing_time,

    -- 用户交互统计
    COALESCE(interaction_stats.interaction_count, 0) as interaction_count,
    interaction_stats.last_interaction_time

FROM documents d
LEFT JOIN analysis_tasks extract_task ON d.id = extract_task.document_id AND extract_task.agent_type = 'EXTRACT'
LEFT JOIN analysis_tasks detect_task ON d.id = detect_task.document_id AND detect_task.agent_type = 'DETECT'
LEFT JOIN (
    SELECT
        document_id,
        SUM(CASE WHEN agent_type = 'EXTRACT' THEN 1 ELSE 0 END) as extract_count,
        SUM(CASE WHEN agent_type = 'DETECT' THEN 1 ELSE 0 END) as detect_count,
        SUM(COALESCE(processing_time, 0)) as total_processing_time
    FROM analysis_results
    GROUP BY document_id
) result_stats ON d.id = result_stats.document_id
LEFT JOIN (
    SELECT
        document_id,
        COUNT(*) as interaction_count,
        MAX(created_time) as last_interaction_time
    FROM user_interactions
    GROUP BY document_id
) interaction_stats ON d.id = interaction_stats.document_id
WHERE d.deleted = 0;

-- 3. 数据库状态监控视图
CREATE VIEW v_database_status AS
SELECT
    'documents' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 'UPLOADED' THEN 1 END) as uploaded_count,
    COUNT(CASE WHEN status = 'PROCESSING' THEN 1 END) as processing_count,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_count,
    COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_count,
    ROUND(AVG(file_size)/1024/1024, 2) as avg_file_size_mb,
    MAX(upload_time) as latest_upload
FROM documents WHERE deleted = 0

UNION ALL

SELECT
    'analysis_results' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN agent_type = 'EXTRACT' THEN 1 END) as extract_count,
    COUNT(CASE WHEN agent_type = 'DETECT' THEN 1 END) as detect_count,
    0 as completed_count,
    0 as failed_count,
    ROUND(AVG(processing_time), 2) as avg_processing_time,
    MAX(created_time) as latest_analysis
FROM analysis_results

UNION ALL

SELECT
    'user_interactions' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT document_id) as unique_documents,
    COUNT(DISTINCT session_id) as unique_sessions,
    0 as completed_count,
    0 as failed_count,
    0 as avg_metric,
    MAX(created_time) as latest_interaction
FROM user_interactions;

-- 4. 系统健康状况视图
CREATE VIEW v_system_health AS
SELECT
    'Database Health Check' as check_name,
    CASE
        WHEN (SELECT COUNT(*) FROM documents WHERE deleted = 0) > 0 THEN 'HEALTHY'
        ELSE 'WARNING'
    END as status,
    CONCAT('Total Documents: ', (SELECT COUNT(*) FROM documents WHERE deleted = 0)) as details,
    NOW() as check_time

UNION ALL

SELECT
    'Analysis Performance' as check_name,
    CASE
        WHEN (SELECT AVG(processing_time) FROM analysis_results WHERE processing_time IS NOT NULL) < 30000 THEN 'HEALTHY'
        WHEN (SELECT AVG(processing_time) FROM analysis_results WHERE processing_time IS NOT NULL) < 60000 THEN 'WARNING'
        ELSE 'CRITICAL'
    END as status,
    CONCAT('Avg Processing Time: ',
           ROUND((SELECT AVG(processing_time) FROM analysis_results WHERE processing_time IS NOT NULL)/1000, 2),
           's') as details,
    NOW() as check_time

UNION ALL

SELECT
    'Storage Usage' as check_name,
    CASE
        WHEN (SELECT SUM(file_size) FROM documents WHERE deleted = 0) < 10737418240 THEN 'HEALTHY'  -- 10GB
        WHEN (SELECT SUM(file_size) FROM documents WHERE deleted = 0) < 53687091200 THEN 'WARNING'  -- 50GB
        ELSE 'CRITICAL'
    END as status,
    CONCAT('Total Storage: ',
           ROUND((SELECT SUM(file_size) FROM documents WHERE deleted = 0)/1024/1024/1024, 2),
           'GB') as details,
    NOW() as check_time;

-- ================================================================================================
-- 第四部分：存储过程创建
-- ================================================================================================

DELIMITER //

-- 1. 清理过期数据存储过程
CREATE PROCEDURE CleanupExpiredData(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 记录清理操作
    INSERT INTO operation_logs (operation_type, operation_desc, request_params, status)
    VALUES ('DATA_CLEANUP', '清理过期数据', JSON_OBJECT('days_to_keep', days_to_keep), 'PENDING');

    SET @log_id = LAST_INSERT_ID();

    -- 清理过期的用户交互记录
    DELETE FROM user_interactions
    WHERE created_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);

    SET @deleted_interactions = ROW_COUNT();

    -- 清理过期的操作日志
    DELETE FROM operation_logs
    WHERE created_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND id != @log_id;

    SET @deleted_logs = ROW_COUNT();

    -- 更新清理操作记录
    UPDATE operation_logs
    SET status = 'SUCCESS',
        response_data = JSON_OBJECT(
            'deleted_interactions', @deleted_interactions,
            'deleted_logs', @deleted_logs
        ),
        execution_time = TIMESTAMPDIFF(MICROSECOND, created_time, NOW()) / 1000
    WHERE id = @log_id;

    COMMIT;

    SELECT
        'Data cleanup completed' as message,
        @deleted_interactions as deleted_interactions,
        @deleted_logs as deleted_logs;

END //

-- 2. 文档分析统计存储过程
CREATE PROCEDURE GetDocumentAnalysisStats(IN document_id_param BIGINT)
BEGIN
    SELECT
        d.id,
        d.original_name,
        d.file_size,
        d.upload_time,
        d.status,

        -- 分析结果统计
        COUNT(ar.id) as total_analysis_count,
        COUNT(CASE WHEN ar.agent_type = 'EXTRACT' THEN 1 END) as extract_count,
        COUNT(CASE WHEN ar.agent_type = 'DETECT' THEN 1 END) as detect_count,
        AVG(ar.processing_time) as avg_processing_time,

        -- 用户交互统计
        COUNT(ui.id) as interaction_count,
        COUNT(DISTINCT ui.session_id) as unique_sessions,
        MAX(ui.created_time) as last_interaction_time

    FROM documents d
    LEFT JOIN analysis_results ar ON d.id = ar.document_id
    LEFT JOIN user_interactions ui ON d.id = ui.document_id
    WHERE d.id = document_id_param AND d.deleted = 0
    GROUP BY d.id, d.original_name, d.file_size, d.upload_time, d.status;

END //

-- 3. 批量更新文档状态存储过程
CREATE PROCEDURE BatchUpdateDocumentStatus(
    IN document_ids TEXT,
    IN new_status VARCHAR(20)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 记录批量更新操作
    INSERT INTO operation_logs (operation_type, operation_desc, request_params, status)
    VALUES ('BATCH_UPDATE_STATUS', '批量更新文档状态',
            JSON_OBJECT('document_ids', document_ids, 'new_status', new_status), 'PENDING');

    SET @log_id = LAST_INSERT_ID();

    -- 执行批量更新
    SET @sql = CONCAT('UPDATE documents SET status = ''', new_status,
                     ''' WHERE id IN (', document_ids, ') AND deleted = 0');

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET @updated_count = ROW_COUNT();

    -- 更新操作记录
    UPDATE operation_logs
    SET status = 'SUCCESS',
        response_data = JSON_OBJECT('updated_count', @updated_count),
        execution_time = TIMESTAMPDIFF(MICROSECOND, created_time, NOW()) / 1000
    WHERE id = @log_id;

    COMMIT;

    SELECT
        'Batch update completed' as message,
        @updated_count as updated_count;

END //

DELIMITER ;

-- ================================================================================================
-- 第五部分：触发器创建
-- ================================================================================================

-- 1. 文档状态变更触发器
DELIMITER //

CREATE TRIGGER tr_document_status_change
    AFTER UPDATE ON documents
    FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO operation_logs (
            operation_type,
            operation_desc,
            document_id,
            request_params,
            status
        ) VALUES (
            'DOCUMENT_STATUS_CHANGE',
            CONCAT('文档状态从 ', OLD.status, ' 变更为 ', NEW.status),
            NEW.id,
            JSON_OBJECT('old_status', OLD.status, 'new_status', NEW.status),
            'SUCCESS'
        );
    END IF;
END //

-- 2. 分析结果插入触发器
CREATE TRIGGER tr_analysis_result_insert
    AFTER INSERT ON analysis_results
    FOR EACH ROW
BEGIN
    INSERT INTO operation_logs (
        operation_type,
        operation_desc,
        document_id,
        request_params,
        status
    ) VALUES (
        'ANALYSIS_RESULT_CREATED',
        CONCAT('创建', NEW.agent_type, '类型分析结果'),
        NEW.document_id,
        JSON_OBJECT(
            'analysis_id', NEW.id,
            'agent_type', NEW.agent_type,
            'processing_time', NEW.processing_time
        ),
        'SUCCESS'
    );
END //

DELIMITER ;

-- ================================================================================================
-- 第六部分：初始配置数据
-- ================================================================================================

-- 插入系统配置数据
INSERT INTO system_config (config_key, config_value, config_type, description, is_active) VALUES
('database_version', '1.0.0', 'STRING', '数据库版本号', TRUE),
('database_initialized', 'true', 'BOOLEAN', '数据库是否已初始化', TRUE),
('max_file_size', '52428800', 'NUMBER', '最大文件上传大小（字节）50MB', TRUE),
('allowed_file_types', '["pdf", "doc", "docx", "txt", "xlsx", "xls"]', 'JSON', '允许上传的文件类型', TRUE),
('maxkb_api_timeout', '30000', 'NUMBER', 'MaxKB API超时时间（毫秒）', TRUE),
('maxkb_api_retry_count', '3', 'NUMBER', 'MaxKB API重试次数', TRUE),
('analysis_batch_size', '10', 'NUMBER', '批量分析处理大小', TRUE),
('cleanup_retention_days', '90', 'NUMBER', '数据保留天数', TRUE),
('enable_user_tracking', 'true', 'BOOLEAN', '是否启用用户行为跟踪', TRUE),
('system_maintenance_mode', 'false', 'BOOLEAN', '系统维护模式', FALSE),
('log_level', 'INFO', 'STRING', '系统日志级别', TRUE),
('cache_expiry_minutes', '60', 'NUMBER', '缓存过期时间（分钟）', TRUE),
('concurrent_analysis_limit', '5', 'NUMBER', '并发分析任务限制', TRUE),
('storage_path', '/app/uploads', 'STRING', '文件存储路径', TRUE),
('backup_enabled', 'true', 'BOOLEAN', '是否启用自动备份', TRUE);

-- ================================================================================================
-- 第七部分：示例数据（可选）
-- ================================================================================================

-- 插入示例文档数据
INSERT INTO documents (filename, original_name, file_path, file_size, file_hash, status) VALUES
('doc_20250812_001.pdf', '财务报告2024.pdf', '/uploads/2025/08/doc_20250812_001.pdf', 2048576, 'a1b2c3d4e5f6789012345678901234567890abcd', 'COMPLETED'),
('doc_20250812_002.docx', '项目计划书.docx', '/uploads/2025/08/doc_20250812_002.docx', 1536000, 'b2c3d4e5f6789012345678901234567890abcde1', 'PROCESSING'),
('doc_20250812_003.xlsx', '数据分析表.xlsx', '/uploads/2025/08/doc_20250812_003.xlsx', 512000, 'c3d4e5f6789012345678901234567890abcdef12', 'UPLOADED'),
('doc_20250812_004.txt', '会议纪要.txt', '/uploads/2025/08/doc_20250812_004.txt', 102400, 'd4e5f6789012345678901234567890abcdef123', 'COMPLETED'),
('doc_20250812_005.pdf', '技术文档.pdf', '/uploads/2025/08/doc_20250812_005.pdf', 3072000, 'e5f6789012345678901234567890abcdef1234', 'FAILED');

-- 插入示例分析结果数据
INSERT INTO analysis_results (document_id, agent_type, result_content, processing_time, api_response_code) VALUES
(1, 'EXTRACT', JSON_OBJECT(
    'status', 'success',
    'data', JSON_OBJECT(
        'content', JSON_ARRAY(
            JSON_OBJECT('type', 'paragraph', 'text', '2024年度财务总结', 'confidence', 0.95),
            JSON_OBJECT('type', 'table', 'text', '收入支出明细表', 'confidence', 0.88)
        )
    ),
    'timestamp', '2025-08-12T10:30:00Z'
), 15000, 200),

(1, 'DETECT', JSON_OBJECT(
    'status', 'success',
    'data', JSON_OBJECT(
        'issues', JSON_ARRAY(
            JSON_OBJECT('type', 'data_inconsistency', 'description', '数据不一致', 'severity', 'medium'),
            JSON_OBJECT('type', 'missing_signature', 'description', '缺少签名', 'severity', 'high')
        )
    ),
    'timestamp', '2025-08-12T10:35:00Z'
), 12000, 200),

(4, 'EXTRACT', JSON_OBJECT(
    'status', 'success',
    'data', JSON_OBJECT(
        'content', JSON_ARRAY(
            JSON_OBJECT('type', 'paragraph', 'text', '会议时间：2025年8月12日', 'confidence', 0.99),
            JSON_OBJECT('type', 'list', 'text', '参会人员名单', 'confidence', 0.92)
        )
    ),
    'timestamp', '2025-08-12T11:00:00Z'
), 8000, 200);

-- 插入示例分析任务数据
INSERT INTO analysis_tasks (document_id, agent_type, task_status, chat_id, file_id, progress_percentage, status_message, processing_duration_ms, result_id) VALUES
(1, 'EXTRACT', 'COMPLETED', 'chat_001', 'file_001', 100, '提取完成', 15000, 1),
(1, 'DETECT', 'COMPLETED', 'chat_002', 'file_001', 100, '检测完成', 12000, 2),
(2, 'EXTRACT', 'PROCESSING', 'chat_003', 'file_002', 65, '正在处理中...', NULL, NULL),
(3, 'EXTRACT', 'PENDING', NULL, NULL, 0, '等待处理', NULL, NULL),
(4, 'EXTRACT', 'COMPLETED', 'chat_004', 'file_004', 100, '提取完成', 8000, 3),
(5, 'EXTRACT', 'FAILED', 'chat_005', 'file_005', 0, '处理失败：文件格式不支持', NULL, NULL);

-- 插入示例用户交互数据
INSERT INTO user_interactions (document_id, interaction_type, content, position_info, session_id, ip_address) VALUES
(1, 'highlight', '用户高亮了财务数据部分', JSON_OBJECT('page', 1, 'start', 100, 'end', 200), 'session_001', '*************'),
(1, 'click', '用户点击了图表区域', JSON_OBJECT('page', 2, 'x', 300, 'y', 150), 'session_001', '*************'),
(1, 'search', '用户搜索关键词：收入', JSON_OBJECT('keyword', '收入', 'results_count', 5), 'session_001', '*************'),
(4, 'highlight', '用户高亮了会议时间', JSON_OBJECT('page', 1, 'start', 50, 'end', 80), 'session_002', '*************'),
(4, 'click', '用户点击了参会人员列表', JSON_OBJECT('page', 1, 'x', 200, 'y', 300), 'session_002', '*************');

-- ================================================================================================
-- 第八部分：权限和安全设置
-- ================================================================================================

-- 创建只读用户（可选）
-- CREATE USER IF NOT EXISTS 'doc_readonly'@'%' IDENTIFIED BY 'ReadOnly@2025';
-- GRANT SELECT ON doc_analysis.* TO 'doc_readonly'@'%';

-- 创建应用用户（可选）
-- CREATE USER IF NOT EXISTS 'doc_app'@'%' IDENTIFIED BY 'DocApp@2025';
-- GRANT SELECT, INSERT, UPDATE ON doc_analysis.documents TO 'doc_app'@'%';
-- GRANT SELECT, INSERT, UPDATE ON doc_analysis.analysis_results TO 'doc_app'@'%';
-- GRANT SELECT, INSERT, UPDATE ON doc_analysis.user_interactions TO 'doc_app'@'%';
-- GRANT SELECT, INSERT, UPDATE ON doc_analysis.analysis_tasks TO 'doc_app'@'%';
-- GRANT SELECT ON doc_analysis.system_config TO 'doc_app'@'%';
-- GRANT INSERT ON doc_analysis.operation_logs TO 'doc_app'@'%';

-- FLUSH PRIVILEGES;

-- ================================================================================================
-- 第九部分：数据库验证和测试
-- ================================================================================================

-- 验证表结构
SELECT
    TABLE_NAME,
    ENGINE,
    TABLE_COLLATION,
    TABLE_COMMENT,
    TABLE_ROWS
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'doc_analysis'
ORDER BY TABLE_NAME;

-- 验证索引
SELECT
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    INDEX_TYPE
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'doc_analysis'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 验证外键约束
SELECT
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'doc_analysis'
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 验证视图
SELECT
    TABLE_NAME as VIEW_NAME,
    VIEW_DEFINITION
FROM information_schema.VIEWS
WHERE TABLE_SCHEMA = 'doc_analysis';

-- 验证存储过程
SELECT
    ROUTINE_NAME,
    ROUTINE_TYPE,
    ROUTINE_COMMENT
FROM information_schema.ROUTINES
WHERE ROUTINE_SCHEMA = 'doc_analysis';

-- 验证触发器
SELECT
    TRIGGER_NAME,
    EVENT_MANIPULATION,
    EVENT_OBJECT_TABLE,
    ACTION_TIMING
FROM information_schema.TRIGGERS
WHERE TRIGGER_SCHEMA = 'doc_analysis';

-- 基本数据验证
SELECT 'Documents Count' as metric, COUNT(*) as value FROM documents
UNION ALL
SELECT 'Analysis Results Count', COUNT(*) FROM analysis_results
UNION ALL
SELECT 'User Interactions Count', COUNT(*) FROM user_interactions
UNION ALL
SELECT 'Analysis Tasks Count', COUNT(*) FROM analysis_tasks
UNION ALL
SELECT 'System Config Count', COUNT(*) FROM system_config
UNION ALL
SELECT 'Operation Logs Count', COUNT(*) FROM operation_logs;

-- 测试视图
SELECT '=== 测试分析任务进度视图 ===' as test_section;
SELECT * FROM v_analysis_task_progress LIMIT 5;

SELECT '=== 测试文档分析概览视图 ===' as test_section;
SELECT * FROM v_document_analysis_overview LIMIT 5;

SELECT '=== 测试数据库状态视图 ===' as test_section;
SELECT * FROM v_database_status;

SELECT '=== 测试系统健康视图 ===' as test_section;
SELECT * FROM v_system_health;

-- ================================================================================================
-- 第十部分：完成和清理
-- ================================================================================================

-- 提交事务
COMMIT;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT
    '数据库初始化完成' as status,
    DATABASE() as database_name,
    NOW() as completion_time,
    VERSION() as mariadb_version,
    @@character_set_database as charset,
    @@collation_database as collation;

-- ================================================================================================
-- 使用说明和维护建议
-- ================================================================================================

/*
=== 使用说明 ===

1. 执行脚本：
   mysql -u root -p < complete_mariadb_script.sql

2. 连接数据库：
   mysql -u root -p doc_analysis

3. 基本查询示例：
   -- 查看所有文档
   SELECT * FROM documents WHERE deleted = 0;

   -- 查看分析进度
   SELECT * FROM v_analysis_task_progress;

   -- 查看系统状态
   SELECT * FROM v_system_health;

4. 维护操作：
   -- 清理90天前的数据
   CALL CleanupExpiredData(90);

   -- 获取文档统计
   CALL GetDocumentAnalysisStats(1);

   -- 批量更新状态
   CALL BatchUpdateDocumentStatus('1,2,3', 'COMPLETED');

=== 性能优化建议 ===

1. 定期维护：
   - 每周执行 ANALYZE TABLE 更新统计信息
   - 每月执行 OPTIMIZE TABLE 优化表结构
   - 定期清理过期数据

2. 监控指标：
   - 查询响应时间
   - 存储空间使用
   - 索引使用效率
   - 并发连接数

3. 备份策略：
   - 每日增量备份
   - 每周完整备份
   - 重要操作前手动备份

=== 安全建议 ===

1. 用户权限：
   - 为应用创建专用数据库用户
   - 限制用户权限到最小必要范围
   - 定期更新密码

2. 网络安全：
   - 限制数据库访问IP
   - 使用SSL连接
   - 启用防火墙

3. 数据保护：
   - 敏感数据加密存储
   - 定期安全审计
   - 监控异常访问

=== 故障排除 ===

1. 常见问题：
   - 字符集问题：确保使用utf8mb4
   - 外键约束：检查关联数据完整性
   - JSON字段：验证JSON格式正确性

2. 性能问题：
   - 检查慢查询日志
   - 分析执行计划
   - 优化索引使用

3. 空间问题：
   - 监控磁盘使用
   - 清理临时文件
   - 压缩历史数据

联系信息：
- 数据库版本：1.0.0
- 创建日期：2025-08-12
- 兼容性：MariaDB 10.5+ / MySQL 8.0+
*/
