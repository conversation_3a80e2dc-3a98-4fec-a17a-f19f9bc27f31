# ================================================================================================
# 文档智能分析系统 - 数据库配置示例
# Document Intelligence Analysis System - Database Configuration Example
# ================================================================================================
# 用途：Spring Boot 应用程序的数据库配置
# 文件位置：src/main/resources/application.yml 或 application-prod.yml
# ================================================================================================

spring:
  application:
    name: document-analysis-backend
  
  # ================================================================================================
  # MariaDB/MySQL 数据库配置
  # ================================================================================================
  datasource:
    # 数据库连接URL - 根据实际环境调整
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/doc_analysis?useUnicode=true&characterEncoding=utf8mb4&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&rewriteBatchedStatements=true
    
    # 数据库用户名和密码 - 建议使用环境变量
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:your_password}
    
    # 数据库驱动
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # HikariCP 连接池配置
    hikari:
      # 连接池大小配置
      maximum-pool-size: ${DB_MAX_POOL_SIZE:20}        # 最大连接数
      minimum-idle: ${DB_MIN_IDLE:5}                   # 最小空闲连接数
      
      # 超时配置
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}    # 连接超时（毫秒）
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}              # 空闲超时（毫秒）
      max-lifetime: ${DB_MAX_LIFETIME:1800000}             # 连接最大生命周期（毫秒）
      
      # 连接测试
      connection-test-query: SELECT 1
      validation-timeout: 5000
      
      # 连接池名称
      pool-name: DocAnalysisHikariCP
      
      # 其他配置
      auto-commit: true
      read-only: false
      
  # ================================================================================================
  # JPA/Hibernate 配置
  # ================================================================================================
  jpa:
    # Hibernate DDL 配置
    hibernate:
      ddl-auto: ${JPA_DDL_AUTO:validate}  # 生产环境建议使用 validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
    
    # SQL 显示配置
    show-sql: ${JPA_SHOW_SQL:false}      # 生产环境建议设为 false
    
    # Hibernate 属性配置
    properties:
      hibernate:
        # 数据库方言
        dialect: org.hibernate.dialect.MySQL8Dialect
        
        # SQL 格式化
        format_sql: ${JPA_FORMAT_SQL:false}
        
        # 批处理配置
        jdbc:
          batch_size: 20
          batch_versioned_data: true
        order_inserts: true
        order_updates: true
        
        # 二级缓存配置（可选）
        cache:
          use_second_level_cache: false
          use_query_cache: false
        
        # 统计信息
        generate_statistics: ${JPA_GENERATE_STATISTICS:false}
        
        # 连接处理
        connection:
          handling_mode: delayed_acquisition_and_release_after_transaction

# ================================================================================================
# MyBatis Plus 配置
# ================================================================================================
mybatis-plus:
  # 配置文件位置
  config-location: classpath:mybatis-config.xml
  
  # Mapper XML 文件位置
  mapper-locations: classpath*:mapper/*.xml
  
  # 类型别名包
  type-aliases-package: com.docanalysis.entity
  
  # 全局配置
  global-config:
    # 数据库配置
    db-config:
      # 主键类型
      id-type: auto
      
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      
      # 表名前缀
      # table-prefix: doc_
      
      # 字段验证策略
      insert-strategy: not_null
      update-strategy: not_null
      select-strategy: not_null
    
    # 横幅配置
    banner: false
  
  # MyBatis 配置
  configuration:
    # 驼峰命名转换
    map-underscore-to-camel-case: true
    
    # 缓存配置
    cache-enabled: false
    
    # 延迟加载
    lazy-loading-enabled: false
    aggressive-lazy-loading: false
    
    # 日志实现
    log-impl: ${MYBATIS_LOG_IMPL:org.apache.ibatis.logging.slf4j.Slf4jImpl}
    
    # 自动映射行为
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: none
    
    # 执行器类型
    default-executor-type: simple
    
    # 超时时间
    default-statement-timeout: 30

# ================================================================================================
# 数据库监控配置（Druid - 可选）
# ================================================================================================
# 如果使用 Druid 连接池，可以启用以下配置
# spring:
#   datasource:
#     type: com.alibaba.druid.pool.DruidDataSource
#     druid:
#       # 初始化配置
#       initial-size: 5
#       min-idle: 5
#       max-active: 20
#       
#       # 超时配置
#       max-wait: 60000
#       time-between-eviction-runs-millis: 60000
#       min-evictable-idle-time-millis: 300000
#       
#       # 验证配置
#       validation-query: SELECT 1
#       test-while-idle: true
#       test-on-borrow: false
#       test-on-return: false
#       
#       # 监控配置
#       filters: stat,wall,slf4j
#       web-stat-filter:
#         enabled: true
#         url-pattern: /*
#         exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
#       
#       stat-view-servlet:
#         enabled: true
#         url-pattern: /druid/*
#         reset-enable: false
#         login-username: admin
#         login-password: admin

# ================================================================================================
# 环境变量说明
# ================================================================================================
# 
# 生产环境建议设置以下环境变量：
# 
# MYSQL_HOST=your_database_host
# MYSQL_PORT=3306
# MYSQL_USERNAME=doc_user
# MYSQL_PASSWORD=your_secure_password
# 
# DB_MAX_POOL_SIZE=20
# DB_MIN_IDLE=5
# DB_CONNECTION_TIMEOUT=30000
# DB_IDLE_TIMEOUT=600000
# DB_MAX_LIFETIME=1800000
# 
# JPA_DDL_AUTO=validate
# JPA_SHOW_SQL=false
# JPA_FORMAT_SQL=false
# JPA_GENERATE_STATISTICS=false
# 
# MYBATIS_LOG_IMPL=org.apache.ibatis.logging.slf4j.Slf4jImpl
# 
# ================================================================================================
# Docker Compose 环境变量示例
# ================================================================================================
# 
# version: '3.8'
# services:
#   app:
#     environment:
#       - MYSQL_HOST=mariadb
#       - MYSQL_PORT=3306
#       - MYSQL_USERNAME=doc_user
#       - MYSQL_PASSWORD=DocAnalysis@2025
#       - JPA_DDL_AUTO=validate
#       - JPA_SHOW_SQL=false
#   
#   mariadb:
#     image: mariadb:10.6
#     environment:
#       - MYSQL_ROOT_PASSWORD=root_password
#       - MYSQL_DATABASE=doc_analysis
#       - MYSQL_USER=doc_user
#       - MYSQL_PASSWORD=DocAnalysis@2025
#     volumes:
#       - ./complete_mariadb_script.sql:/docker-entrypoint-initdb.d/init.sql
# 
# ================================================================================================
