# 文档智能分析系统 - 数据库部署指南

## 概述

本指南提供了文档智能分析系统数据库的完整部署和配置说明。系统支持 MariaDB 10.5+ 和 MySQL 8.0+。

## 文件说明

### 核心脚本文件

1. **`complete_mariadb_script.sql`** - 完整数据库脚本
   - 包含完整的表结构、索引、视图、存储过程、触发器
   - 包含示例数据和配置数据
   - 适用于生产环境部署

2. **`quick_deploy_mariadb.sql`** - 快速部署脚本
   - 仅包含基本表结构和必要配置
   - 适用于开发和测试环境
   - 部署速度快，结构简洁

3. **`database_config_example.yml`** - 应用配置示例
   - Spring Boot 数据库配置模板
   - 包含连接池、JPA、MyBatis Plus 配置
   - 环境变量配置说明

## 快速开始

### 1. 环境要求

- MariaDB 10.5+ 或 MySQL 8.0+
- 至少 1GB 可用磁盘空间
- 支持 UTF8MB4 字符集

### 2. 快速部署（开发环境）

```bash
# 1. 连接到数据库
mysql -u root -p

# 2. 执行快速部署脚本
mysql -u root -p < quick_deploy_mariadb.sql

# 3. 验证部署
mysql -u root -p doc_analysis -e "SHOW TABLES;"
```

### 3. 完整部署（生产环境）

```bash
# 1. 执行完整脚本
mysql -u root -p < complete_mariadb_script.sql

# 2. 验证部署
mysql -u root -p doc_analysis -e "SELECT * FROM v_system_health;"
```

## 详细部署步骤

### 步骤 1：准备环境

```bash
# 安装 MariaDB（Ubuntu/Debian）
sudo apt update
sudo apt install mariadb-server mariadb-client

# 安装 MariaDB（CentOS/RHEL）
sudo yum install mariadb-server mariadb

# 启动服务
sudo systemctl start mariadb
sudo systemctl enable mariadb

# 安全配置
sudo mysql_secure_installation
```

### 步骤 2：创建数据库用户（推荐）

```sql
-- 连接到 MariaDB
mysql -u root -p

-- 创建应用用户
CREATE USER 'doc_user'@'%' IDENTIFIED BY 'DocAnalysis@2025';
CREATE USER 'doc_readonly'@'%' IDENTIFIED BY 'ReadOnly@2025';

-- 授权
GRANT ALL PRIVILEGES ON doc_analysis.* TO 'doc_user'@'%';
GRANT SELECT ON doc_analysis.* TO 'doc_readonly'@'%';
FLUSH PRIVILEGES;
```

### 步骤 3：执行数据库脚本

```bash
# 方式1：直接执行完整脚本
mysql -u root -p < complete_mariadb_script.sql

# 方式2：分步执行（推荐生产环境）
mysql -u root -p < quick_deploy_mariadb.sql
# 然后根据需要添加其他功能
```

### 步骤 4：验证部署

```sql
-- 连接到数据库
mysql -u doc_user -p doc_analysis

-- 检查表结构
SHOW TABLES;

-- 检查数据
SELECT * FROM system_config;
SELECT * FROM v_database_status;
SELECT * FROM v_system_health;

-- 测试存储过程（如果使用完整脚本）
CALL GetDocumentAnalysisStats(1);
```

## 应用程序配置

### Spring Boot 配置

将 `database_config_example.yml` 中的配置复制到您的 `application.yml` 文件中：

```yaml
spring:
  datasource:
    url: ****************************************************************************************************************************
    username: doc_user
    password: DocAnalysis@2025
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 环境变量配置

```bash
# 设置环境变量
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export MYSQL_USERNAME=doc_user
export MYSQL_PASSWORD=DocAnalysis@2025
export JPA_DDL_AUTO=validate
```

## Docker 部署

### Docker Compose 配置

```yaml
version: '3.8'
services:
  mariadb:
    image: mariadb:10.6
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: doc_analysis
      MYSQL_USER: doc_user
      MYSQL_PASSWORD: DocAnalysis@2025
    volumes:
      - ./complete_mariadb_script.sql:/docker-entrypoint-initdb.d/init.sql
      - mariadb_data:/var/lib/mysql
    ports:
      - "3306:3306"
    
  app:
    build: .
    environment:
      MYSQL_HOST: mariadb
      MYSQL_USERNAME: doc_user
      MYSQL_PASSWORD: DocAnalysis@2025
    depends_on:
      - mariadb
    ports:
      - "8080:8080"

volumes:
  mariadb_data:
```

### 启动 Docker 环境

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs mariadb

# 连接到数据库
docker-compose exec mariadb mysql -u doc_user -p doc_analysis
```

## 维护和监控

### 日常维护

```sql
-- 1. 清理过期数据（90天前）
CALL CleanupExpiredData(90);

-- 2. 更新表统计信息
ANALYZE TABLE documents, analysis_results, user_interactions;

-- 3. 优化表
OPTIMIZE TABLE documents, analysis_results, user_interactions;

-- 4. 检查系统健康
SELECT * FROM v_system_health;
```

### 性能监控

```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看连接状态
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';

-- 查看表大小
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'doc_analysis'
ORDER BY (data_length + index_length) DESC;
```

### 备份和恢复

```bash
# 完整备份
mysqldump -u root -p --single-transaction --routines --triggers doc_analysis > backup_$(date +%Y%m%d).sql

# 仅结构备份
mysqldump -u root -p --no-data doc_analysis > schema_backup.sql

# 恢复数据
mysql -u root -p doc_analysis < backup_20250812.sql
```

## 故障排除

### 常见问题

1. **字符集问题**
   ```sql
   -- 检查字符集
   SHOW VARIABLES LIKE 'character_set%';
   
   -- 修改字符集
   ALTER DATABASE doc_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **连接问题**
   ```sql
   -- 检查用户权限
   SHOW GRANTS FOR 'doc_user'@'%';
   
   -- 重置密码
   ALTER USER 'doc_user'@'%' IDENTIFIED BY 'new_password';
   ```

3. **性能问题**
   ```sql
   -- 查看执行计划
   EXPLAIN SELECT * FROM documents WHERE status = 'COMPLETED';
   
   -- 检查索引使用
   SHOW INDEX FROM documents;
   ```

## 安全建议

1. **用户权限管理**
   - 为不同环境创建不同用户
   - 限制用户权限到最小必要范围
   - 定期更新密码

2. **网络安全**
   - 限制数据库访问IP
   - 使用SSL连接
   - 配置防火墙规则

3. **数据保护**
   - 定期备份数据
   - 监控异常访问
   - 加密敏感数据

## 联系信息

- 数据库版本：1.0.0
- 创建日期：2025-08-12
- 兼容性：MariaDB 10.5+ / MySQL 8.0+
- 字符集：UTF8MB4

如有问题，请参考项目文档或联系开发团队。
