# 基于 pdfjs-dist 的高级功能实现指南

## 项目信息
- **分析时间**: 2025-08-18T11:25:43+08:00
- **当前版本**: pdfjs-dist: ^3.11.174
- **实现方式**: 基于现有 pdfjs-dist 包，无需 plugins 目录

## 1. 现状分析

### 1.1 当前项目优势 ✅
- ✅ 已使用最新版本 `pdfjs-dist: ^3.11.174`
- ✅ 已有完整的PDF.js配置和Worker设置
- ✅ 已解决Vue 3兼容性问题（markRaw方案）
- ✅ 已有基础渲染和缓存机制
- ✅ 已有DocumentViewer.vue中的搜索功能实现参考

### 1.2 可直接使用的API
`pdfjs-dist`包已包含所有高级功能API：
- **TextLayer API**: 文本提取和搜索
- **AnnotationLayer API**: 注释处理
- **XfaLayer API**: 表单处理
- **StructTree API**: 文档结构
- **Outline API**: 文档大纲

## 2. 高级功能实现方案

### 2.1 文本搜索功能 🔍

#### 实现原理
使用PDF.js的TextLayer API提取文本内容，然后进行搜索和高亮。

#### 代码实现
```typescript
// 在 DocumentPdfViewer.vue 中添加

// 文本搜索相关状态
const searchText = ref('')
const searchResults = ref<Array<{
  pageIndex: number
  textDivs: HTMLElement[]
  match: string
}>>([])
const currentSearchIndex = ref(-1)
const textLayers = ref(new Map<number, any>()) // 存储每页的文本层

// 渲染文本层（在renderSinglePage中调用）
const renderTextLayer = async (pageNum: number, page: any, viewport: any) => {
  try {
    // 获取文本内容
    const textContent = await page.getTextContent()
    
    // 创建文本层容器
    const textLayerDiv = document.createElement('div')
    textLayerDiv.className = 'textLayer'
    textLayerDiv.style.position = 'absolute'
    textLayerDiv.style.left = '0'
    textLayerDiv.style.top = '0'
    textLayerDiv.style.right = '0'
    textLayerDiv.style.bottom = '0'
    textLayerDiv.style.overflow = 'hidden'
    textLayerDiv.style.opacity = '0.2' // 透明文本层
    textLayerDiv.style.lineHeight = '1.0'
    
    // 渲染文本层
    const textLayer = new pdfjsLib.TextLayer({
      textContentSource: textContent,
      container: textLayerDiv,
      viewport: viewport,
      textDivs: [],
      textContentItemsStr: [],
      isOffscreenCanvasSupported: false
    })
    
    await textLayer.render()
    
    // 存储文本层引用
    textLayers.value.set(pageNum, {
      textLayer,
      textContent,
      textLayerDiv
    })
    
    return textLayerDiv
  } catch (error) {
    console.error(`第${pageNum}页文本层渲染失败:`, error)
    return null
  }
}

// 搜索功能实现
const searchInPdf = async (query: string) => {
  if (!query.trim() || !pdfDocument.value) {
    clearSearchResults()
    return
  }
  
  searchText.value = query
  searchResults.value = []
  
  // 在所有页面中搜索
  for (let pageNum = 1; pageNum <= totalPages.value; pageNum++) {
    const textLayerData = textLayers.value.get(pageNum)
    if (!textLayerData) {
      // 如果文本层未渲染，先渲染
      const page = await pdfDocument.value.getPage(pageNum)
      const viewport = page.getViewport({ scale: scale.value })
      await renderTextLayer(pageNum, page, viewport)
      continue
    }
    
    // 在文本内容中搜索
    const { textContent } = textLayerData
    const textItems = textContent.items
    
    for (let i = 0; i < textItems.length; i++) {
      const item = textItems[i]
      if (item.str && item.str.toLowerCase().includes(query.toLowerCase())) {
        searchResults.value.push({
          pageIndex: pageNum,
          textDivs: [textLayerData.textLayer.textDivs[i]],
          match: item.str
        })
      }
    }
  }
  
  // 高亮第一个搜索结果
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = 0
    highlightSearchResult(0)
  }
}

// 高亮搜索结果
const highlightSearchResult = (index: number) => {
  // 清除之前的高亮
  clearSearchHighlight()
  
  if (index >= 0 && index < searchResults.value.length) {
    const result = searchResults.value[index]
    
    // 跳转到对应页面
    scrollToPage(result.pageIndex)
    
    // 高亮文本
    result.textDivs.forEach(div => {
      div.style.backgroundColor = '#ffff00'
      div.style.color = '#000'
    })
    
    // 滚动到高亮位置
    if (result.textDivs[0]) {
      result.textDivs[0].scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
    }
  }
}

// 清除搜索高亮
const clearSearchHighlight = () => {
  searchResults.value.forEach(result => {
    result.textDivs.forEach(div => {
      div.style.backgroundColor = ''
      div.style.color = ''
    })
  })
}

// 搜索导航
const nextSearchResult = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
    highlightSearchResult(currentSearchIndex.value)
  }
}

const prevSearchResult = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = currentSearchIndex.value <= 0 
      ? searchResults.value.length - 1 
      : currentSearchIndex.value - 1
    highlightSearchResult(currentSearchIndex.value)
  }
}
```

### 2.2 文档大纲功能 📋

```typescript
// 文档大纲相关状态
const documentOutline = ref<any[]>([])
const showOutline = ref(false)

// 获取文档大纲
const loadDocumentOutline = async () => {
  if (!pdfDocument.value) return
  
  try {
    const outline = await pdfDocument.value.getOutline()
    if (outline) {
      documentOutline.value = outline
      console.log('文档大纲加载成功:', outline)
    }
  } catch (error) {
    console.error('获取文档大纲失败:', error)
  }
}

// 大纲项点击处理
const handleOutlineClick = async (item: any) => {
  if (item.dest) {
    try {
      const dest = await pdfDocument.value.getDestination(item.dest)
      if (dest) {
        const pageRef = dest[0]
        const pageIndex = await pdfDocument.value.getPageIndex(pageRef)
        scrollToPage(pageIndex + 1) // PDF.js页面索引从0开始
      }
    } catch (error) {
      console.error('跳转到大纲位置失败:', error)
    }
  }
}
```

### 2.3 缩略图功能 🖼️

```typescript
// 缩略图相关状态
const thumbnails = ref(new Map<number, string>())
const showThumbnails = ref(false)

// 生成页面缩略图
const generateThumbnail = async (pageNum: number) => {
  if (!pdfDocument.value) return null
  
  try {
    const page = await pdfDocument.value.getPage(pageNum)
    const viewport = page.getViewport({ scale: 0.2 }) // 小尺寸缩略图
    
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    
    canvas.height = viewport.height
    canvas.width = viewport.width
    
    await page.render({
      canvasContext: context,
      viewport: viewport
    }).promise
    
    const thumbnailUrl = canvas.toDataURL()
    thumbnails.value.set(pageNum, thumbnailUrl)
    
    return thumbnailUrl
  } catch (error) {
    console.error(`生成第${pageNum}页缩略图失败:`, error)
    return null
  }
}

// 批量生成缩略图
const generateAllThumbnails = async () => {
  for (let i = 1; i <= totalPages.value; i++) {
    await generateThumbnail(i)
  }
}
```

### 2.4 注释功能 ✏️

```typescript
// 注释相关状态
const annotations = ref(new Map<number, any[]>())

// 渲染注释层
const renderAnnotationLayer = async (pageNum: number, page: any, viewport: any) => {
  try {
    const annotationData = await page.getAnnotations()
    
    if (annotationData.length > 0) {
      const annotationLayerDiv = document.createElement('div')
      annotationLayerDiv.className = 'annotationLayer'
      annotationLayerDiv.style.position = 'absolute'
      annotationLayerDiv.style.left = '0'
      annotationLayerDiv.style.top = '0'
      annotationLayerDiv.style.right = '0'
      annotationLayerDiv.style.bottom = '0'
      
      // 渲染注释
      const annotationLayer = new pdfjsLib.AnnotationLayer({
        div: annotationLayerDiv,
        annotations: annotationData,
        page: page,
        viewport: viewport,
        linkService: {
          externalLinkTarget: 2, // 新窗口打开外部链接
          externalLinkRel: 'noopener noreferrer nofollow'
        }
      })
      
      annotationLayer.render()
      annotations.value.set(pageNum, annotationData)
      
      return annotationLayerDiv
    }
  } catch (error) {
    console.error(`第${pageNum}页注释层渲染失败:`, error)
  }
  
  return null
}
```

## 3. 集成到现有组件

### 3.1 修改 renderSinglePage 方法

```typescript
// 修改现有的 renderSinglePage 方法
const renderSinglePage = async (pageNum: number) => {
  // ... 现有的Canvas渲染代码 ...
  
  // 创建页面容器
  const pageContainer = document.createElement('div')
  pageContainer.className = 'pdf-page-container'
  pageContainer.style.position = 'relative'
  pageContainer.style.marginBottom = '20px'
  
  // 添加Canvas
  pageContainer.appendChild(canvas)
  
  // 渲染文本层
  const textLayerDiv = await renderTextLayer(pageNum, page, viewport)
  if (textLayerDiv) {
    pageContainer.appendChild(textLayerDiv)
  }
  
  // 渲染注释层
  const annotationLayerDiv = await renderAnnotationLayer(pageNum, page, viewport)
  if (annotationLayerDiv) {
    pageContainer.appendChild(annotationLayerDiv)
  }
  
  return pageContainer
}
```

### 3.2 添加搜索工具栏

```vue
<!-- 在模板中添加搜索工具栏 -->
<div class="pdf-search-toolbar" v-if="showSearchBar">
  <el-input
    v-model="searchText"
    placeholder="在文档中搜索..."
    @keyup.enter="searchInPdf(searchText)"
    size="small"
    class="search-input"
  >
    <template #append>
      <el-button @click="searchInPdf(searchText)" :icon="Search" />
    </template>
  </el-input>
  
  <div class="search-navigation" v-if="searchResults.length > 0">
    <span class="search-count">
      {{ currentSearchIndex + 1 }} / {{ searchResults.length }}
    </span>
    <el-button-group size="small">
      <el-button @click="prevSearchResult" :icon="ArrowUp" />
      <el-button @click="nextSearchResult" :icon="ArrowDown" />
    </el-button-group>
  </div>
  
  <el-button @click="clearSearchResults" size="small" :icon="Close" />
</div>
```

## 4. 实现优势

### 4.1 相比官方WebViewer的优势 ✅
- ✅ **完全控制**: 可以精确控制每个功能的实现
- ✅ **Vue集成**: 与Vue 3响应式系统完美集成
- ✅ **样式一致**: 与Element Plus组件库风格统一
- ✅ **性能优化**: 保持现有的懒加载和缓存优化
- ✅ **渐进实现**: 可以按需实现功能，不必全部实现

### 4.2 相比iframe方案的优势 ✅
- ✅ **无通信复杂性**: 直接使用API，无需postMessage
- ✅ **状态同步**: 完美的Vue响应式状态管理
- ✅ **调试友好**: 可以直接调试，无iframe隔离
- ✅ **SEO友好**: 文本内容可被搜索引擎索引

## 5. 实施建议

### 5.1 优先级排序
1. **高优先级**: 文本搜索功能（用户需求最高）
2. **中优先级**: 文档大纲、缩略图导航
3. **低优先级**: 注释功能、高级编辑功能

### 5.2 实施策略
1. **第一阶段**: 实现文本搜索和高亮
2. **第二阶段**: 添加文档大纲和缩略图
3. **第三阶段**: 根据用户反馈决定是否实现注释功能

### 5.3 兼容性保证
- 保持现有API接口不变
- 新功能作为可选功能添加
- 保持现有性能优化效果

## 6. 结论

**您的观点完全正确！** 使用`pdfjs-dist`包可以实现所有高级功能，而且：

1. ✅ **无需plugins目录**: 直接使用npm包即可
2. ✅ **API完整**: 包含所有必要的高级功能API
3. ✅ **实现可控**: 可以精确控制功能实现和用户体验
4. ✅ **维护简单**: 跟随npm包版本更新即可

这确实是比iframe方案更好的选择，既保持了功能完整性，又保持了技术架构的一致性。
