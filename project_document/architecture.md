# PDF在线预览功能架构设计文档

## 项目信息
- **执行模式**: 完整系统模式
- **总状态**: 设计阶段
- **最后更新**: 2025-08-18T11:16:38+08:00
- **性能指标**: 并行度 L1[85%] L2[60%] L3[40%] | 时间节省[~70%]

## 团队配置
- **内置顾问团**: AR, PDM, LD, DW, QE
- **动态Subagents**: backend-api-expert, frontend-vue-expert, pdfjs-integration-expert

## 1. 架构概述

### 1.1 设计目标
基于CSDN博客文章的PDF.js实现方案，在现有Spring Boot后端和Vue前端项目中集成PDF在线预览功能，实现：
- 页面翻页、缩放、导航等基本功能
- 与现有文档上传流程的无缝集成
- 支持权限控制的安全PDF访问
- 响应式设计和移动端兼容性

### 1.2 技术选型
- **后端**: Spring Boot 2.7.18 + 现有DocumentController
- **前端**: Vue 3 + PDF.js + 现有组件架构
- **数据传输**: ArrayBuffer格式（当前已支持）
- **预览方式**: 组件化集成（非iframe嵌入）

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Vue应用    │    │  Spring Boot    │    │   文件存储      │
│                │    │     后端        │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │PDF预览组件  │ │◄──►│ │DocumentCtrl │ │◄──►│ │  PDF文件    │ │
│ │             │ │    │ │             │ │    │ │             │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                │    │                │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                │
│ │PDF.js库     │ │    │ │文件服务     │ │    │                │
│ │             │ │    │ │             │ │    │                │
│ └─────────────┘ │    │ └─────────────┘ │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 数据流设计
1. **文档上传流程**（已存在）：
   ```
   用户上传 → DocumentController.upload() → 文件存储 → 转换为PDF → 数据库记录
   ```

2. **PDF预览流程**（新增优化）：
   ```
   前端请求 → DocumentController.getPdf() → 读取PDF文件 → 返回ArrayBuffer → PDF.js渲染
   ```

## 3. 核心组件设计

### 3.1 后端接口优化

#### 3.1.1 现有接口分析
- **接口**: `GET /documents/{id}/pdf`
- **返回格式**: `byte[]` (已符合PDF.js要求)
- **Content-Type**: `application/pdf`
- **安全机制**: 已完备

#### 3.1.2 优化方案
1. **添加流式传输支持**：
   - 支持Range请求，提升大文件加载性能
   - 添加缓存控制头，优化重复访问

2. **新增URL访问端点**（可选）：
   - `GET /documents/{id}/pdf/stream` - 支持直接URL访问
   - 用于PDF.js的viewer.html?file=url方式

### 3.2 前端组件架构

#### 3.2.1 组件层次结构
```
PdfViewerContainer (容器组件)
├── PdfToolbar (工具栏)
│   ├── PageNavigation (页面导航)
│   ├── ZoomControls (缩放控制)
│   └── ActionButtons (操作按钮)
├── PdfCanvas (PDF渲染区域)
│   ├── PageRenderer (页面渲染器)
│   └── LoadingIndicator (加载指示器)
└── PdfSidebar (侧边栏 - 可选)
    ├── ThumbnailView (缩略图)
    └── OutlineView (大纲)
```

#### 3.2.2 核心功能模块
1. **PDF加载模块**：
   - 支持ArrayBuffer数据源
   - 错误处理和重试机制
   - 加载进度显示

2. **渲染控制模块**：
   - 页面渲染管理
   - 缩放和旋转控制
   - 响应式布局适配

3. **交互控制模块**：
   - 页面导航（上一页/下一页/跳转）
   - 缩放控制（放大/缩小/适应宽度/适应页面）
   - 全屏模式切换

## 4. 实现策略

### 4.1 渐进式集成策略
1. **阶段一**：优化现有PDF预览组件
2. **阶段二**：增强用户交互功能
3. **阶段三**：性能优化和移动端适配

### 4.2 兼容性保证
- 保持现有API接口不变
- 向后兼容现有前端组件
- 支持渐进式功能增强

## 5. 性能优化设计

### 5.1 前端优化
- PDF.js Worker线程配置
- 页面懒加载和虚拟滚动
- 缓存策略优化

### 5.2 后端优化
- 文件流式传输
- HTTP缓存头配置
- 并发访问控制

## 6. 安全考虑

### 6.1 访问控制
- 继承现有权限验证机制
- 防止直接文件路径访问
- 跨域请求安全控制

### 6.2 数据保护
- 敏感文件访问日志
- 防止PDF内容泄露
- 客户端缓存控制

## 7. 技术风险评估

### 7.1 兼容性风险
- **风险**: PDF.js版本兼容性
- **缓解**: 使用稳定版本，充分测试

### 7.2 性能风险
- **风险**: 大文件加载性能
- **缓解**: 流式传输，分页加载

### 7.3 安全风险
- **风险**: 文件访问权限绕过
- **缓解**: 严格权限验证，安全审计

## 8. 实施计划

### 8.1 开发优先级
1. **高优先级**: 核心预览功能
2. **中优先级**: 用户交互增强
3. **低优先级**: 高级功能和优化

### 8.2 测试策略
- 功能测试：各浏览器兼容性
- 性能测试：大文件加载测试
- 安全测试：权限验证测试

## 9. 关键决策记录

### 9.1 数据传输方式
**决策**: 继续使用ArrayBuffer格式
**理由**: 
- 现有接口已支持
- 安全性更好（需要权限验证）
- 避免直接文件URL暴露

### 9.2 集成方式
**决策**: 组件化集成，非iframe嵌入
**理由**:
- 更好的用户体验
- 更灵活的界面定制
- 更好的响应式支持

### 9.3 PDF.js版本选择
**决策**: 使用当前项目中的PDF.js版本，必要时升级
**理由**:
- 减少兼容性风险
- 保持项目依赖一致性
