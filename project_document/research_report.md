# DocumentPdfViewer.vue 问题研究报告

## 研究时间
2025-08-18T16:47:22+08:00

## 问题分析

### 1. 文本选择功能问题

#### 根本原因分析
通过代码审查发现关键冲突：

**JavaScript设置 (行1419)**:
```javascript
textLayerDiv.style.opacity = '0.2' // 半透明，便于调试和文本选择
```

**CSS样式覆盖 (行4553)**:
```css
.textLayer {
  opacity: 0 !important; // 完全透明，CSS优先级更高
}
```

#### 技术细节
- TextLayer DOM结构正确：Canvas和TextLayer正确叠加
- 事件绑定正确：enableTextSelection函数正确绑定到textLayerDiv
- CSS层叠问题：`!important` 覆盖了JavaScript的内联样式
- 用户体验影响：opacity: 0导致用户无法看到可选择的文本区域

#### 最佳实践参考
根据Stack Overflow研究，PDF.js TextLayer最佳实践：
- TextLayer应该有适当的透明度(0.1-0.3)以便用户识别可选择区域
- 必须设置 `pointer-events: auto` 和 `user-select: text`
- 位置必须与Canvas完全重叠

### 2. 搜索输入框问题

#### 当前配置检查
```vue
<el-input
  v-model="searchText"
  placeholder="在PDF中搜索..."
  :prefix-icon="Search"
  clearable
  @input="handleSearchInput"
  @keyup.enter="handleSearchEnter"
  @clear="clearSearchResults"
  size="small"
  class="search-input"
  ref="searchInputRef"
/>
```

#### 潜在问题点
1. **响应式状态**: searchText变量正确定义为ref
2. **Element Plus配置**: 未发现disabled或readonly属性
3. **事件冲突**: 可能存在全局事件监听器干扰
4. **CSS样式**: 需要检查是否有样式导致输入框不可交互

### 3. TextLayer可见性问题

#### 当前状态
- 代码中设置opacity: 0.2用于调试
- CSS中强制设置opacity: 0 !important
- 导致用户看到PDF背景上的文字显示

#### 解决方向
1. 移除CSS中的 `opacity: 0 !important`
2. 调整为合适的透明度值
3. 确保文本选择功能正常工作

## 修复优先级
1. **高优先级**: 修复TextLayer透明度冲突
2. **中优先级**: 诊断搜索输入框问题
3. **低优先级**: 优化TextLayer视觉效果
