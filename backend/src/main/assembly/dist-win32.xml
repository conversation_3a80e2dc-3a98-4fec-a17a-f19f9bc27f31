<?xml version="1.0" encoding="UTF-8"?>
<assembly
        xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/2.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
    <id>assembly-windows</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>true</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>src/main/bin</directory>
            <outputDirectory>${file.separator}bin</outputDirectory>
            <includes>
                <include>*.bat</include>
            </includes>
        </fileSet>
        <fileSet>
            <directory>src/main/config</directory>
            <outputDirectory>${file.separator}config</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>src/main/log</directory>
            <outputDirectory>${file.separator}log</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>LibreOfficePortable</directory>
            <outputDirectory>${file.separator}LibreOfficePortable</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>${file.separator}bin</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
