package com.docanalysis.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;


@Data
@Accessors(chain = true)
public class DocumentUploadDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文档ID（上传成功后返回）
     */
    private Long id;

    /**
     * 系统生成的文件名
     */
    private String filename;

    /**
     * 用户上传的原始文件名
     */
    @NotBlank(message = "原始文件名不能为空")
    @Size(max = 255, message = "文件名长度不能超过255个字符")
    private String originalName;

    /**
     * 文件存储路径
     */
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    private String contentType;

    /**
     * 上传状态
     */
    private String status;

    /**
     * 上传进度（百分比）
     */
    private Integer progress;

    /**
     * 错误信息（如果有）
     */
    private String errorMessage;

    /**
     * 上传时间戳
     */
    private Long uploadTimestamp;

    /**
     * 文件MD5哈希值（用于重复检测）
     */
    private String md5Hash;

    /**
     * 检查文件是否为DOCX格式
     */
    public boolean isDocxFile() {
        if (originalName == null) {
            return false;
        }
        String lowerName = originalName.toLowerCase();
        return lowerName.endsWith(".docx") && 
               "application/vnd.openxmlformats-officedocument.wordprocessingml.document".equals(contentType);
    }

    /**
     * 检查文件大小是否在允许范围内
     */
    public boolean isFileSizeValid(long maxSizeBytes) {
        return fileSize != null && fileSize > 0 && fileSize <= maxSizeBytes;
    }

    /**
     * 获取可读的文件大小
     */
    public String getReadableFileSize() {
        if (fileSize == null) {
            return "0 B";
        }
        
        long size = fileSize;
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", (double) size, units[unitIndex]);
    }

    /**
     * 设置上传成功状态
     */
    public DocumentUploadDTO setUploadSuccess() {
        this.status = "SUCCESS";
        this.progress = 100;
        this.errorMessage = null;
        return this;
    }

    /**
     * 设置上传失败状态
     */
    public DocumentUploadDTO setUploadFailed(String errorMessage) {
        this.status = "FAILED";
        this.progress = 0;
        this.errorMessage = errorMessage;
        return this;
    }

    /**
     * 设置上传进行中状态
     */
    public DocumentUploadDTO setUploadInProgress(int progress) {
        this.status = "UPLOADING";
        this.progress = Math.max(0, Math.min(100, progress));
        this.errorMessage = null;
        return this;
    }
}
