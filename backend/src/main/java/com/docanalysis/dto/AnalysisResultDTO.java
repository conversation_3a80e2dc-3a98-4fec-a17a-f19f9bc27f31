package com.docanalysis.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Data
@Accessors(chain = true)
public class AnalysisResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分析结果ID
     */
    private Long id;

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 智能体类型
     */
    private String agentType;

    /**
     * 原始结果内容
     */
    private Map<String, Object> resultContent;

    /**
     * 解析后的结构化数据
     */
    private StructuredResult structuredResult;

    /**
     * 处理耗时（毫秒）
     */
    private Integer processingTime;

    /**
     * API响应状态码
     */
    private Integer apiResponseCode;

    /**
     * 分析完成时间
     */
    private LocalDateTime createdTime;

    /**
     * 结构化结果
     */
    @Data
    @Accessors(chain = true)
    public static class StructuredResult implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 状态
         */
        private String status;

        /**
         * 主要内容
         */
        private String content;

        /**
         * 提取的数据项（提取智能体）
         */
        private List<ExtractedItem> extractedItems;

        /**
         * 检测的问题（检测智能体）
         */
        private List<DetectedIssue> detectedIssues;

        /**
         * 元数据
         */
        private Map<String, Object> metadata;
    }

    /**
     * 提取的数据项
     */
    @Data
    @Accessors(chain = true)
    public static class ExtractedItem implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 数据类型
         */
        private String type;

        /**
         * 标题/名称
         */
        private String title;

        /**
         * 内容/值
         */
        private String content;

        /**
         * 位置信息
         */
        private PositionInfo position;

        /**
         * 置信度
         */
        private Double confidence;

        /**
         * 重要性级别
         */
        private String importance;

        /**
         * 附加属性
         */
        private Map<String, Object> attributes;
    }

    /**
     * 检测的问题
     */
    @Data
    @Accessors(chain = true)
    public static class DetectedIssue implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 问题类型
         */
        private String type;

        /**
         * 问题标题
         */
        private String title;

        /**
         * 问题描述
         */
        private String description;

        /**
         * 严重程度
         */
        private String severity;

        /**
         * 位置信息
         */
        private PositionInfo position;

        /**
         * 建议修改
         */
        private String suggestion;

        /**
         * 影响范围
         */
        private String impact;

        /**
         * 附加属性
         */
        private Map<String, Object> attributes;
    }

    /**
     * 位置信息
     */
    @Data
    @Accessors(chain = true)
    public static class PositionInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 起始位置
         */
        private Integer start;

        /**
         * 结束位置
         */
        private Integer end;

        /**
         * 页码
         */
        private Integer page;

        /**
         * 段落索引
         */
        private Integer paragraphIndex;

        /**
         * 行号
         */
        private Integer lineNumber;

        /**
         * 元素ID
         */
        private String elementId;
    }

    /**
     * 检查是否为提取类型结果
     */
    public boolean isExtractResult() {
        return "EXTRACT".equals(agentType);
    }

    /**
     * 检查是否为检测类型结果
     */
    public boolean isDetectResult() {
        return "DETECT".equals(agentType);
    }

    /**
     * 检查分析是否成功
     */
    public boolean isAnalysisSuccessful() {
        return structuredResult != null && 
               "success".equals(structuredResult.getStatus()) && 
               apiResponseCode != null && 
               apiResponseCode == 200;
    }

    /**
     * 获取提取项数量
     */
    public int getExtractedItemCount() {
        if (structuredResult != null && structuredResult.getExtractedItems() != null) {
            return structuredResult.getExtractedItems().size();
        }
        return 0;
    }

    /**
     * 获取检测问题数量
     */
    public int getDetectedIssueCount() {
        if (structuredResult != null && structuredResult.getDetectedIssues() != null) {
            return structuredResult.getDetectedIssues().size();
        }
        return 0;
    }

    /**
     * 获取可读的处理时间
     */
    public String getReadableProcessingTime() {
        if (processingTime == null) {
            return "未知";
        }
        
        if (processingTime < 1000) {
            return processingTime + "ms";
        } else if (processingTime < 60000) {
            return String.format("%.1fs", processingTime / 1000.0);
        } else {
            int minutes = processingTime / 60000;
            int seconds = (processingTime % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        }
    }

    /**
     * 获取分析摘要
     */
    public String getAnalysisSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (isExtractResult()) {
            summary.append("提取了 ").append(getExtractedItemCount()).append(" 项数据");
        } else if (isDetectResult()) {
            summary.append("检测到 ").append(getDetectedIssueCount()).append(" 个问题");
        }
        
        if (processingTime != null) {
            summary.append("，耗时 ").append(getReadableProcessingTime());
        }
        
        return summary.toString();
    }
}
