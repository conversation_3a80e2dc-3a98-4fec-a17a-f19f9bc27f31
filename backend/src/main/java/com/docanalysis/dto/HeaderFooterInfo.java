package com.docanalysis.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class HeaderFooterInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 页眉左侧文本
     */
    private String headerLeft;

    /**
     * 页眉中央文本
     */
    private String headerCenter;

    /**
     * 页眉右侧文本
     */
    private String headerRight;

    /**
     * 页脚左侧文本
     */
    private String footerLeft;

    /**
     * 页脚中央文本
     */
    private String footerCenter;

    /**
     * 页脚右侧文本
     */
    private String footerRight;

    /**
     * 是否有页眉信息
     */
    public boolean hasHeader() {
        return (headerLeft != null && !headerLeft.trim().isEmpty()) ||
               (headerCenter != null && !headerCenter.trim().isEmpty()) ||
               (headerRight != null && !headerRight.trim().isEmpty());
    }

    /**
     * 是否有页脚信息
     */
    public boolean hasFooter() {
        return (footerLeft != null && !footerLeft.trim().isEmpty()) ||
               (footerCenter != null && !footerCenter.trim().isEmpty()) ||
               (footerRight != null && !footerRight.trim().isEmpty());
    }

    /**
     * 是否有任何页眉页脚信息
     */
    public boolean hasAny() {
        return hasHeader() || hasFooter();
    }

    /**
     * 获取页眉完整文本（左中右拼接）
     */
    public String getFullHeaderText() {
        StringBuilder sb = new StringBuilder();
        if (headerLeft != null && !headerLeft.trim().isEmpty()) {
            sb.append(headerLeft.trim());
        }
        if (headerCenter != null && !headerCenter.trim().isEmpty()) {
            if (sb.length() > 0) sb.append(" | ");
            sb.append(headerCenter.trim());
        }
        if (headerRight != null && !headerRight.trim().isEmpty()) {
            if (sb.length() > 0) sb.append(" | ");
            sb.append(headerRight.trim());
        }
        return sb.toString();
    }

    /**
     * 获取页脚完整文本（左中右拼接）
     */
    public String getFullFooterText() {
        StringBuilder sb = new StringBuilder();
        if (footerLeft != null && !footerLeft.trim().isEmpty()) {
            sb.append(footerLeft.trim());
        }
        if (footerCenter != null && !footerCenter.trim().isEmpty()) {
            if (sb.length() > 0) sb.append(" | ");
            sb.append(footerCenter.trim());
        }
        if (footerRight != null && !footerRight.trim().isEmpty()) {
            if (sb.length() > 0) sb.append(" | ");
            sb.append(footerRight.trim());
        }
        return sb.toString();
    }
}
