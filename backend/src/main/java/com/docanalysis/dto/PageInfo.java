package com.docanalysis.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@Accessors(chain = true)
public class PageInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 页码（从1开始）
     */
    private Integer pageNumber;

    /**
     * 页面在文档中的起始位置（字符索引）
     */
    private Integer startPosition;

    /**
     * 页面在文档中的结束位置（字符索引）
     */
    private Integer endPosition;

    /**
     * 页面内容长度（字符数）
     */
    private Integer contentLength;

    /**
     * 页眉文本（该页的页眉）
     */
    private String headerText;

    /**
     * 页脚文本（该页的页脚）
     */
    private String footerText;

    /**
     * 页面是否包含表格
     */
    private Boolean hasTable;

    /**
     * 页面是否包含图片
     */
    private Boolean hasImage;

    /**
     * 页面是否有显式分页符
     */
    private Boolean hasPageBreak;

    /**
     * 页面估算的行数
     */
    private Integer estimatedLines;

    /**
     * 页面置信度（0.0-1.0）
     */
    private Double confidence;

    /**
     * 获取页面内容长度
     */
    public int getContentLength() {
        if (startPosition != null && endPosition != null) {
            return Math.max(0, endPosition - startPosition);
        }
        return contentLength != null ? contentLength : 0;
    }

    /**
     * 检查页面是否有效
     */
    public boolean isValid() {
        return pageNumber != null && pageNumber > 0 
               && startPosition != null && startPosition >= 0
               && endPosition != null && endPosition >= startPosition;
    }

    /**
     * 检查是否为首页
     */
    public boolean isFirstPage() {
        return pageNumber != null && pageNumber == 1;
    }

    /**
     * 检查是否包含特殊内容（表格或图片）
     */
    public boolean hasSpecialContent() {
        return (hasTable != null && hasTable) || (hasImage != null && hasImage);
    }

    /**
     * 获取页面描述信息
     */
    public String getPageDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("第").append(pageNumber).append("页");
        
        if (hasSpecialContent()) {
            desc.append(" (");
            if (hasTable != null && hasTable) {
                desc.append("含表格");
            }
            if (hasImage != null && hasImage) {
                if (hasTable != null && hasTable) {
                    desc.append("、");
                }
                desc.append("含图片");
            }
            desc.append(")");
        }
        
        if (hasPageBreak != null && hasPageBreak) {
            desc.append(" [分页符]");
        }
        
        return desc.toString();
    }

    /**
     * 获取置信度描述
     */
    public String getConfidenceDescription() {
        if (confidence == null) {
            return "未知";
        }
        
        if (confidence >= 0.9) {
            return "高";
        } else if (confidence >= 0.7) {
            return "中";
        } else {
            return "低";
        }
    }
}
