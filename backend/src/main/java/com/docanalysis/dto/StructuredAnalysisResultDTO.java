package com.docanalysis.dto;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StructuredAnalysisResultDTO {

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 提取结果列表
     */
    private List<ExtractItem> extractItems;

    /**
     * 检测结果列表
     */
    private List<DetectItem> detectItems;

    /**
     * 统计信息
     */
    private Statistics statistics;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdated;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 提取项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExtractItem {
        /**
         * 项目ID（用于前端标识）
         */
        private String id;

        /**
         * 提取点名称
         */
        private String title;

        /**
         * 原文内容数组（支持多个引用）
         */
        private List<SourceReference> sources;

        /**
         * 状态（success, warning, error）
         */
        private String status;

        /**
         * 重要性级别（normal, high, low）
         */
        private String importance;

        /**
         * 在文档中的位置信息（用于高亮）
         */
        private PositionInfo position;

        /**
         * 原始数据（如果需要）
         */
        private Map<String, Object> rawData;

        /**
         * 兼容性字段：合并后的完整内容
         */
        private String content;
    }

    /**
     * 检测项
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DetectItem {
        /**
         * 项目ID（用于前端标识）
         */
        private String id;

        /**
         * 检测点名称
         */
        private String title;

        /**
         * 原文内容数组（支持多个引用）
         */
        private List<SourceReference> sources;

        /**
         * 检测结果（异常结果、正常结果）
         */
        private String result;

        /**
         * 问题级别（high, medium, low）
         */
        private String severity;

        /**
         * 状态（detected, normal, error）
         */
        private String status;

        /**
         * 在文档中的位置信息（用于高亮）
         */
        private PositionInfo position;

        /**
         * 判断原因
         */
        private String reason;

        /**
         * 原始数据（如果需要）
         */
        private Map<String, Object> rawData;

        /**
         * 兼容性字段：合并后的完整内容
         */
        private String content;
    }

    /**
     * 原文引用
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SourceReference {
        /**
         * 引用序号（如"第一处"、"第二处"）
         */
        private String referenceIndex;

        /**
         * 引用位置描述（如"第一章 招标公告4.3"）
         */
        private String location;

        /**
         * 具体内容
         */
        private String content;

        /**
         * 在文档中的位置信息
         */
        private PositionInfo position;
    }

    /**
     * 位置信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PositionInfo {
        /**
         * 页码
         */
        private Integer page;

        /**
         * 段落索引
         */
        private Integer paragraph;

        /**
         * 开始位置
         */
        private Integer startOffset;

        /**
         * 结束位置
         */
        private Integer endOffset;

        /**
         * 关键词（用于搜索定位）
         */
        private List<String> keywords;
    }

    /**
     * 统计信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Statistics {
        /**
         * 提取项总数
         */
        private Integer totalExtractItems;

        /**
         * 检测项总数
         */
        private Integer totalDetectItems;

        /**
         * 成功提取数
         */
        private Integer successfulExtracts;

        /**
         * 异常检测数
         */
        private Integer abnormalDetects;

        /**
         * 正常检测数
         */
        private Integer normalDetects;

        /**
         * 高风险检测数
         */
        private Integer highSeverityDetects;

        /**
         * 中风险检测数
         */
        private Integer mediumSeverityDetects;

        /**
         * 低风险检测数
         */
        private Integer lowSeverityDetects;

        /**
         * 处理状态
         */
        private String processingStatus;

        /**
         * 完成度百分比
         */
        private Double completionPercentage;
    }
}
