package com.docanalysis.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Data
@Accessors(chain = true)
public class MaxKBResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 提取智能体结果
     */
    private AgentResult extractResult;

    /**
     * 检测智能体结果
     */
    private AgentResult detectResult;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 总处理时间（毫秒）
     */
    private Long totalProcessingTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 处理时间戳
     */
    private LocalDateTime processedTime;

    /**
     * 智能体结果
     */
    @Data
    @Accessors(chain = true)
    public static class AgentResult implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 智能体类型
         */
        private String agentType;

        /**
         * 会话ID
         */
        private String chatId;

        /**
         * 文件信息
         */
        private Map<String, Object> fileInfo;

        /**
         * 响应内容
         */
        private String content;

        /**
         * 原始响应数据
         */
        private Map<String, Object> rawResponse;

        /**
         * 处理时间（毫秒）
         */
        private Long processingTime;

        /**
         * API调用状态
         */
        private String status;

        /**
         * 错误信息
         */
        private String errorMessage;

        /**
         * 提取的数据（仅提取智能体）
         */
        private List<ExtractedData> extractedData;

        /**
         * 检测的问题（仅检测智能体）
         */
        private List<DetectedIssue> detectedIssues;

        /**
         * 获取文件ID
         */
        public String getFileId() {
            if (fileInfo != null && fileInfo.containsKey("file_id")) {
                return (String) fileInfo.get("file_id");
            }
            return null;
        }

        /**
         * 获取文件名
         */
        public String getFileName() {
            if (fileInfo != null && fileInfo.containsKey("name")) {
                return (String) fileInfo.get("name");
            }
            return null;
        }

        /**
         * 获取文件URL
         */
        public String getFileUrl() {
            if (fileInfo != null && fileInfo.containsKey("url")) {
                return (String) fileInfo.get("url");
            }
            return null;
        }
    }

    /**
     * 提取的数据
     */
    @Data
    @Accessors(chain = true)
    public static class ExtractedData implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 数据类型
         */
        private String type;

        /**
         * 提取的内容
         */
        private String content;

        /**
         * 位置信息
         */
        private PositionInfo position;

        /**
         * 置信度
         */
        private Double confidence;

        /**
         * 元数据
         */
        private Map<String, Object> metadata;
    }

    /**
     * 检测的问题
     */
    @Data
    @Accessors(chain = true)
    public static class DetectedIssue implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 问题类型
         */
        private String type;

        /**
         * 问题描述
         */
        private String description;

        /**
         * 严重程度
         */
        private String severity;

        /**
         * 位置信息
         */
        private PositionInfo position;

        /**
         * 建议修改
         */
        private String suggestion;

        /**
         * 元数据
         */
        private Map<String, Object> metadata;
    }

    /**
     * 位置信息
     */
    @Data
    @Accessors(chain = true)
    public static class PositionInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 起始位置
         */
        private Integer start;

        /**
         * 结束位置
         */
        private Integer end;

        /**
         * 页码
         */
        private Integer page;

        /**
         * 段落索引
         */
        private Integer paragraphIndex;

        /**
         * 行号
         */
        private Integer lineNumber;
    }

    /**
     * 检查是否处理成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return errorMessage != null && !errorMessage.isEmpty();
    }

    /**
     * 检查提取智能体是否成功
     */
    public boolean isExtractSuccess() {
        return extractResult != null && "SUCCESS".equals(extractResult.getStatus());
    }

    /**
     * 检查检测智能体是否成功
     */
    public boolean isDetectSuccess() {
        return detectResult != null && "SUCCESS".equals(detectResult.getStatus());
    }

    /**
     * 获取成功的智能体数量
     */
    public int getSuccessfulAgentCount() {
        int count = 0;
        if (isExtractSuccess()) count++;
        if (isDetectSuccess()) count++;
        return count;
    }

    /**
     * 获取可读的处理时间
     */
    public String getReadableProcessingTime() {
        if (totalProcessingTime == null) {
            return "未知";
        }
        
        if (totalProcessingTime < 1000) {
            return totalProcessingTime + "ms";
        } else if (totalProcessingTime < 60000) {
            return String.format("%.1fs", totalProcessingTime / 1000.0);
        } else {
            int minutes = (int) (totalProcessingTime / 60000);
            int seconds = (int) ((totalProcessingTime % 60000) / 1000);
            return String.format("%dm %ds", minutes, seconds);
        }
    }

    /**
     * 获取处理摘要
     */
    public String getProcessingSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (isSuccess()) {
            summary.append("处理成功");
        } else {
            summary.append("处理失败");
        }
        
        summary.append(" (");
        summary.append(getSuccessfulAgentCount()).append("/2 智能体成功");
        summary.append(")");
        
        if (totalProcessingTime != null) {
            summary.append(" - 耗时: ").append(getReadableProcessingTime());
        }
        
        return summary.toString();
    }
}
