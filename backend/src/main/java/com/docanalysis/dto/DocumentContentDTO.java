package com.docanalysis.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


@Data
@Accessors(chain = true)
public class DocumentContentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * HTML内容
     */
    private String htmlContent;

    /**
     * 纯文本内容
     */
    private String textContent;

    /**
     * CSS样式
     */
    private String cssStyles;

    /**
     * 文档元数据
     */
    private Map<String, Object> metadata;

    /**
     * 文档元素列表
     */
    private List<DocumentElementDTO> elements;

    /**
     * 位置映射列表
     */
    private List<PositionMappingDTO> positionMappings;

    /**
     * 解析状态
     */
    private String parseStatus;

    /**
     * 解析时间（毫秒）
     */
    private Long parseTime;

    /**
     * 错误信息（如果有）
     */
    private String errorMessage;

    // ========== 页眉页码功能字段（可选增强功能） ==========


    private HeaderFooterInfo headerFooter;

    /**
     * 页面信息列表（可选）
     * 包含页码、位置映射、页眉页脚等信息
     */
    private List<PageInfo> pages;

    /**
     * 文档元素DTO
     */
    @Data
    @Accessors(chain = true)
    public static class DocumentElementDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String elementId;
        private String type;
        private String content;
        private Map<String, Object> style;
        private int startPosition;
        private int endPosition;
    }

    /**
     * 位置映射DTO
     */
    @Data
    @Accessors(chain = true)
    public static class PositionMappingDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        private String elementId;
        private int originalStart;
        private int originalEnd;
        private int htmlStart;
        private int htmlEnd;
        private String htmlElementId;
    }

    /**
     * 获取文档统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new java.util.HashMap<>();
        
        if (metadata != null) {
            stats.put("paragraphCount", metadata.get("paragraphCount"));
            stats.put("tableCount", metadata.get("tableCount"));
            stats.put("characterCount", metadata.get("characterCount"));
        }
        
        if (elements != null) {
            stats.put("elementCount", elements.size());
            
            // 统计各类型元素数量
            Map<String, Long> typeCount = elements.stream()
                    .collect(java.util.stream.Collectors.groupingBy(
                            DocumentElementDTO::getType,
                            java.util.stream.Collectors.counting()
                    ));
            stats.put("elementTypes", typeCount);
        }
        
        if (textContent != null) {
            stats.put("textLength", textContent.length());
        }
        
        if (htmlContent != null) {
            stats.put("htmlLength", htmlContent.length());
        }
        
        return stats;
    }

    /**
     * 检查解析是否成功
     */
    public boolean isParseSuccessful() {
        return "SUCCESS".equals(parseStatus) && htmlContent != null && !htmlContent.isEmpty();
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return "ERROR".equals(parseStatus) || (errorMessage != null && !errorMessage.isEmpty());
    }

    /**
     * 获取可读的解析时间
     */
    public String getReadableParseTime() {
        if (parseTime == null) {
            return "未知";
        }

        if (parseTime < 1000) {
            return parseTime + "ms";
        } else if (parseTime < 60000) {
            return String.format("%.1fs", parseTime / 1000.0);
        } else {
            int minutes = (int) (parseTime / 60000);
            int seconds = (int) ((parseTime % 60000) / 1000);
            return String.format("%dm %ds", minutes, seconds);
        }
    }

    // ========== 页眉页码功能便捷方法 ==========

    /**
     * 检查是否包含页眉页脚信息
     */
    public boolean hasHeaderFooter() {
        return headerFooter != null && headerFooter.hasAny();
    }

    /**
     * 检查是否包含页码信息
     */
    public boolean hasPages() {
        return pages != null && !pages.isEmpty();
    }

    /**
     * 获取页面数量
     */
    public int getPageCount() {
        return pages != null ? pages.size() : 0;
    }

    /**
     * 根据页码获取页面信息
     */
    public PageInfo getPageByNumber(int pageNumber) {
        if (pages == null || pageNumber < 1 || pageNumber > pages.size()) {
            return null;
        }
        return pages.stream()
                .filter(page -> page.getPageNumber() != null && page.getPageNumber() == pageNumber)
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据文档位置获取页码
     */
    public Integer getPageNumberByPosition(int position) {
        if (pages == null || pages.isEmpty()) {
            return null;
        }

        for (PageInfo page : pages) {
            if (page.getStartPosition() != null && page.getEndPosition() != null
                && position >= page.getStartPosition() && position <= page.getEndPosition()) {
                return page.getPageNumber();
            }
        }

        return null;
    }

    /**
     * 获取页眉页脚摘要信息
     */
    public String getHeaderFooterSummary() {
        if (!hasHeaderFooter()) {
            return "无页眉页脚";
        }

        StringBuilder summary = new StringBuilder();
        if (headerFooter.hasHeader()) {
            summary.append("页眉: ").append(headerFooter.getFullHeaderText());
        }
        if (headerFooter.hasFooter()) {
            if (summary.length() > 0) {
                summary.append(" | ");
            }
            summary.append("页脚: ").append(headerFooter.getFullFooterText());
        }

        return summary.toString();
    }

    /**
     * 获取页码信息摘要
     */
    public String getPagesSummary() {
        if (!hasPages()) {
            return "无页码信息";
        }

        int totalPages = getPageCount();
        long totalContent = pages.stream()
                .mapToLong(PageInfo::getContentLength)  // getContentLength()返回int，不需要null检查
                .sum();

        return String.format("共%d页，总内容长度%d字符", totalPages, totalContent);
    }

    /**
     * 检查页眉页码功能是否可用
     */
    public boolean isHeaderFooterFeatureAvailable() {
        return hasHeaderFooter() || hasPages();
    }
}
