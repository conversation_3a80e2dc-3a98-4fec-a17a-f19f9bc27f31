package com.docanalysis.service;

import com.docanalysis.dto.MaxKBResponseDTO;
import com.docanalysis.util.MaxKBApiClient;

import java.util.Map;
import java.util.concurrent.CompletableFuture;


public interface MaxKBService {

    /**
     * 分析文档
     * 
     * @param documentId 文档ID
     * @return 分析结果
     */
    CompletableFuture<MaxKBResponseDTO> analyzeDocument(Long documentId);

    /**
     * 分析文档（指定文件路径）
     * 
     * @param documentId 文档ID
     * @param filePath 文件路径
     * @return 分析结果
     */
    CompletableFuture<MaxKBResponseDTO> analyzeDocument(Long documentId, String filePath);

    /**
     * 重新分析文档
     * 
     * @param documentId 文档ID
     * @return 分析结果
     */
    CompletableFuture<MaxKBResponseDTO> reanalyzeDocument(Long documentId);

    /**
     * 检查分析状态
     * 
     * @param documentId 文档ID
     * @return 分析状态
     */
    String getAnalysisStatus(Long documentId);

    /**
     * 获取分析结果
     * 
     * @param documentId 文档ID
     * @return 分析结果
     */
    MaxKBResponseDTO getAnalysisResult(Long documentId);

    /**
     * 取消分析
     *
     * @param documentId 文档ID
     * @return 是否成功取消
     */
    boolean cancelAnalysis(Long documentId);

    /**
     * 分析文档（指定智能体类型）
     *
     * @param agentType 智能体类型
     * @param documentId 文档ID
     * @param filePath 文件路径
     * @return 分析结果
     */
    CompletableFuture<Map<String, Object>> analyzeDocument(MaxKBApiClient.AgentType agentType, Long documentId, String filePath);

    /**
     * 创建会话并上传文件
     *
     * @param agentType 智能体类型
     * @param filePath 文件路径
     * @return 包含chatId和fileId的Map
     */
    CompletableFuture<Map<String, String>> createSessionAndUploadFile(MaxKBApiClient.AgentType agentType, String filePath);

    /**
     * 发送分析请求
     *
     * @param agentType 智能体类型
     * @param chatId 会话ID
     * @param fileId 文件ID
     * @param message 分析消息
     * @return 分析结果
     */
    CompletableFuture<Map<String, Object>> sendAnalysisRequest(MaxKBApiClient.AgentType agentType, String chatId, String fileId, String message);

    String getMaxKbToken();

    String getQaAppId();
}
