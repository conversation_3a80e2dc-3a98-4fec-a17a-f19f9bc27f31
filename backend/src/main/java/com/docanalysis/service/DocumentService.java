package com.docanalysis.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.docanalysis.dto.DocumentContentDTO;
import com.docanalysis.entity.Document;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


public interface DocumentService extends IService<Document> {

    /**
     * 根据状态分页查询文档
     */
    IPage<Document> getDocumentsByStatus(Page<Document> page, Document.DocumentStatus status);

    /**
     * 根据状态和时间范围查询文档
     */
    List<Document> getDocumentsByStatusAndTimeRange(
            Document.DocumentStatus status,
            LocalDateTime startTime,
            LocalDateTime endTime
    );

    /**
     * 根据文件名模糊查询
     */
    List<Document> getDocumentsByFilename(String filename);

    /**
     * 查询指定大小范围的文档
     */
    List<Document> getDocumentsByFileSizeRange(Long minSize, Long maxSize);

    /**
     * 统计各状态的文档数量
     */
    Map<String, Long> getDocumentCountByStatus();

    /**
     * 查询最近上传的文档
     */
    List<Document> getRecentDocuments(Integer limit);

    /**
     * 查询处理失败的文档
     */
    List<Document> getFailedDocuments();

    /**
     * 查询处理中的文档
     */
    List<Document> getProcessingDocuments();

    /**
     * 查询已完成的文档
     */
    List<Document> getCompletedDocuments();

    /**
     * 根据文件路径查询文档
     */
    Document getDocumentByFilePath(String filePath);

    /**
     * 查询指定时间段内的文档统计信息
     */
    Map<String, Object> getDocumentStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询大文件（超过指定大小）
     */
    List<Document> getLargeFiles(Long sizeThreshold);

    /**
     * 查询过期文档
     */
    List<Document> getExpiredDocuments(Integer retentionDays);

    /**
     * 批量更新文档状态
     */
    boolean batchUpdateStatus(List<Long> documentIds, Document.DocumentStatus oldStatus, Document.DocumentStatus newStatus);

    /**
     * 查询文档处理成功率
     */
    Double getDocumentSuccessRate(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 复合条件查询文档
     */
    IPage<Document> getDocumentsByConditions(
            Page<Document> page,
            Document.DocumentStatus status,
            String filename,
            Long minSize,
            Long maxSize,
            LocalDateTime startTime,
            LocalDateTime endTime
    );

    /**
     * 创建新文档记录
     */
    Document createDocument(String filename, String originalName, String filePath, Long fileSize,String pdfFilename,String pdfFilePath);

    /**
     * 更新文档状态
     */
    boolean updateDocumentStatus(Long documentId, Document.DocumentStatus status);

    /**
     * 删除文档及相关数据
     */
    boolean deleteDocumentCompletely(Long documentId);

    /**
     * 检查文档是否存在
     */
    boolean existsDocument(Long documentId);

    /**
     * 检查文件路径是否已存在
     */
    boolean existsFilePath(String filePath);

    /**
     * 获取文档总数
     */
    Long getTotalDocumentCount();

    /**
     * 获取文档总大小
     */
    Long getTotalDocumentSize();

    /**
     * 清理过期文档
     */
    int cleanupExpiredDocuments(Integer retentionDays);

    /**
     * 解析文档内容
     */
    DocumentContentDTO parseDocumentContent(Long documentId);

    /**
     * 获取文档内容
     */
    DocumentContentDTO getDocumentContent(Long documentId);

    /**
     * 检查文档是否已解析
     */
    boolean isDocumentParsed(Long documentId);

    /**
     * 重新解析文档
     */
    DocumentContentDTO reparseDocument(Long documentId);
}
