package com.docanalysis.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.docanalysis.dto.MaxKBResponseDTO;
import com.docanalysis.entity.AnalysisResult;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


public interface AnalysisService {

    /**
     * 处理并存储MaxKB分析结果
     * 
     * @param documentId 文档ID
     * @param maxKBResponse MaxKB响应结果
     * @return 处理结果列表
     */
    List<AnalysisResult> processAndStoreResults(Long documentId, MaxKBResponseDTO maxKBResponse);

    /**
     * 解析并存储单个智能体结果
     * 
     * @param documentId 文档ID
     * @param agentResult 智能体结果
     * @param agentType 智能体类型
     * @return 分析结果
     */
    AnalysisResult processAgentResult(Long documentId, MaxKBResponseDTO.AgentResult agentResult, AnalysisResult.AgentType agentType);

    /**
     * 根据文档ID获取分析结果
     *
     * @param documentId 文档ID
     * @return 分析结果列表
     */
    List<AnalysisResult> getAnalysisResultsByDocumentId(Long documentId);

    /**
     * 根据文档ID和智能体类型获取分析结果
     * 
     * @param documentId 文档ID
     * @param agentType 智能体类型
     * @return 分析结果
     */
    AnalysisResult getAnalysisResult(Long documentId, AnalysisResult.AgentType agentType);

    /**
     * 分页查询分析结果
     * 
     * @param page 分页参数
     * @param documentId 文档ID（可选）
     * @param agentType 智能体类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 分页结果
     */
    IPage<AnalysisResult> getAnalysisResultsPage(
            Page<AnalysisResult> page,
            Long documentId,
            AnalysisResult.AgentType agentType,
            LocalDateTime startTime,
            LocalDateTime endTime
    );

    /**
     * 获取分析结果统计信息
     * 
     * @param documentId 文档ID（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 统计信息
     */
    Map<String, Object> getAnalysisStatistics(Long documentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除文档的所有分析结果
     * 
     * @param documentId 文档ID
     * @return 删除数量
     */
    int deleteAnalysisResultsByDocumentId(Long documentId);

    /**
     * 清理过期的分析结果
     * 
     * @param retentionDays 保留天数
     * @return 清理数量
     */
    int cleanupExpiredResults(Integer retentionDays);

    /**
     * 重新处理分析结果
     * 
     * @param documentId 文档ID
     * @param maxKBResponse 新的MaxKB响应结果
     * @return 处理结果列表
     */
    List<AnalysisResult> reprocessResults(Long documentId, MaxKBResponseDTO maxKBResponse);

    /**
     * 检查分析结果是否存在
     * 
     * @param documentId 文档ID
     * @param agentType 智能体类型
     * @return 是否存在
     */
    boolean existsAnalysisResult(Long documentId, AnalysisResult.AgentType agentType);

    /**
     * 获取最近的分析结果
     * 
     * @param limit 限制数量
     * @return 分析结果列表
     */
    List<AnalysisResult> getRecentAnalysisResults(Integer limit);
}
