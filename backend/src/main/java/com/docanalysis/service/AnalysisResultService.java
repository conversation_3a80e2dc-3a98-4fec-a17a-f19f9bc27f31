package com.docanalysis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.docanalysis.entity.AnalysisResult;

import java.util.List;

/**
 * AnalysisResultService - 分析结果服务接口
 */
public interface AnalysisResultService extends IService<AnalysisResult> {

    /**
     * 根据文档ID获取分析结果
     * 
     * @param documentId 文档ID
     * @return 分析结果列表
     */
    List<AnalysisResult> getByDocumentId(Long documentId);

    /**
     * 根据文档ID和智能体类型获取分析结果
     * 
     * @param documentId 文档ID
     * @param agentType 智能体类型
     * @return 分析结果
     */
    AnalysisResult getByDocumentIdAndAgentType(Long documentId, AnalysisResult.AgentType agentType);

    /**
     * 删除文档的所有分析结果
     * 
     * @param documentId 文档ID
     * @return 删除数量
     */
    int deleteByDocumentId(Long documentId);
}
