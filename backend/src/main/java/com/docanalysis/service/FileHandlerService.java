package com.docanalysis.service;


import com.docanalysis.config.ConfigConstants;
import com.docanalysis.dto.FileAttribute;
import com.docanalysis.dto.FileType;
import com.docanalysis.service.cache.CacheService;
import com.docanalysis.util.EncodingDetects;
import com.docanalysis.util.KkFileUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import org.apache.poi.EncryptedDocumentException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @date 2017/11/13
 */
@Component
@DependsOn(ConfigConstants.BEAN_NAME)
public class FileHandlerService implements InitializingBean {

    private static final String PDF2JPG_IMAGE_FORMAT = ".jpg";
    private static final String PDF_PASSWORD_MSG = "password";
    private final Logger logger = LoggerFactory.getLogger(FileHandlerService.class);
    private final String fileDir = ConfigConstants.getFileDir();
    private final CacheService cacheService;
    @Value("${server.tomcat.uri-encoding:UTF-8}")
    private String uriEncoding;

    public FileHandlerService(CacheService cacheService) {
        this.cacheService = cacheService;
    }

    /**
     * @return 已转换过的文件集合(缓存)
     */
    public Map<String, String> listConvertedFiles() {
        return cacheService.getPDFCache();
    }

    /**
     * @return 已转换过的文件，根据文件名获取
     */
    public String getConvertedFile(String key) {
        return cacheService.getPDFCache(key);
    }

    /**
     * @param key pdf本地路径
     * @return 已将pdf转换成图片的图片本地相对路径
     */
    public Integer getPdf2jpgCache(String key) {
        return cacheService.getPdfImageCache(key);
    }


    /**
     * 从路径中获取文件负
     *
     * @param path 类似这种：C:\Users\<USER>\Downloads
     * @return 文件名
     */
    public String getFileNameFromPath(String path) {
        return path.substring(path.lastIndexOf(File.separator) + 1);
    }

    /**
     * 获取相对路径
     *
     * @param absolutePath 绝对路径
     * @return 相对路径
     */
    public String getRelativePath(String absolutePath) {
        return absolutePath.substring(fileDir.length());
    }

    /**
     * 添加转换后PDF缓存
     *
     * @param fileName pdf文件名
     * @param value    缓存相对路径
     */
    public void addConvertedFile(String fileName, String value) {
        cacheService.putPDFCache(fileName, value);
    }

    /**
     * 添加转换后图片组缓存
     *
     * @param pdfFilePath pdf文件绝对路径
     * @param num         图片张数
     */
    public void addPdf2jpgCache(String pdfFilePath, int num) {
        cacheService.putPdfImageCache(pdfFilePath, num);
    }

    /**
     * 获取redis中压缩包内图片文件
     *
     * @param compressFileKey compressFileKey
     * @return 图片文件访问url列表
     */
    public List<String> getImgCache(String compressFileKey) {
        return cacheService.getImgCache(compressFileKey);
    }

    /**
     * 设置redis中压缩包内图片文件
     *
     * @param fileKey fileKey
     * @param imgs    图片文件访问url列表
     */
    public void putImgCache(String fileKey, List<String> imgs) {
        cacheService.putImgCache(fileKey, imgs);
    }

    /**
     * cad定义线程池
     */
    private ExecutorService pool = null;

    @Override
    public void afterPropertiesSet() throws Exception {
        pool = Executors.newFixedThreadPool(ConfigConstants.getCadThread());
    }

    /**
     * 对转换后的文件进行操作(改变编码方式)
     *
     * @param outFilePath 文件绝对路径
     */
    public void doActionConvertedFile(String outFilePath) {
        String charset = EncodingDetects.getJavaEncode(outFilePath);
        StringBuilder sb = new StringBuilder();
        try (InputStream inputStream = new FileInputStream(outFilePath); BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, charset))) {
            String line;
            while (null != (line = reader.readLine())) {
                if (line.contains("charset=gb2312")) {
                    line = line.replace("charset=gb2312", "charset=utf-8");
                }
                sb.append(line);
            }
            // 添加sheet控制头
            sb.append("<script src=\"js/jquery-3.6.1.min.js\" type=\"text/javascript\"></script>");
            sb.append("<script src=\"excel/excel.header.js\" type=\"text/javascript\"></script>");
            sb.append("<link rel=\"stylesheet\" href=\"excel/excel.css\">");
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 重新写入文件
        try (FileOutputStream fos = new FileOutputStream(outFilePath); BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(fos, StandardCharsets.UTF_8))) {
            writer.write(sb.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    /**
     * @param str    原字符串（待截取原串）
     * @param posStr 指定字符串
     * @return 截取截取指定字符串之后的数据
     */
    public static String getSubString(String str, String posStr) {
        return str.substring(str.indexOf(posStr) + posStr.length());
    }

    /**
     * 通过本地文件路径获取文件属性
     *
     * @param localFilePath 本地文件路径
     * @return 文件属性
     */
    public FileAttribute getFileAttributeFromLocalFile(String localFilePath) {
        FileAttribute attribute = new FileAttribute();
        String suffix;
        FileType type;
        String originFileName; //原始文件名
        String outFilePath; //生成文件的路径
        String originFilePath; //原始文件路径

        // 从本地文件路径中提取文件名
        File file = new File(localFilePath);
        if (!file.exists()) {
            logger.error("本地文件不存在: {}", localFilePath);
            return null;
        }

        originFileName = file.getName();
        type = FileType.typeFromFileName(originFileName);
        suffix = KkFileUtils.suffixFromFileName(originFileName);

        // 文件名处理 - 防止XSS攻击
        originFileName = KkFileUtils.htmlEscape(originFileName);

        // 判断是否为HTML视图类型的文件
        boolean isHtmlView = suffix.equalsIgnoreCase("xls") || suffix.equalsIgnoreCase("xlsx") ||
                suffix.equalsIgnoreCase("csv") || suffix.equalsIgnoreCase("xlsm") ||
                suffix.equalsIgnoreCase("xlt") || suffix.equalsIgnoreCase("xltm") ||
                suffix.equalsIgnoreCase("et") || suffix.equalsIgnoreCase("ett") ||
                suffix.equalsIgnoreCase("xlam");

        // 生成缓存文件前缀名
        String cacheFilePrefixName = null;
        try {
            cacheFilePrefixName = originFileName.substring(0, originFileName.lastIndexOf(".")) + suffix + ".";
        } catch (Exception e) {
            logger.error("获取文件名后缀错误：", e);
        }

        // 获取缓存文件名
        String cacheFileName = this.getCacheFileName(type, originFileName, cacheFilePrefixName, isHtmlView, false);
        outFilePath = fileDir + cacheFileName;
        originFilePath = localFilePath; // 使用传入的本地文件路径
        String cacheListName = cacheFilePrefixName + "ListName";

        // 设置文件属性
        attribute.setType(type);
        attribute.setName(originFileName);
        attribute.setCacheName(cacheFileName);
        attribute.setCacheListName(cacheListName);
        attribute.setHtmlView(isHtmlView);
        attribute.setOutFilePath(outFilePath);
        attribute.setOriginFilePath(originFilePath);
        attribute.setSuffix(suffix);
        attribute.setUrl("file://" + localFilePath); // 设置为file协议的URL

        // 本地文件不需要下载，直接跳过下载步骤
        attribute.setSkipDownLoad(true);

        // 设置默认的office预览类型
        attribute.setOfficePreviewType(ConfigConstants.getOfficePreviewType());

        return attribute;
    }


    /**
     * 通过本地文件路径获取文件属性（重载方法，支持额外参数）
     *
     * @param localFilePath     本地文件路径
     * @param officePreviewType office预览类型
     * @param forceUpdatedCache 是否强制更新缓存
     * @return 文件属性
     */
    public FileAttribute getFileAttributeFromLocalFile(String localFilePath, String officePreviewType, boolean forceUpdatedCache) {
        FileAttribute attribute = getFileAttributeFromLocalFile(localFilePath);
        if (attribute != null) {
            if (StringUtils.hasText(officePreviewType)) {
                attribute.setOfficePreviewType(officePreviewType);
            }
            if (forceUpdatedCache) {
                attribute.setForceUpdatedCache(true);
            }
        }
        return attribute;
    }

    /**
     * 获取缓存的文件名
     *
     * @return 文件名
     */
    private String getCacheFileName(FileType type, String originFileName, String cacheFilePrefixName, boolean isHtmlView, boolean isCompressFile) {
        String cacheFileName;
        if (type.equals(FileType.OFFICE)) {
            cacheFileName = cacheFilePrefixName + (isHtmlView ? "html" : "pdf"); //生成文件添加类型后缀 防止同名文件
        } else if (type.equals(FileType.PDF)) {
            cacheFileName = originFileName;
        } else if (type.equals(FileType.MEDIACONVERT)) {
            cacheFileName = cacheFilePrefixName + "mp4";
        } else if (type.equals(FileType.CAD)) {
            String cadPreviewType = ConfigConstants.getCadPreviewType();
            cacheFileName = cacheFilePrefixName + cadPreviewType; //生成文件添加类型后缀 防止同名文件
        } else if (type.equals(FileType.COMPRESS)) {
            cacheFileName = originFileName;
        } else if (type.equals(FileType.TIFF)) {
            cacheFileName = cacheFilePrefixName + ConfigConstants.getTifPreviewType();
        } else {
            cacheFileName = originFileName;
        }
        if (isCompressFile) {  //判断是否使用特定压缩包符号
            cacheFileName = "_decompression" + cacheFileName;
        }
        return cacheFileName;
    }

    /**
     * @return 已转换过的视频文件集合(缓存)
     */
    public Map<String, String> listConvertedMedias() {
        return cacheService.getMediaConvertCache();
    }

    /**
     * 添加转换后的视频文件缓存
     *
     * @param fileName
     * @param value
     */
    public void addConvertedMedias(String fileName, String value) {
        cacheService.putMediaConvertCache(fileName, value);
    }

    /**
     * @return 已转换视频文件缓存，根据文件名获取
     */
    public String getConvertedMedias(String key) {
        return cacheService.getMediaConvertCache(key);
    }
}
