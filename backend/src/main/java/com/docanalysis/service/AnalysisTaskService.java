package com.docanalysis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.docanalysis.entity.AnalysisTask;
import com.docanalysis.util.MaxKBApiClient;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


public interface AnalysisTaskService extends IService<AnalysisTask> {

    /**
     * 创建分析任务
     * 
     * @param documentId 文档ID
     * @param agentType 智能体类型
     * @return 创建的任务
     */
    AnalysisTask createTask(Long documentId, MaxKBApiClient.AgentType agentType);

    /**
     * 批量创建分析任务（为文档创建所有类型的分析任务）
     * 
     * @param documentId 文档ID
     * @return 创建的任务列表
     */
    List<AnalysisTask> createAllTasksForDocument(Long documentId);

    /**
     * 启动分析任务
     * 
     * @param taskId 任务ID
     * @param chatId MaxKB会话ID
     * @param fileId MaxKB文件ID
     * @return 异步任务结果
     */
    CompletableFuture<AnalysisTask> startTask(Long taskId, String chatId, String fileId);

    /**
     * 更新任务进度
     * 
     * @param taskId 任务ID
     * @param progress 进度百分比(0-100)
     * @param message 状态消息
     */
    void updateTaskProgress(Long taskId, int progress, String message);

    /**
     * 标记任务完成
     * 
     * @param taskId 任务ID
     * @param resultId 分析结果ID
     * @param message 完成消息
     */
    void markTaskCompleted(Long taskId, Long resultId, String message);

    /**
     * 标记任务失败
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     */
    void markTaskFailed(Long taskId, String errorMessage);

    /**
     * 获取文档的所有分析任务
     * 
     * @param documentId 文档ID
     * @return 任务列表
     */
    List<AnalysisTask> getTasksByDocumentId(Long documentId);

    /**
     * 获取文档的特定类型任务
     * 
     * @param documentId 文档ID
     * @param agentType 智能体类型
     * @return 任务对象
     */
    AnalysisTask getTaskByDocumentIdAndType(Long documentId, MaxKBApiClient.AgentType agentType);

    /**
     * 获取文档分析进度概览
     * 
     * @param documentId 文档ID
     * @return 进度概览信息
     */
    Map<String, Object> getDocumentAnalysisOverview(Long documentId);

    /**
     * 获取文档整体分析状态
     * 
     * @param documentId 文档ID
     * @return 整体状态信息
     */
    Map<String, Object> getDocumentOverallStatus(Long documentId);

    /**
     * 获取任务详细进度信息
     * 
     * @param documentId 文档ID
     * @return 详细进度信息列表
     */
    List<Map<String, Object>> getTaskProgressDetails(Long documentId);

    /**
     * 检查文档是否有活跃的任务
     * 
     * @param documentId 文档ID
     * @return 是否有活跃任务
     */
    boolean hasActiveTasksForDocument(Long documentId);

    /**
     * 获取所有进行中的任务
     * 
     * @return 进行中的任务列表
     */
    List<AnalysisTask> getProcessingTasks();

    /**
     * 获取等待中的任务
     * 
     * @return 等待中的任务列表
     */
    List<AnalysisTask> getPendingTasks();

    /**
     * 获取任务统计信息
     * 
     * @param documentId 文档ID
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics(Long documentId);

    /**
     * 获取最近的任务列表（用于监控）
     * 
     * @param hours 小时数
     * @param limit 限制数量
     * @return 最近任务列表
     */
    List<Map<String, Object>> getRecentTasks(int hours, int limit);

    /**
     * 重试失败的任务
     * 
     * @param taskId 任务ID
     * @return 异步任务结果
     */
    CompletableFuture<AnalysisTask> retryTask(Long taskId);

    /**
     * 取消任务
     * 
     * @param taskId 任务ID
     */
    void cancelTask(Long taskId);

    /**
     * 清理过期的已完成任务
     * 
     * @param days 保留天数
     * @return 清理的任务数量
     */
    int cleanupExpiredTasks(int days);

    /**
     * 异步启动文档的所有分析任务
     * 
     * @param documentId 文档ID
     * @param filePath 文档文件路径
     * @return 异步任务结果Map，key为AgentType，value为CompletableFuture<AnalysisTask>
     */
    Map<MaxKBApiClient.AgentType, CompletableFuture<AnalysisTask>> startAllAnalysisTasksAsync(Long documentId, String filePath);

    /**
     * 检查并更新超时的任务
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 更新的任务数量
     */
    int checkAndUpdateTimeoutTasks(int timeoutMinutes);

    /**
     * 获取任务执行状态摘要
     * 
     * @return 状态摘要信息
     */
    Map<String, Object> getTaskExecutionSummary();
}
