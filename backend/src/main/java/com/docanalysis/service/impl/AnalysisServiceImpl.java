package com.docanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.docanalysis.dto.MaxKBResponseDTO;
import com.docanalysis.entity.AnalysisResult;
import com.docanalysis.mapper.AnalysisResultMapper;
import com.docanalysis.service.AnalysisService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AnalysisServiceImpl extends ServiceImpl<AnalysisResultMapper, AnalysisResult> implements AnalysisService {

    private final ObjectMapper objectMapper;

    @Override
    public List<AnalysisResult> processAndStoreResults(Long documentId, MaxKBResponseDTO maxKBResponse) {
        log.info("开始处理并存储分析结果: documentId={}", documentId);
        
        List<AnalysisResult> results = new ArrayList<>();
        
        try {
            // 处理提取智能体结果
            if (maxKBResponse.getExtractResult() != null) {
                AnalysisResult extractResult = processAgentResult(
                        documentId, 
                        maxKBResponse.getExtractResult(), 
                        AnalysisResult.AgentType.EXTRACT
                );
                if (extractResult != null) {
                    results.add(extractResult);
                }
            }
            
            // 处理检测智能体结果
            if (maxKBResponse.getDetectResult() != null) {
                AnalysisResult detectResult = processAgentResult(
                        documentId, 
                        maxKBResponse.getDetectResult(), 
                        AnalysisResult.AgentType.DETECT
                );
                if (detectResult != null) {
                    results.add(detectResult);
                }
            }
            
            log.info("分析结果处理完成: documentId={}, 结果数量={}", documentId, results.size());
            return results;
            
        } catch (Exception e) {
            log.error("处理分析结果失败: documentId={}", documentId, e);
            throw new RuntimeException("处理分析结果失败", e);
        }
    }

    @Override
    public AnalysisResult processAgentResult(Long documentId, MaxKBResponseDTO.AgentResult agentResult, AnalysisResult.AgentType agentType) {
        log.info("处理{}智能体结果: documentId={}", agentType.getDescription(), documentId);
        
        try {
            // 删除已存在的同类型结果
            QueryWrapper<AnalysisResult> deleteWrapper = new QueryWrapper<>();
            deleteWrapper.eq("document_id", documentId)
                        .eq("agent_type", agentType);
            remove(deleteWrapper);
            
            // 解析结果内容
            Map<String, Object> processedContent = parseAgentResultContent(agentResult, agentType);
            
            // 创建分析结果实体
            AnalysisResult analysisResult = new AnalysisResult()
                    .setDocumentId(documentId)
                    .setAgentType(agentType)
                    .setResultContent(processedContent)
                    .setProcessingTime(agentResult.getProcessingTime() != null ? 
                                     agentResult.getProcessingTime().intValue() : null)
                    .setApiResponseCode(extractApiResponseCode(agentResult.getRawResponse()));
            
            // 保存到数据库
            boolean saved = save(analysisResult);
            if (saved) {
                log.info("{}智能体结果保存成功: documentId={}, resultId={}", 
                        agentType.getDescription(), documentId, analysisResult.getId());
                return analysisResult;
            } else {
                log.error("{}智能体结果保存失败: documentId={}", agentType.getDescription(), documentId);
                return null;
            }
            
        } catch (Exception e) {
            log.error("处理{}智能体结果失败: documentId={}", agentType.getDescription(), documentId, e);
            throw new RuntimeException("处理智能体结果失败", e);
        }
    }

    @Override
    @Cacheable(value = "analysisResults", key = "#documentId")
    public List<AnalysisResult> getAnalysisResultsByDocumentId(Long documentId) {
        return baseMapper.selectByDocumentId(documentId);
    }

    @Override
    @Cacheable(value = "analysisResults", key = "#documentId + '_' + #agentType")
    public AnalysisResult getAnalysisResult(Long documentId, AnalysisResult.AgentType agentType) {
        return baseMapper.selectLatestByDocumentIdAndAgentType(documentId, agentType);
    }

    @Override
    public IPage<AnalysisResult> getAnalysisResultsPage(Page<AnalysisResult> page, Long documentId, 
                                                       AnalysisResult.AgentType agentType, 
                                                       LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectByConditions(page, documentId, agentType, null, null, 
                                            null, null, startTime, endTime);
    }

    @Override
    public Map<String, Object> getAnalysisStatistics(Long documentId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        if (documentId != null) {
            // 单个文档的统计
            Map<String, Object> docStats = baseMapper.getAnalysisCompletionStatus(documentId);
            statistics.putAll(docStats);
        } else {
            // 全局统计
            Map<String, Object> globalStats = baseMapper.getStatisticsByTimeRange(startTime, endTime);
            statistics.putAll(globalStats);
            
            // 各智能体类型统计
            List<Map<String, Object>> typeStats = baseMapper.countByAgentType();
            statistics.put("agentTypeStats", typeStats);
        }
        
        return statistics;
    }

    @Override
    @CacheEvict(value = "analysisResults", key = "#documentId")
    public int deleteAnalysisResultsByDocumentId(Long documentId) {
        QueryWrapper<AnalysisResult> wrapper = new QueryWrapper<>();
        wrapper.eq("document_id", documentId);
        
        List<AnalysisResult> results = list(wrapper);
        boolean removed = remove(wrapper);
        
        int deletedCount = removed ? results.size() : 0;
        log.info("删除文档分析结果: documentId={}, 删除数量={}", documentId, deletedCount);
        
        return deletedCount;
    }

    @Override
    public int cleanupExpiredResults(Integer retentionDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(retentionDays);
        int deletedCount = baseMapper.deleteExpiredResults(expireTime);
        
        log.info("清理过期分析结果: 保留天数={}, 清理数量={}", retentionDays, deletedCount);
        return deletedCount;
    }

    @Override
    @CacheEvict(value = "analysisResults", key = "#documentId")
    public List<AnalysisResult> reprocessResults(Long documentId, MaxKBResponseDTO maxKBResponse) {
        log.info("重新处理分析结果: documentId={}", documentId);
        
        // 删除现有结果
        deleteAnalysisResultsByDocumentId(documentId);
        
        // 重新处理并存储
        return processAndStoreResults(documentId, maxKBResponse);
    }

    @Override
    public boolean existsAnalysisResult(Long documentId, AnalysisResult.AgentType agentType) {
        QueryWrapper<AnalysisResult> wrapper = new QueryWrapper<>();
        wrapper.eq("document_id", documentId)
               .eq("agent_type", agentType);
        
        return count(wrapper) > 0;
    }

    @Override
    public List<AnalysisResult> getRecentAnalysisResults(Integer limit) {
        QueryWrapper<AnalysisResult> wrapper = new QueryWrapper<>();
        wrapper.orderByDesc("created_time")
               .last("LIMIT " + limit);
        
        return list(wrapper);
    }

    /**
     * 解析智能体结果内容
     */
    private Map<String, Object> parseAgentResultContent(MaxKBResponseDTO.AgentResult agentResult, 
                                                       AnalysisResult.AgentType agentType) {
        Map<String, Object> processedContent = new HashMap<>();
        
        try {
            // 基础信息
            processedContent.put("agentType", agentType.name());
            processedContent.put("status", agentResult.getStatus());
            processedContent.put("content", agentResult.getContent());
            processedContent.put("rawResponse", agentResult.getRawResponse());
            
            // 解析具体内容
            if ("SUCCESS".equals(agentResult.getStatus()) && agentResult.getContent() != null) {
                if (agentType == AnalysisResult.AgentType.EXTRACT) {
                    processedContent.put("extractedData", parseExtractedData(agentResult.getContent()));
                } else if (agentType == AnalysisResult.AgentType.DETECT) {
                    processedContent.put("detectedIssues", parseDetectedIssues(agentResult.getContent()));
                }
            }
            
            // 文件信息
            if (agentResult.getFileInfo() != null) {
                processedContent.put("fileInfo", agentResult.getFileInfo());
            }
            
            // 处理时间
            if (agentResult.getProcessingTime() != null) {
                processedContent.put("processingTime", agentResult.getProcessingTime());
            }
            
        } catch (Exception e) {
            log.error("解析智能体结果内容失败: agentType={}", agentType, e);
            processedContent.put("parseError", e.getMessage());
        }
        
        return processedContent;
    }

    /**
     * 解析提取的数据
     */
    private List<Map<String, Object>> parseExtractedData(String content) {
        List<Map<String, Object>> extractedData = new ArrayList<>();
        
        try {
            // TODO: 根据实际的MaxKB响应格式来解析提取的数据
            // 这里是示例实现，需要根据实际格式调整
            
            // 简单的文本解析示例
            if (content != null && !content.trim().isEmpty()) {
                Map<String, Object> item = new HashMap<>();
                item.put("type", "text");
                item.put("content", content);
                item.put("confidence", 0.9);
                extractedData.add(item);
            }
            
        } catch (Exception e) {
            log.error("解析提取数据失败", e);
        }
        
        return extractedData;
    }

    /**
     * 解析检测的问题
     */
    private List<Map<String, Object>> parseDetectedIssues(String content) {
        List<Map<String, Object>> detectedIssues = new ArrayList<>();
        
        try {
            // TODO: 根据实际的MaxKB响应格式来解析检测的问题
            // 这里是示例实现，需要根据实际格式调整
            
            // 简单的文本解析示例
            if (content != null && !content.trim().isEmpty()) {
                Map<String, Object> issue = new HashMap<>();
                issue.put("type", "general");
                issue.put("description", content);
                issue.put("severity", "medium");
                detectedIssues.add(issue);
            }
            
        } catch (Exception e) {
            log.error("解析检测问题失败", e);
        }
        
        return detectedIssues;
    }

    /**
     * 提取API响应状态码
     */
    private Integer extractApiResponseCode(Map<String, Object> rawResponse) {
        if (rawResponse != null) {
            Object code = rawResponse.get("code");
            if (code instanceof Number) {
                return ((Number) code).intValue();
            }
        }
        return 200; // 默认成功
    }
}
