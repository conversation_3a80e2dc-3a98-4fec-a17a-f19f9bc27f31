package com.docanalysis.service.impl;

import com.docanalysis.dto.StructuredAnalysisResultDTO;
import com.docanalysis.entity.AnalysisResult;
import com.docanalysis.service.AnalysisContentParserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AnalysisContentParserServiceImpl implements AnalysisContentParserService {

    @Override
    public StructuredAnalysisResultDTO parseToStructuredResult(Long documentId, List<AnalysisResult> analysisResults) {
        log.info("开始解析结构化分析结果: documentId={}, 结果数量={}", documentId, analysisResults.size());

        if (analysisResults.isEmpty()) {
            return createEmptyResult(documentId);
        }

        // 分离提取和检测结果
        List<AnalysisResult> extractResults = analysisResults.stream()
                .filter(result -> result.getAgentType() == AnalysisResult.AgentType.EXTRACT)
                .collect(Collectors.toList());

        List<AnalysisResult> detectResults = analysisResults.stream()
                .filter(result -> result.getAgentType() == AnalysisResult.AgentType.DETECT)
                .collect(Collectors.toList());

        // 解析提取结果
        List<StructuredAnalysisResultDTO.ExtractItem> extractItems = new ArrayList<>();
        for (AnalysisResult result : extractResults) {
            extractItems.addAll(parseExtractResult(result));
        }

        // 解析检测结果
        List<StructuredAnalysisResultDTO.DetectItem> detectItems = new ArrayList<>();
        for (AnalysisResult result : detectResults) {
            detectItems.addAll(parseDetectResult(result));
        }

        // 计算统计信息
        StructuredAnalysisResultDTO.Statistics statistics = calculateStatistics(extractItems, detectItems);



        // 确定处理状态
        String status = determineProcessingStatus(extractResults, detectResults);

        return StructuredAnalysisResultDTO.builder()
                .documentId(documentId)
                .extractItems(extractItems)
                .detectItems(detectItems)
                .statistics(statistics)
                .status(status)
                .build();
    }

    @Override
    public List<StructuredAnalysisResultDTO.ExtractItem> parseExtractResult(AnalysisResult analysisResult) {
        List<StructuredAnalysisResultDTO.ExtractItem> items = new ArrayList<>();

        try {
            Map<String, Object> resultContent = analysisResult.getResultContent();
            if (resultContent == null) {
                log.warn("分析结果内容为空: resultId={}", analysisResult.getId());
                return items;
            }

            // 尝试从content字段获取文本内容
            String content = extractContentText(resultContent);
            if (content != null && !content.trim().isEmpty()) {
                items.addAll(parseExtractContent(content));
            }

            log.info("解析提取结果完成: resultId={}, 提取项数量={}", analysisResult.getId(), items.size());

        } catch (Exception e) {
            log.error("解析提取结果失败: resultId={}", analysisResult.getId(), e);
        }

        return items;
    }

    @Override
    public List<StructuredAnalysisResultDTO.DetectItem> parseDetectResult(AnalysisResult analysisResult) {
        List<StructuredAnalysisResultDTO.DetectItem> items = new ArrayList<>();

        try {
            Map<String, Object> resultContent = analysisResult.getResultContent();
            if (resultContent == null) {
                log.warn("分析结果内容为空: resultId={}", analysisResult.getId());
                return items;
            }

            // 尝试从content字段获取文本内容
            String content = extractContentText(resultContent);
            if (content != null && !content.trim().isEmpty()) {
                items.addAll(parseDetectContent(content));
            }

            log.info("解析检测结果完成: resultId={}, 检测项数量={}", analysisResult.getId(), items.size());

        } catch (Exception e) {
            log.error("解析检测结果失败: resultId={}", analysisResult.getId(), e);
        }

        return items;
    }

    @Override
    public List<StructuredAnalysisResultDTO.ExtractItem> parseExtractContent(String content) {
        List<StructuredAnalysisResultDTO.ExtractItem> items = new ArrayList<>();

        try {
            // 按行分割内容
            String[] lines = content.split("\n");
            StructuredAnalysisResultDTO.ExtractItem currentItem = null;
            List<String> currentItemLines = new ArrayList<>();
            int itemIndex = 0;

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // 检查是否是新的提取点（格式：数字、提取点名称：xxx）
                if (line.matches("^\\d+、提取点名称：.*")) {
                    // 保存上一个项目
                    if (currentItem != null) {
                        parseSourceReferences(currentItem, currentItemLines);
                        items.add(currentItem);
                    }

                    // 开始新项目
                    itemIndex++;
                    String title = line.replaceFirst("^\\d+、提取点名称：", "");
                    currentItem = StructuredAnalysisResultDTO.ExtractItem.builder()
                            .id("extract_" + itemIndex)
                            .title(title)
                            .status("success")
                            .importance("normal")
                            .sources(new ArrayList<>())
                            .build();
                    currentItemLines = new ArrayList<>();

                } else if (currentItem != null) {
                    // 收集当前项目的所有行
                    currentItemLines.add(line);
                }
            }

            // 保存最后一个项目
            if (currentItem != null) {
                parseSourceReferences(currentItem, currentItemLines);
                items.add(currentItem);
            }

            log.info("解析提取内容完成，共{}个提取点", items.size());

        } catch (Exception e) {
            log.error("解析提取内容失败", e);
        }

        return items;
    }

    @Override
    public List<StructuredAnalysisResultDTO.DetectItem> parseDetectContent(String content) {
        List<StructuredAnalysisResultDTO.DetectItem> items = new ArrayList<>();

        try {
            // 按行分割内容
            String[] lines = content.split("\n");
            StructuredAnalysisResultDTO.DetectItem currentItem = null;
            List<String> currentItemLines = new ArrayList<>();
            int itemIndex = 0;

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // 检查是否是新的检测点（格式：数字、检测点名称：xxx）
                if (line.matches("^\\d+、检测点名称：.*")) {
                    // 保存上一个项目
                    if (currentItem != null) {
                        parseDetectItemDetails(currentItem, currentItemLines);
                        items.add(currentItem);
                    }

                    // 开始新项目
                    itemIndex++;
                    String title = line.replaceFirst("^\\d+、检测点名称：", "");
                    currentItem = StructuredAnalysisResultDTO.DetectItem.builder()
                            .id("detect_" + itemIndex)
                            .title(title)
                            .status("detected")
                            .severity("medium")
                            .sources(new ArrayList<>())
                            .build();
                    currentItemLines = new ArrayList<>();

                } else if (currentItem != null) {
                    // 收集当前项目的所有行
                    currentItemLines.add(line);
                }
            }

            // 保存最后一个项目
            if (currentItem != null) {
                parseDetectItemDetails(currentItem, currentItemLines);
                items.add(currentItem);
            }

            log.info("解析检测内容完成，共{}个检测点", items.size());

        } catch (Exception e) {
            log.error("解析检测内容失败", e);
        }

        return items;
    }

    /**
     * 从结果内容中提取文本内容
     */
    private String extractContentText(Map<String, Object> resultContent) {
        try {
            // 直接从content字段获取
            Object contentObj = resultContent.get("content");
            if (contentObj instanceof String) {
                return (String) contentObj;
            }

            // 如果content是Map，尝试获取其中的文本
            if (contentObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> contentMap = (Map<String, Object>) contentObj;
                Object textObj = contentMap.get("content");
                if (textObj instanceof String) {
                    return (String) textObj;
                }
            }

            log.warn("无法从结果内容中提取文本: {}", resultContent);
            return null;

        } catch (Exception e) {
            log.error("提取文本内容失败", e);
            return null;
        }
    }

    /**
     * 计算统计信息
     */
    private StructuredAnalysisResultDTO.Statistics calculateStatistics(
            List<StructuredAnalysisResultDTO.ExtractItem> extractItems,
            List<StructuredAnalysisResultDTO.DetectItem> detectItems) {

        int totalExtractItems = extractItems.size();
        int totalDetectItems = detectItems.size();

        int successfulExtracts = (int) extractItems.stream()
                .filter(item -> "success".equals(item.getStatus()))
                .count();

        int abnormalDetects = (int) detectItems.stream()
                .filter(item -> "异常结果".equals(item.getResult()))
                .count();

        int normalDetects = (int) detectItems.stream()
                .filter(item -> "正常结果".equals(item.getResult()))
                .count();

        int highSeverityDetects = (int) detectItems.stream()
                .filter(item -> "high".equals(item.getSeverity()))
                .count();

        int mediumSeverityDetects = (int) detectItems.stream()
                .filter(item -> "medium".equals(item.getSeverity()))
                .count();

        int lowSeverityDetects = (int) detectItems.stream()
                .filter(item -> "low".equals(item.getSeverity()))
                .count();

        // 计算完成度
        double completionPercentage = 0.0;
        if (totalExtractItems > 0 || totalDetectItems > 0) {
            completionPercentage = ((double) (successfulExtracts + normalDetects + abnormalDetects)) / 
                                  (totalExtractItems + totalDetectItems) * 100.0;
        }

        return StructuredAnalysisResultDTO.Statistics.builder()
                .totalExtractItems(totalExtractItems)
                .totalDetectItems(totalDetectItems)
                .successfulExtracts(successfulExtracts)
                .abnormalDetects(abnormalDetects)
                .normalDetects(normalDetects)
                .highSeverityDetects(highSeverityDetects)
                .mediumSeverityDetects(mediumSeverityDetects)
                .lowSeverityDetects(lowSeverityDetects)
                .processingStatus("COMPLETED")
                .completionPercentage(completionPercentage)
                .build();
    }

    /**
     * 创建空结果
     */
    private StructuredAnalysisResultDTO createEmptyResult(Long documentId) {
        return StructuredAnalysisResultDTO.builder()
                .documentId(documentId)
                .extractItems(new ArrayList<>())
                .detectItems(new ArrayList<>())
                .statistics(StructuredAnalysisResultDTO.Statistics.builder()
                        .totalExtractItems(0)
                        .totalDetectItems(0)
                        .successfulExtracts(0)
                        .abnormalDetects(0)
                        .normalDetects(0)
                        .highSeverityDetects(0)
                        .mediumSeverityDetects(0)
                        .lowSeverityDetects(0)
                        .processingStatus("NO_DATA")
                        .completionPercentage(0.0)
                        .build())
                .lastUpdated(LocalDateTime.now())
                .status("NO_DATA")
                .build();
    }

    /**
     * 解析检测项详细信息
     */
    private void parseDetectItemDetails(StructuredAnalysisResultDTO.DetectItem item, List<String> lines) {
        List<StructuredAnalysisResultDTO.SourceReference> sources = new ArrayList<>();
        StringBuilder fullContentBuilder = new StringBuilder();

        StructuredAnalysisResultDTO.SourceReference currentSource = null;
        StringBuilder sourceContentBuilder = new StringBuilder();

        String result = null;
        String reason = null;
        boolean inSourceContent = false;

        for (String line : lines) {
            // 添加到完整内容
            if (fullContentBuilder.length() > 0) {
                fullContentBuilder.append("\n");
            }
            fullContentBuilder.append(line);

            // 检查特殊字段
            if (line.startsWith("检测结果：")) {
                result = line.replaceFirst("^检测结果：", "").trim();
                inSourceContent = false;
            } else if (line.startsWith("判断原因：")) {
                reason = line.replaceFirst("^判断原因：", "").trim();
                inSourceContent = false;
            } else if (line.equals("原文内容：")) {
                inSourceContent = true;
                // 保存上一个引用
                if (currentSource != null) {
                    currentSource.setContent(sourceContentBuilder.toString().trim());
                    sources.add(currentSource);
                    currentSource = null;
                }
            } else if (inSourceContent && line.matches("^第.+处（.+）：.*")) {
                // 保存上一个引用
                if (currentSource != null) {
                    currentSource.setContent(sourceContentBuilder.toString().trim());
                    sources.add(currentSource);
                }

                // 解析新引用 - 将referenceIndex和location合并到content中
                String referenceIndex = extractReferenceIndex(line);
                String location = extractLocation(line);
                String originalContent = extractReferenceContent(line);

                // 构建完整的content，包含referenceIndex和location信息
                String fullContent = referenceIndex + "（" + location + "）：" + originalContent;

                currentSource = StructuredAnalysisResultDTO.SourceReference.builder()
                        .referenceIndex(referenceIndex)
                        .location(location)
                        .content(fullContent)
                        .build();

                sourceContentBuilder = new StringBuilder(fullContent);

            } else if (currentSource != null && inSourceContent && !line.startsWith("检测结果：") && !line.startsWith("判断原因：")) {
                // 继续添加到当前引用的内容
                if (sourceContentBuilder.length() > 0) {
                    sourceContentBuilder.append("\n");
                }
                sourceContentBuilder.append(line);
            }
        }

        // 保存最后一个引用
        if (currentSource != null) {
            currentSource.setContent(sourceContentBuilder.toString().trim());
            sources.add(currentSource);
        }

        // 如果没有找到标准格式的引用，但有原文内容，创建默认引用
        if (sources.isEmpty() && inSourceContent) {
            List<String> sourceLines = new ArrayList<>();
            boolean collectingSource = false;
            for (String line : lines) {
                if (line.equals("原文内容：")) {
                    collectingSource = true;
                    continue;
                }
                if (collectingSource && !line.startsWith("检测结果：") && !line.startsWith("判断原因：")) {
                    sourceLines.add(line);
                } else if (line.startsWith("检测结果：") || line.startsWith("判断原因：")) {
                    break;
                }
            }

            if (!sourceLines.isEmpty()) {
                String allContent = String.join("\n", sourceLines).trim();
                String fullContent = "第一处（未指定位置）：" + allContent;

                StructuredAnalysisResultDTO.SourceReference singleSource =
                    StructuredAnalysisResultDTO.SourceReference.builder()
                        .referenceIndex("第一处")
                        .location("未指定位置")
                        .content(fullContent)
                        .build();
                sources.add(singleSource);
            }
        }

        // 设置检测项属性
        item.setSources(sources);
        item.setContent(fullContentBuilder.toString().trim());
        item.setResult(result);
        item.setReason(reason);

        // 根据检测结果设置严重性和状态
        if ("异常结果".equals(result)) {
            item.setSeverity("high");
            item.setStatus("detected");
        } else if ("正常结果".equals(result)) {
            item.setSeverity("low");
            item.setStatus("normal");
        }
    }

    /**
     * 解析原文引用
     */
    private void parseSourceReferences(StructuredAnalysisResultDTO.ExtractItem item, List<String> lines) {
        List<StructuredAnalysisResultDTO.SourceReference> sources = new ArrayList<>();
        StringBuilder fullContentBuilder = new StringBuilder();

        StructuredAnalysisResultDTO.SourceReference currentSource = null;
        StringBuilder sourceContentBuilder = new StringBuilder();

        for (String line : lines) {
            // 添加到完整内容
            if (fullContentBuilder.length() > 0) {
                fullContentBuilder.append("\n");
            }
            fullContentBuilder.append(line);

            // 检查是否是新的引用（格式：第X处（位置描述）：内容）
            if (line.matches("^第.+处（.+）：.*")) {
                // 保存上一个引用
                if (currentSource != null) {
                    currentSource.setContent(sourceContentBuilder.toString().trim());
                    sources.add(currentSource);
                }

                // 解析新引用 - 将referenceIndex和location合并到content中
                String referenceIndex = extractReferenceIndex(line);
                String location = extractLocation(line);
                String originalContent = extractReferenceContent(line);

                // 构建完整的content，包含referenceIndex和location信息
//                String fullContent = referenceIndex + "（" + location + "）：" + originalContent;

                currentSource = StructuredAnalysisResultDTO.SourceReference.builder()
                        .referenceIndex(referenceIndex)
                        .location(location)
                        .content(originalContent)  // 包含完整信息的content
                        .build();

                sourceContentBuilder = new StringBuilder(originalContent);

            } else if (currentSource != null && !line.startsWith("原文内容如下：")) {
                // 继续添加到当前引用的内容
                if (sourceContentBuilder.length() > 0) {
                    sourceContentBuilder.append("\n");
                }
                sourceContentBuilder.append(line);
            }
        }

        // 保存最后一个引用
        if (currentSource != null) {
            currentSource.setContent(sourceContentBuilder.toString().trim());
            sources.add(currentSource);
        }

        // 如果没有找到标准格式的引用，将整个内容作为一个引用
        if (sources.isEmpty() && !lines.isEmpty()) {
            String allContent = String.join("\n", lines);
            if (!allContent.trim().isEmpty() && !allContent.equals("原文内容如下：")) {
                // 对于非标准格式，也构建完整的content
                String fullContent = "第一处（未指定位置）：" + allContent.trim();

                StructuredAnalysisResultDTO.SourceReference singleSource =
                    StructuredAnalysisResultDTO.SourceReference.builder()
                        .referenceIndex("第一处")
                        .location("未指定位置")
                        .content(fullContent)  // 包含完整信息的content
                        .build();
                sources.add(singleSource);
            }
        }

        item.setSources(sources);
        item.setContent(fullContentBuilder.toString().trim());
    }

    /**
     * 提取引用序号
     */
    private String extractReferenceIndex(String line) {
        if (line.matches("^第.+处（.*")) {
            int endIndex = line.indexOf("处");
            if (endIndex > 0) {
                return line.substring(0, endIndex + 1);
            }
        }
        return "第一处";
    }

    /**
     * 提取位置描述
     */
    private String extractLocation(String line) {
        int startIndex = line.indexOf("（");
        int endIndex = line.indexOf("）");
        if (startIndex > 0 && endIndex > startIndex) {
            return line.substring(startIndex + 1, endIndex);
        }
        return "未指定位置";
    }

    /**
     * 提取引用内容
     */
    private String extractReferenceContent(String line) {
        int colonIndex = line.indexOf("：");
        if (colonIndex > 0 && colonIndex < line.length() - 1) {
            return line.substring(colonIndex + 1).trim();
        }
        return line;
    }

    /**
     * 确定处理状态
     */
    private String determineProcessingStatus(List<AnalysisResult> extractResults, List<AnalysisResult> detectResults) {
        if (extractResults.isEmpty() && detectResults.isEmpty()) {
            return "NO_DATA";
        }

        boolean hasExtract = !extractResults.isEmpty();
        boolean hasDetect = !detectResults.isEmpty();

        if (hasExtract && hasDetect) {
            return "COMPLETED";
        } else if (hasExtract || hasDetect) {
            return "PARTIAL";
        } else {
            return "NO_DATA";
        }
    }
}
