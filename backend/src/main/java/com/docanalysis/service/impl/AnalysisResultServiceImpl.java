package com.docanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.docanalysis.entity.AnalysisResult;
import com.docanalysis.mapper.AnalysisResultMapper;
import com.docanalysis.service.AnalysisResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * AnalysisResultService实现类
 */
@Slf4j
@Service
public class AnalysisResultServiceImpl extends ServiceImpl<AnalysisResultMapper, AnalysisResult> implements AnalysisResultService {

    @Override
    public List<AnalysisResult> getByDocumentId(Long documentId) {
        LambdaQueryWrapper<AnalysisResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisResult::getDocumentId, documentId)
                .orderByDesc(AnalysisResult::getCreatedTime);
        return list(queryWrapper);
    }

    @Override
    public AnalysisResult getByDocumentIdAndAgentType(Long documentId, AnalysisResult.AgentType agentType) {
        LambdaQueryWrapper<AnalysisResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisResult::getDocumentId, documentId)
                .eq(AnalysisResult::getAgentType, agentType)
                .orderByDesc(AnalysisResult::getCreatedTime)
                .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    @Override
    public int deleteByDocumentId(Long documentId) {
        LambdaQueryWrapper<AnalysisResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalysisResult::getDocumentId, documentId);
        return Math.toIntExact(count(queryWrapper));
    }
}
