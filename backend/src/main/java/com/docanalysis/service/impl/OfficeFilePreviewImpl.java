package com.docanalysis.service.impl;


import com.docanalysis.config.ConfigConstants;
import com.docanalysis.dto.FileAttribute;
import com.docanalysis.service.FileHandlerService;
import com.docanalysis.service.FilePreview;
import com.docanalysis.service.OfficeToPdfService;
import com.docanalysis.util.KkFileUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.EncryptedDocumentException;
import org.jodconverter.core.office.OfficeException;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;

/**
 * Created by kl on 2018/1/17.
 * Content :处理office文件
 */
@Service
public class OfficeFilePreviewImpl implements FilePreview {

    public static final String OFFICE_PREVIEW_TYPE_IMAGE = "image";
    public static final String OFFICE_PREVIEW_TYPE_ALL_IMAGES = "allImages";
    private static final String OFFICE_PASSWORD_MSG = "password";

    private final FileHandlerService fileHandlerService;
    private final OfficeToPdfService officeToPdfService;

    public OfficeFilePreviewImpl(FileHandlerService fileHandlerService, OfficeToPdfService officeToPdfService) {
        this.fileHandlerService = fileHandlerService;
        this.officeToPdfService = officeToPdfService;
    }

    @Override
    public String filePreviewHandle(String file, Model model, FileAttribute fileAttribute) {
        // 预览Type，参数传了就取参数的，没传取系统默认
        String officePreviewType = fileAttribute.getOfficePreviewType();
        boolean userToken = fileAttribute.getUsePasswordCache();
        String suffix = fileAttribute.getSuffix();  //获取文件后缀
        String fileName = fileAttribute.getName(); //获取文件原始名称
        String filePassword = fileAttribute.getFilePassword(); //获取密码
        boolean forceUpdatedCache = fileAttribute.forceUpdatedCache();  //是否启用强制更新命令
        boolean isHtmlView = fileAttribute.isHtmlView();  //xlsx  转换成html
        String cacheName = fileAttribute.getCacheName();  //转换后的文件名
        String outFilePath = fileAttribute.getOutFilePath();  //转换后生成文件的路径
        if (StringUtils.hasText(outFilePath)) {
            try {
                officeToPdfService.openOfficeToPDF(fileAttribute.getOriginFilePath(), outFilePath, fileAttribute);
            } catch (OfficeException e) {
            }
            if (isHtmlView) {
                // 对转换后的文件进行操作(改变编码方式)
                fileHandlerService.doActionConvertedFile(outFilePath);
            }
            //是否保留OFFICE源文件
            if (!fileAttribute.isCompressFile() && ConfigConstants.getDeleteSourceFile()) {
                KkFileUtils.deleteFileByPath(fileAttribute.getOriginFilePath());
            }
            fileHandlerService.addConvertedFile(cacheName, fileHandlerService.getRelativePath(outFilePath));

        }

        return outFilePath;
    }

}
