package com.docanalysis.service.impl;

import com.docanalysis.dto.MaxKBResponseDTO;
import com.docanalysis.entity.Document;

import com.docanalysis.service.AnalysisService;
import com.docanalysis.service.DocumentService;
import com.docanalysis.service.MaxKBService;
import com.docanalysis.util.MaxKBApiClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class MaxKBServiceImpl implements MaxKBService {

    private final MaxKBApiClient maxKBApiClient;
    private final DocumentService documentService;
    private final AnalysisService analysisService;

    @Value("${app.maxkb.qa-agent.application-id}")
    private String apiKey;
    // 存储正在进行的分析任务
    private final Map<Long, CompletableFuture<MaxKBResponseDTO>> runningAnalyses = new ConcurrentHashMap<>();

    // 存储分析结果缓存
    private final Map<Long, MaxKBResponseDTO> analysisResults = new ConcurrentHashMap<>();

    @Override
    public CompletableFuture<MaxKBResponseDTO> analyzeDocument(Long documentId) {
        Document document = documentService.getById(documentId);
        if (document == null) {
            CompletableFuture<MaxKBResponseDTO> future = new CompletableFuture<>();
            future.completeExceptionally(new IllegalArgumentException("文档不存在: " + documentId));
            return future;
        }

        return analyzeDocument(documentId, document.getFilePath());
    }

    @Override
    public CompletableFuture<MaxKBResponseDTO> analyzeDocument(Long documentId, String filePath) {
        log.info("开始分析文档: documentId={}, filePath={}", documentId, filePath);

        // 检查是否已有正在进行的分析
        if (runningAnalyses.containsKey(documentId)) {
            log.info("文档正在分析中，返回现有任务: documentId={}", documentId);
            return runningAnalyses.get(documentId);
        }

        long startTime = System.currentTimeMillis();

        CompletableFuture<MaxKBResponseDTO> analysisTask = performAnalysis(documentId, filePath, startTime);

        // 存储正在进行的任务
        runningAnalyses.put(documentId, analysisTask);

        // 任务完成后清理
        analysisTask.whenComplete((result, throwable) -> {
            runningAnalyses.remove(documentId);
            if (result != null) {
                analysisResults.put(documentId, result);
            }
        });

        return analysisTask;
    }

    @Override
    public CompletableFuture<MaxKBResponseDTO> reanalyzeDocument(Long documentId) {
        log.info("重新分析文档: documentId={}", documentId);

        // 取消正在进行的分析
        cancelAnalysis(documentId);

        // 清除缓存结果
        analysisResults.remove(documentId);

        return analyzeDocument(documentId);
    }

    @Override
    public String getAnalysisStatus(Long documentId) {
        if (runningAnalyses.containsKey(documentId)) {
            return "PROCESSING";
        } else if (analysisResults.containsKey(documentId)) {
            MaxKBResponseDTO result = analysisResults.get(documentId);
            return result.getStatus();
        } else {
            return "NOT_STARTED";
        }
    }

    @Override
    @Cacheable(value = "maxkbResults", key = "#documentId")
    public MaxKBResponseDTO getAnalysisResult(Long documentId) {
        return analysisResults.get(documentId);
    }

    @Override
    public boolean cancelAnalysis(Long documentId) {
        CompletableFuture<MaxKBResponseDTO> task = runningAnalyses.get(documentId);
        if (task != null && !task.isDone()) {
            boolean cancelled = task.cancel(true);
            if (cancelled) {
                runningAnalyses.remove(documentId);
                log.info("分析任务已取消: documentId={}", documentId);
            }
            return cancelled;
        }
        return false;
    }

    /**
     * 执行分析
     */
    private CompletableFuture<MaxKBResponseDTO> performAnalysis(Long documentId, String filePath, long startTime) {
        return maxKBApiClient.createBothSessions()
                .thenCompose(sessions -> {
                    log.info("会话创建完成，开始上传文件: documentId={}", documentId);
                    return maxKBApiClient.uploadFileToBoth(sessions, filePath)
                            .thenCompose(fileInfos -> {
                                log.info("文件上传完成，开始发送分析请求: documentId={}", documentId);
                                return sendAnalysisRequests(sessions, fileInfos, documentId);
                            });
                })
                .thenApply(results -> {
                    long totalTime = System.currentTimeMillis() - startTime;
                    MaxKBResponseDTO response = buildResponse(documentId, results, totalTime);

                    // 处理并存储分析结果
                    try {
                        analysisService.processAndStoreResults(documentId, response);
                        log.info("分析结果已存储到数据库: documentId={}", documentId);
                    } catch (Exception e) {
                        log.error("存储分析结果失败: documentId={}", documentId, e);
                        // 不影响主流程，继续返回结果
                    }

                    return response;
                })
                .exceptionally(throwable -> {
                    long totalTime = System.currentTimeMillis() - startTime;
                    log.error("文档分析失败: documentId={}", documentId, throwable);
                    return buildErrorResponse(documentId, throwable, totalTime);
                });
    }

    /**
     * 发送分析请求
     */
    private CompletableFuture<Map<MaxKBApiClient.AgentType, Map<String, Object>>> sendAnalysisRequests(
            Map<MaxKBApiClient.AgentType, String> sessions,
            Map<MaxKBApiClient.AgentType, Map<String, Object>> fileInfos,
            Long documentId) {

        String extractMessage = "分析";
        String detectMessage = "分析。";

        return maxKBApiClient.sendChatRequestToBoth(sessions, fileInfos, extractMessage, detectMessage);
    }

    /**
     * 构建响应结果
     */
    private MaxKBResponseDTO buildResponse(Long documentId,
                                         Map<MaxKBApiClient.AgentType, Map<String, Object>> results,
                                         long totalTime) {

        MaxKBResponseDTO response = new MaxKBResponseDTO()
                .setDocumentId(documentId)
                .setTotalProcessingTime(totalTime)
                .setProcessedTime(LocalDateTime.now());

        // 处理提取智能体结果
        Map<String, Object> extractResult = results.get(MaxKBApiClient.AgentType.EXTRACT);
        if (extractResult != null) {
            MaxKBResponseDTO.AgentResult extractAgentResult = buildAgentResult(
                    MaxKBApiClient.AgentType.EXTRACT, extractResult);
            response.setExtractResult(extractAgentResult);
        }

        // 处理检测智能体结果
        Map<String, Object> detectResult = results.get(MaxKBApiClient.AgentType.DETECT);
        if (detectResult != null) {
            MaxKBResponseDTO.AgentResult detectAgentResult = buildAgentResult(
                    MaxKBApiClient.AgentType.DETECT, detectResult);
            response.setDetectResult(detectAgentResult);
        }

        // 设置整体状态
        if (response.getSuccessfulAgentCount() == 2) {
            response.setStatus("SUCCESS");
        } else if (response.getSuccessfulAgentCount() == 1) {
            response.setStatus("PARTIAL_SUCCESS");
        } else {
            response.setStatus("FAILED");
        }

        log.info("文档分析完成: documentId={}, status={}, time={}ms",
                documentId, response.getStatus(), totalTime);

        return response;
    }

    /**
     * 构建智能体结果
     */
    private MaxKBResponseDTO.AgentResult buildAgentResult(MaxKBApiClient.AgentType agentType,
                                                         Map<String, Object> apiResponse) {
        MaxKBResponseDTO.AgentResult agentResult = new MaxKBResponseDTO.AgentResult()
                .setAgentType(agentType.name())
                .setRawResponse(apiResponse);

        try {
            // 提取响应内容
            if (apiResponse.containsKey("data")) {
                Map<String, Object> data = (Map<String, Object>) apiResponse.get("data");
                if (data.containsKey("content")) {
                    agentResult.setContent((String) data.get("content"));
                }
            }

            agentResult.setStatus("SUCCESS");

            // TODO: 解析具体的提取数据和检测问题
            // 这里需要根据实际的MaxKB响应格式来解析

        } catch (Exception e) {
            log.error("解析{}智能体响应失败", agentType.getDescription(), e);
            agentResult.setStatus("FAILED")
                      .setErrorMessage("响应解析失败: " + e.getMessage());
        }

        return agentResult;
    }

    /**
     * 构建错误响应
     */
    private MaxKBResponseDTO buildErrorResponse(Long documentId, Throwable throwable, long totalTime) {
        return new MaxKBResponseDTO()
                .setDocumentId(documentId)
                .setStatus("FAILED")
                .setErrorMessage(throwable.getMessage())
                .setTotalProcessingTime(totalTime)
                .setProcessedTime(LocalDateTime.now());
    }

    @Override
    public CompletableFuture<Map<String, Object>> analyzeDocument(MaxKBApiClient.AgentType agentType, Long documentId, String filePath) {
        log.info("开始{}分析: documentId={}, filePath={}", agentType.getDescription(), documentId, filePath);


        return createSessionAndUploadFile(agentType, filePath)
                .thenCompose(sessionInfo -> {
                    String chatId = sessionInfo.get("chatId");
                    String fileId = sessionInfo.get("fileId");

                    String message = agentType == MaxKBApiClient.AgentType.EXTRACT ?
                        "提取关键信息" :
                        "请检测这个文档中的异常和问题";

                    return sendAnalysisRequest(agentType, chatId, fileId, message);
                })
                .exceptionally(throwable -> {
                    log.error("{}分析失败: documentId={}", agentType.getDescription(), documentId, throwable);
                    Map<String, Object> errorResult = new ConcurrentHashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", throwable.getMessage());
                    errorResult.put("agentType", agentType.getCode());
                    return errorResult;
                });
    }

    @Override
    public CompletableFuture<Map<String, String>> createSessionAndUploadFile(MaxKBApiClient.AgentType agentType, String filePath) {
        log.info("为{}创建会话并上传文件: filePath={}", agentType.getDescription(), filePath);

        return maxKBApiClient.createSession(agentType)
                .thenCompose(chatId -> {
                    log.info("{}会话创建成功: chatId={}", agentType.getDescription(), chatId);

                    return maxKBApiClient.uploadFile(agentType, chatId, filePath)
                            .thenApply(fileInfo -> {
                                String fileId = (String) fileInfo.get("file_id");
                                log.info("文件上传到{}成功: fileId={}", agentType.getDescription(), fileId);

                                Map<String, String> result = new ConcurrentHashMap<>();
                                result.put("chatId", chatId);
                                result.put("fileId", fileId);
                                return result;
                            });
                })
                .exceptionally(throwable -> {
                    log.error("创建会话或上传文件失败: agentType={}, filePath={}", agentType, filePath, throwable);
                    throw new RuntimeException("创建会话或上传文件失败", throwable);
                });
    }

    @Override
    public CompletableFuture<Map<String, Object>> sendAnalysisRequest(MaxKBApiClient.AgentType agentType, String chatId, String fileId, String message) {
        log.info("发送{}分析请求: chatId={}, fileId={}, message={}", agentType.getDescription(), chatId, fileId, message);

        // 构建文件信息
        Map<String, Object> fileInfo = new ConcurrentHashMap<>();
        fileInfo.put("file_id", fileId);

        return maxKBApiClient.sendChatRequest(agentType, chatId, message, fileInfo)
                .thenApply(response -> {
                    log.info("{}分析请求完成: response={}", agentType.getDescription(), response);

                    Map<String, Object> result = new ConcurrentHashMap<>();
                    result.put("success", true);
                    result.put("response", response);
                    result.put("agentType", agentType.getCode());
                    result.put("chatId", chatId);
                    result.put("fileId", fileId);

                    return result;
                })
                .exceptionally(throwable -> {
                    log.error("{}分析请求失败: chatId={}, fileId={}", agentType.getDescription(), chatId, fileId, throwable);

                    Map<String, Object> errorResult = new ConcurrentHashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("error", throwable.getMessage());
                    errorResult.put("agentType", agentType.getCode());
                    errorResult.put("chatId", chatId);
                    errorResult.put("fileId", fileId);

                    return errorResult;
                });
    }

    @Override
    public String getMaxKbToken() {
        return maxKBApiClient.createToken();
    }

    @Override
    public String getQaAppId() {
        return apiKey;
    }
}
