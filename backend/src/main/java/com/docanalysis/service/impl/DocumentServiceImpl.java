package com.docanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.docanalysis.config.CacheConfig;
import com.docanalysis.config.HeaderFooterConfig;
import com.docanalysis.dto.DocumentContentDTO;
import com.docanalysis.dto.HeaderFooterInfo;
import com.docanalysis.dto.PageInfo;
import com.docanalysis.entity.Document;
import com.docanalysis.mapper.DocumentMapper;
import com.docanalysis.service.DocumentService;
import com.docanalysis.util.DocumentHtmlConverter;
import com.docanalysis.util.HeaderFooterPerformanceMonitor;
import com.docanalysis.util.POIDocumentParser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class DocumentServiceImpl extends ServiceImpl<DocumentMapper, Document> implements DocumentService {

    private final POIDocumentParser poiDocumentParser;
    private final DocumentHtmlConverter documentHtmlConverter;
    private final CacheConfig cacheConfig;
    private final HeaderFooterConfig headerFooterConfig;
    private final HeaderFooterPerformanceMonitor performanceMonitor;

    @Override
    public IPage<Document> getDocumentsByStatus(Page<Document> page, Document.DocumentStatus status) {
        return baseMapper.selectPageByStatus(page, status);
    }

    @Override
    public List<Document> getDocumentsByStatusAndTimeRange(Document.DocumentStatus status, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectByStatusAndTimeRange(status, startTime, endTime);
    }

    @Override
    public List<Document> getDocumentsByFilename(String filename) {
        return baseMapper.selectByFilenameContaining(filename);
    }

    @Override
    public List<Document> getDocumentsByFileSizeRange(Long minSize, Long maxSize) {
        return baseMapper.selectByFileSizeRange(minSize, maxSize);
    }

    @Override
    public Map<String, Long> getDocumentCountByStatus() {
        List<Map<String, Object>> results = baseMapper.countByStatus();
        return results.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("status"),
                        map -> ((Number) map.get("count")).longValue()
                ));
    }

    @Override
    public List<Document> getRecentDocuments(Integer limit) {
        return baseMapper.selectRecentDocuments(limit);
    }

    @Override
    public List<Document> getFailedDocuments() {
        return baseMapper.selectFailedDocuments();
    }

    @Override
    public List<Document> getProcessingDocuments() {
        return baseMapper.selectProcessingDocuments();
    }

    @Override
    public List<Document> getCompletedDocuments() {
        return baseMapper.selectCompletedDocuments();
    }

    @Override
    public Document getDocumentByFilePath(String filePath) {
        return baseMapper.selectByFilePath(filePath);
    }

    @Override
    public Map<String, Object> getDocumentStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getStatisticsByTimeRange(startTime, endTime);
    }

    @Override
    public List<Document> getLargeFiles(Long sizeThreshold) {
        return baseMapper.selectLargeFiles(sizeThreshold);
    }

    @Override
    public List<Document> getExpiredDocuments(Integer retentionDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(retentionDays);
        return baseMapper.selectExpiredDocuments(expireTime);
    }

    @Override
    public boolean batchUpdateStatus(List<Long> documentIds, Document.DocumentStatus oldStatus, Document.DocumentStatus newStatus) {
        if (documentIds == null || documentIds.isEmpty()) {
            return false;
        }
        String ids = documentIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        int updated = baseMapper.batchUpdateStatus(ids, oldStatus, newStatus);
        return updated > 0;
    }

    @Override
    public Double getDocumentSuccessRate(LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.getSuccessRateByTimeRange(startTime, endTime);
    }

    @Override
    public IPage<Document> getDocumentsByConditions(Page<Document> page, Document.DocumentStatus status, String filename, Long minSize, Long maxSize, LocalDateTime startTime, LocalDateTime endTime) {
        return baseMapper.selectByConditions(page, status, filename, minSize, maxSize, startTime, endTime);
    }

    @Override
    public Document createDocument(String filename, String originalName, String filePath, Long fileSize,String pdfFilename,String pdfFilePath) {
        Document document = new Document()
                .setPdfFilename(pdfFilename)
                .setPdfFilePath(pdfFilePath)
                .setFilename(filename)
                .setOriginalName(originalName)
                .setFilePath(filePath)
                .setFileSize(fileSize)
                .setStatus(Document.DocumentStatus.UPLOADED);
        
        boolean saved = save(document);
        if (saved) {
            log.info("创建文档记录成功: {}", document.getId());
            return document;
        } else {
            log.error("创建文档记录失败: filename={}", filename);
            return null;
        }
    }

    @Override
    public boolean updateDocumentStatus(Long documentId, Document.DocumentStatus status) {
        Document document = new Document();
        document.setId(documentId);
        document.setStatus(status);
        boolean updated = updateById(document);
        if (updated) {
            log.info("更新文档状态成功: documentId={}, status={}", documentId, status);
        } else {
            log.warn("更新文档状态失败: documentId={}, status={}", documentId, status);
        }
        return updated;
    }

    @Override
    public boolean deleteDocumentCompletely(Long documentId) {
        // TODO: 删除相关的分析结果和用户交互记录
        boolean deleted = removeById(documentId);
        if (deleted) {
            log.info("删除文档成功: documentId={}", documentId);
        } else {
            log.warn("删除文档失败: documentId={}", documentId);
        }
        return deleted;
    }

    @Override
    public boolean existsDocument(Long documentId) {
        return count(new QueryWrapper<Document>().eq("id", documentId)) > 0;
    }

    @Override
    public boolean existsFilePath(String filePath) {
        return count(new QueryWrapper<Document>().eq("file_path", filePath)) > 0;
    }

    @Override
    public Long getTotalDocumentCount() {
        return count();
    }

    @Override
    public Long getTotalDocumentSize() {
        List<Document> documents = list();
        return documents.stream()
                .mapToLong(doc -> doc.getFileSize() != null ? doc.getFileSize() : 0L)
                .sum();
    }

    @Override
    public int cleanupExpiredDocuments(Integer retentionDays) {
        List<Document> expiredDocuments = getExpiredDocuments(retentionDays);
        if (expiredDocuments.isEmpty()) {
            return 0;
        }
        
        List<Long> expiredIds = expiredDocuments.stream()
                .map(Document::getId)
                .collect(Collectors.toList());
        
        boolean removed = removeByIds(expiredIds);
        int cleanedCount = removed ? expiredIds.size() : 0;
        
        log.info("清理过期文档完成: 清理数量={}, 保留天数={}", cleanedCount, retentionDays);
        return cleanedCount;
    }

    @Override
    public DocumentContentDTO parseDocumentContent(Long documentId) {
        log.info("开始解析文档内容: documentId={}", documentId);

        Document document = getById(documentId);
        if (document == null) {
            throw new IllegalArgumentException("文档不存在: " + documentId);
        }

        if (!document.isProcessedSuccessfully()) {
            updateDocumentStatus(documentId, Document.DocumentStatus.PROCESSING);
        }

        try {
            long startTime = System.currentTimeMillis();

            // 使用POI解析文档
            POIDocumentParser.DocumentParseResult parseResult = poiDocumentParser.parseDocument(document.getFilePath());

            // 转换为HTML
            DocumentHtmlConverter.HtmlConvertResult htmlResult = documentHtmlConverter.convertToHtml(parseResult);

            long parseTime = System.currentTimeMillis() - startTime;

            // 构建DTO
            DocumentContentDTO contentDTO = new DocumentContentDTO()
                    .setDocumentId(documentId)
                    .setOriginalName(document.getOriginalName())
                    .setHtmlContent(htmlResult.getHtmlContent())
                    .setCssStyles(htmlResult.getCssStyles())
                    .setMetadata(htmlResult.getMetadata())
                    .setParseStatus("SUCCESS")
                    .setParseTime(parseTime);

            // 转换元素列表
            if (parseResult.getElements() != null) {
                List<DocumentContentDTO.DocumentElementDTO> elementDTOs = parseResult.getElements().stream()
                        .map(this::convertToElementDTO)
                        .collect(java.util.stream.Collectors.toList());
                contentDTO.setElements(elementDTOs);
            }

            // 转换位置映射
            if (parseResult.getPositionMappings() != null) {
                List<DocumentContentDTO.PositionMappingDTO> mappingDTOs = parseResult.getPositionMappings().stream()
                        .map(this::convertToMappingDTO)
                        .collect(java.util.stream.Collectors.toList());
                contentDTO.setPositionMappings(mappingDTOs);
            }

            // 提取纯文本
            String textContent = extractTextContent(parseResult.getElements());
            contentDTO.setTextContent(textContent);


            // 更新文档状态
            updateDocumentStatus(documentId, Document.DocumentStatus.COMPLETED);

            log.info("文档解析完成: documentId={}, 耗时={}ms", documentId, parseTime);
            return contentDTO;

        } catch (Exception e) {
            log.error("文档解析失败: documentId={}", documentId, e);
            updateDocumentStatus(documentId, Document.DocumentStatus.FAILED);

            return new DocumentContentDTO()
                    .setDocumentId(documentId)
                    .setOriginalName(document.getOriginalName())
                    .setParseStatus("ERROR")
                    .setErrorMessage(e.getMessage());
        }
    }

    @Override
    @Cacheable(value = "documentContent", key = "#documentId")
    public DocumentContentDTO getDocumentContent(Long documentId) {
        // 首先尝试从缓存获取，如果没有则解析
        return parseDocumentContent(documentId);
    }

    @Override
    public boolean isDocumentParsed(Long documentId) {
        Document document = getById(documentId);
        return document != null && document.isProcessedSuccessfully();
    }

    @Override
    public DocumentContentDTO reparseDocument(Long documentId) {
        log.info("重新解析文档: documentId={}", documentId);

        // 清除缓存
        cacheConfig.clearDocumentCaches(documentId);

        return parseDocumentContent(documentId);
    }

    /**
     * 转换元素为DTO
     */
    private DocumentContentDTO.DocumentElementDTO convertToElementDTO(POIDocumentParser.DocumentElement element) {
        return new DocumentContentDTO.DocumentElementDTO()
                .setElementId(element.getElementId())
                .setType(element.getType())
                .setContent(element.getContent())
                .setStyle(element.getStyle())
                .setStartPosition(element.getStartPosition())
                .setEndPosition(element.getEndPosition());
    }

    /**
     * 转换位置映射为DTO
     */
    private DocumentContentDTO.PositionMappingDTO convertToMappingDTO(POIDocumentParser.PositionMapping mapping) {
        return new DocumentContentDTO.PositionMappingDTO()
                .setElementId(mapping.getElementId())
                .setOriginalStart(mapping.getOriginalStart())
                .setOriginalEnd(mapping.getOriginalEnd())
                .setHtmlStart(mapping.getHtmlStart())
                .setHtmlEnd(mapping.getHtmlEnd())
                .setHtmlElementId(mapping.getHtmlElementId());
    }

    /**
     * 提取纯文本内容
     */
    private String extractTextContent(List<POIDocumentParser.DocumentElement> elements) {
        if (elements == null || elements.isEmpty()) {
            return "";
        }

        return elements.stream()
                .map(POIDocumentParser.DocumentElement::getContent)
                .filter(content -> content != null && !content.trim().isEmpty())
                .collect(java.util.stream.Collectors.joining("\n"));
    }

    // ========== 页眉页码功能增强方法 ==========


    private void processHeaderFooterFeatures(Document document, DocumentContentDTO contentDTO) {
        // 检查功能是否启用
        if (!headerFooterConfig.isEnabled()) {
            log.debug("页眉页码功能已禁用: documentId={}", document.getId());
            return;
        }

        // 检查文档大小限制
        if (headerFooterConfig.isDocumentSizeExceeded(getDocumentFileSize(document.getFilePath()))) {
            log.warn("文档大小超过限制，跳过页眉页码处理: documentId={}, filePath={}",
                    document.getId(), document.getFilePath());
            return;
        }

        long overallStartTime = performanceMonitor.startOperation("HEADER_FOOTER_OVERALL");

        try {
            // 提取页眉页脚信息
            HeaderFooterInfo headerFooterInfo = extractHeaderFooterInfoWithMonitoring(document.getFilePath());
            if (headerFooterInfo != null && headerFooterInfo.hasAny()) {
                contentDTO.setHeaderFooter(headerFooterInfo);
                log.debug("成功提取页眉页脚信息: documentId={}", document.getId());
            }

            // 计算页码信息
            if (headerFooterConfig.shouldCalculatePageBreaks()) {
                List<PageInfo> pages = calculatePageBreaksWithMonitoring(document.getFilePath(), headerFooterInfo);
                if (pages != null && !pages.isEmpty()) {
                    // 检查页数限制
                    if (headerFooterConfig.isPagesLimitExceeded(pages.size())) {
                        log.warn("页数超过限制，截断页码信息: documentId={}, 实际页数={}, 限制={}",
                                document.getId(), pages.size(), headerFooterConfig.getMaxPagesLimit());
                        pages = pages.subList(0, headerFooterConfig.getMaxPagesLimit());
                    }

                    contentDTO.setPages(pages);
                    log.debug("成功计算页码信息: documentId={}, 页数={}", document.getId(), pages.size());
                }
            }

            performanceMonitor.recordSuccess("HEADER_FOOTER_OVERALL", overallStartTime);

        } catch (Exception e) {
            performanceMonitor.recordFailure("HEADER_FOOTER_OVERALL", overallStartTime, e);

            if (headerFooterConfig.isDetailedErrorLogging()) {
                log.error("页眉页码功能处理失败: documentId={}, filePath={}",
                         document.getId(), document.getFilePath(), e);
            } else {
                log.warn("页眉页码功能处理失败，但不影响主流程: documentId={}, error={}",
                        document.getId(), e.getMessage());
            }
            // 页眉页码功能失败不影响主文档解析流程
        }
    }

    /**
     * 带性能监控的页眉页脚信息提取
     */
    private HeaderFooterInfo extractHeaderFooterInfoWithMonitoring(String filePath) {
        long startTime = performanceMonitor.startOperation("HEADER_FOOTER_EXTRACTION");

        try {
            HeaderFooterInfo result = poiDocumentParser.extractHeaderFooterInfoFromFile(filePath);
            performanceMonitor.recordSuccess("HEADER_FOOTER_EXTRACTION", startTime);
            return result;

        } catch (Exception e) {
            performanceMonitor.recordFailure("HEADER_FOOTER_EXTRACTION", startTime, e);
            throw e;
        }
    }

    /**
     * 带性能监控的页码计算
     */
    private List<PageInfo> calculatePageBreaksWithMonitoring(String filePath, HeaderFooterInfo headerFooterInfo) {
        long startTime = performanceMonitor.startOperation("PAGE_BREAK_CALCULATION");

        try {
            List<PageInfo> result = poiDocumentParser.calculatePageBreaksFromFile(filePath, headerFooterInfo);
            performanceMonitor.recordSuccess("PAGE_BREAK_CALCULATION", startTime);
            return result;

        } catch (Exception e) {
            performanceMonitor.recordFailure("PAGE_BREAK_CALCULATION", startTime, e);
            throw e;
        }
    }

    /**
     * 获取文档文件大小
     */
    private long getDocumentFileSize(String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            return file.exists() ? file.length() : 0;
        } catch (Exception e) {
            log.debug("获取文件大小失败: filePath={}, error={}", filePath, e.getMessage());
            return 0;
        }
    }

    /**
     * 提取页眉页脚信息（兼容旧版本）
     */
    private HeaderFooterInfo extractHeaderFooterInfo(String filePath) {
        return extractHeaderFooterInfoWithMonitoring(filePath);
    }

    /**
     * 计算页码分割点（兼容旧版本）
     */
    private List<PageInfo> calculatePageBreaks(String filePath, HeaderFooterInfo headerFooterInfo) {
        return calculatePageBreaksWithMonitoring(filePath, headerFooterInfo);
    }
}
