package com.docanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.docanalysis.entity.AnalysisResult;
import com.docanalysis.entity.AnalysisTask;
import com.docanalysis.mapper.AnalysisTaskMapper;
import com.docanalysis.service.AnalysisResultService;
import com.docanalysis.service.AnalysisTaskService;
import com.docanalysis.service.MaxKBService;
import com.docanalysis.util.MaxKBApiClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;


@Slf4j
@Service
@RequiredArgsConstructor
public class AnalysisTaskServiceImpl extends ServiceImpl<AnalysisTaskMapper, AnalysisTask> implements AnalysisTaskService {

    private final MaxKBService maxKBService;
    private final AnalysisResultService analysisResultService;

    @Override
    @Transactional
    public AnalysisTask createTask(Long documentId, MaxKBApiClient.AgentType agentType) {
        log.info("创建分析任务: documentId={}, agentType={}", documentId, agentType);
        
        AnalysisTask task = new AnalysisTask();
        task.setDocumentId(documentId);
        task.setAgentType(agentType);
        task.setTaskStatus(AnalysisTask.TaskStatus.PENDING);
        task.setProgressPercentage(0);
        task.setStatusMessage("任务已创建，等待开始处理");
        task.setRetryCount(0);
        task.setCreatedTime(LocalDateTime.now());
        
        save(task);
        log.info("分析任务创建成功: taskId={}", task.getId());
        
        return task;
    }

    @Override
    @Transactional
    public List<AnalysisTask> createAllTasksForDocument(Long documentId) {
        log.info("为文档创建所有分析任务: documentId={}", documentId);
        
        List<AnalysisTask> tasks = new ArrayList<>();
        
        // 创建数据提取任务
        AnalysisTask extractTask = createTask(documentId, MaxKBApiClient.AgentType.EXTRACT);
        tasks.add(extractTask);

        // 创建异常检测任务
        AnalysisTask detectTask = createTask(documentId, MaxKBApiClient.AgentType.DETECT);
        tasks.add(detectTask);
        
        log.info("所有分析任务创建完成: documentId={}, taskCount={}", documentId, tasks.size());
        return tasks;
    }

    @Override
    @Async
    public CompletableFuture<AnalysisTask> startTask(Long taskId, String chatId, String fileId) {
        log.info("启动分析任务: taskId={}, chatId={}, fileId={}", taskId, chatId, fileId);
        
        AnalysisTask task = getById(taskId);
        if (task == null) {
            log.error("任务不存在: taskId={}", taskId);
            return CompletableFuture.failedFuture(new IllegalArgumentException("任务不存在"));
        }
        
        try {
            // 更新任务状态为处理中
            task.markStarted("开始" + task.getAgentTypeDisplay() + "分析");
            task.setChatId(chatId);
            task.setFileId(fileId);
            updateById(task);
            
            // 调用MaxKB服务进行分析
            updateTaskProgress(taskId, 10, "正在连接" + task.getAgentTypeDisplay() + "...");

            // 发送分析请求
            String message = task.getAgentType() == MaxKBApiClient.AgentType.EXTRACT ?
                "请分析这个文档并提取关键信息" :
                "请检测这个文档中的异常和问题";

            updateTaskProgress(taskId, 30, "正在发送" + task.getAgentTypeDisplay() + "请求...");

            Map<String, Object> result = maxKBService.sendAnalysisRequest(
                task.getAgentType(), chatId, fileId, message).get();

            updateTaskProgress(taskId, 90, task.getAgentTypeDisplay() + "即将完成...");

            // 检查分析结果
            if (result != null && Boolean.TRUE.equals(result.get("success"))) {
                // 保存分析结果到数据库
                Long resultId = saveAnalysisResult(task.getDocumentId(), task.getAgentType(), result);
                markTaskCompleted(taskId, resultId, task.getAgentTypeDisplay() + "完成");
                log.info("{}任务完成: taskId={}, resultId={}, result={}", task.getAgentTypeDisplay(), taskId, resultId, result);
            } else {
                String errorMsg = result != null ? (String) result.get("error") : "未知错误";
                markTaskFailed(taskId, task.getAgentTypeDisplay() + "失败: " + errorMsg);
            }
            
            return CompletableFuture.completedFuture(getById(taskId));
            
        } catch (Exception e) {
            log.error("任务执行失败: taskId={}", taskId, e);
            markTaskFailed(taskId, "任务执行失败: " + e.getMessage());
            return CompletableFuture.failedFuture(e);
        }
    }

    @Override
    public void updateTaskProgress(Long taskId, int progress, String message) {
        log.debug("更新任务进度: taskId={}, progress={}%, message={}", taskId, progress, message);
        
        UpdateWrapper<AnalysisTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", taskId)
                .set("progress_percentage", Math.max(0, Math.min(100, progress)))
                .set("status_message", message);
        
        update(updateWrapper);
    }

    @Override
    @Transactional
    public void markTaskCompleted(Long taskId, Long resultId, String message) {
        log.info("标记任务完成: taskId={}, resultId={}, message={}", taskId, resultId, message);
        
        AnalysisTask task = getById(taskId);
        if (task != null) {
            task.markCompleted(message, resultId);
            updateById(task);
        }
    }

    @Override
    @Transactional
    public void markTaskFailed(Long taskId, String errorMessage) {
        log.error("标记任务失败: taskId={}, error={}", taskId, errorMessage);
        
        AnalysisTask task = getById(taskId);
        if (task != null) {
            task.markFailed(errorMessage);
            updateById(task);
        }
    }

    @Override
    public List<AnalysisTask> getTasksByDocumentId(Long documentId) {
        return baseMapper.selectByDocumentId(documentId);
    }

    @Override
    public AnalysisTask getTaskByDocumentIdAndType(Long documentId, MaxKBApiClient.AgentType agentType) {
        return baseMapper.selectByDocumentIdAndAgentType(documentId, agentType);
    }

    @Override
    public Map<String, Object> getDocumentAnalysisOverview(Long documentId) {
        return baseMapper.selectDocumentAnalysisOverview(documentId);
    }

    @Override
    public Map<String, Object> getDocumentOverallStatus(Long documentId) {
        return baseMapper.selectDocumentOverallStatus(documentId);
    }

    @Override
    public List<Map<String, Object>> getTaskProgressDetails(Long documentId) {
        return baseMapper.selectTaskProgressDetails(documentId);
    }

    @Override
    public boolean hasActiveTasksForDocument(Long documentId) {
        return baseMapper.hasActiveTasksForDocument(documentId);
    }

    @Override
    public List<AnalysisTask> getProcessingTasks() {
        return baseMapper.selectProcessingTasks();
    }

    @Override
    public List<AnalysisTask> getPendingTasks() {
        return baseMapper.selectPendingTasks();
    }

    @Override
    public Map<String, Object> getTaskStatistics(Long documentId) {
        return baseMapper.selectTaskStatistics(documentId);
    }

    @Override
    public List<Map<String, Object>> getRecentTasks(int hours, int limit) {
        return baseMapper.selectRecentTasks(hours, limit);
    }

    @Override
    @Async
    public CompletableFuture<AnalysisTask> retryTask(Long taskId) {
        log.info("重试任务: taskId={}", taskId);
        
        AnalysisTask task = getById(taskId);
        if (task == null) {
            return CompletableFuture.failedFuture(new IllegalArgumentException("任务不存在"));
        }
        
        // 增加重试次数
        task.incrementRetryCount();
        task.setTaskStatus(AnalysisTask.TaskStatus.PENDING);
        task.setStatusMessage("准备重试...");
        task.setErrorMessage(null);
        updateById(task);
        
        // 重新启动任务
        return startTask(taskId, task.getChatId(), task.getFileId());
    }

    @Override
    public void cancelTask(Long taskId) {
        log.info("取消任务: taskId={}", taskId);
        
        UpdateWrapper<AnalysisTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", taskId)
                .set("task_status", AnalysisTask.TaskStatus.FAILED.name())
                .set("status_message", "任务已取消")
                .set("completed_time", LocalDateTime.now());
        
        update(updateWrapper);
    }

    @Override
    public int cleanupExpiredTasks(int days) {
        log.info("清理过期任务: days={}", days);
        return baseMapper.cleanupExpiredTasks(days);
    }

    @Override
    public Map<MaxKBApiClient.AgentType, CompletableFuture<AnalysisTask>> startAllAnalysisTasksAsync(Long documentId, String filePath) {
        log.info("异步启动文档的所有分析任务: documentId={}, filePath={}", documentId, filePath);
        
        Map<MaxKBApiClient.AgentType, CompletableFuture<AnalysisTask>> futures = new HashMap<>();
        
        // 创建所有任务
        List<AnalysisTask> tasks = createAllTasksForDocument(documentId);
        
        // 异步启动每个任务
        for (AnalysisTask task : tasks) {
            CompletableFuture<AnalysisTask> future = CompletableFuture
                    .supplyAsync(() -> {
                        try {
                            log.info("启动{}任务: taskId={}, documentId={}",
                                task.getAgentTypeDisplay(), task.getId(), documentId);

                            // 调用MaxKB服务创建会话和上传文件
                            Map<String, String> sessionInfo = maxKBService
                                .createSessionAndUploadFile(task.getAgentType(), filePath).get();

                            String chatId = sessionInfo.get("chatId");
                            String fileId = sessionInfo.get("fileId");

                            log.info("{}会话和文件准备完成: taskId={}, chatId={}, fileId={}",
                                task.getAgentTypeDisplay(), task.getId(), chatId, fileId);

                            // 启动分析任务
                            return startTask(task.getId(), chatId, fileId).get();

                        } catch (Exception e) {
                            log.error("启动{}任务失败: taskId={}", task.getAgentTypeDisplay(), task.getId(), e);
                            markTaskFailed(task.getId(), e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });

            futures.put(task.getAgentType(), future);
        }
        
        return futures;
    }

    @Override
    public int checkAndUpdateTimeoutTasks(int timeoutMinutes) {
        log.info("检查并更新超时任务: timeoutMinutes={}", timeoutMinutes);
        
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);
        
        QueryWrapper<AnalysisTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_status", AnalysisTask.TaskStatus.PROCESSING.name())
                .lt("started_time", timeoutThreshold);
        
        List<AnalysisTask> timeoutTasks = list(queryWrapper);
        
        for (AnalysisTask task : timeoutTasks) {
            markTaskFailed(task.getId(), "任务执行超时");
        }
        
        return timeoutTasks.size();
    }

    @Override
    public Map<String, Object> getTaskExecutionSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        // 获取各种状态的任务数量
        QueryWrapper<AnalysisTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("task_status", "COUNT(*) as count")
                .groupBy("task_status");
        
        List<Map<String, Object>> statusCounts = listMaps(queryWrapper);
        summary.put("statusCounts", statusCounts);
        
        // 获取最近24小时的任务
        summary.put("recentTasks", getRecentTasks(24, 10));
        
        // 获取处理中的任务
        summary.put("processingTasks", getProcessingTasks().size());
        
        // 获取等待中的任务
        summary.put("pendingTasks", getPendingTasks().size());
        
        return summary;
    }

    /**
     * 保存分析结果到数据库
     */
    private Long saveAnalysisResult(Long documentId, MaxKBApiClient.AgentType agentType, Map<String, Object> result) {
        try {
            log.info("保存分析结果: documentId={}, agentType={}", documentId, agentType);

            // 创建分析结果实体
            AnalysisResult analysisResult = new AnalysisResult();
            analysisResult.setDocumentId(documentId);

            // 转换智能体类型
            if (agentType == MaxKBApiClient.AgentType.EXTRACT) {
                analysisResult.setAgentType(AnalysisResult.AgentType.EXTRACT);
            } else {
                analysisResult.setAgentType(AnalysisResult.AgentType.DETECT);
            }

            // 解析并设置结果内容
            Map<String, Object> parsedContent = parseAnalysisContent(result, agentType);
            analysisResult.setResultContent(parsedContent);

            // 设置其他字段
            analysisResult.setProcessingTime(1000); // 临时设置处理时间
            analysisResult.setApiResponseCode(200);
            analysisResult.setCreatedTime(LocalDateTime.now());
    

            // 保存到数据库
            analysisResultService.save(analysisResult);

            log.info("分析结果保存成功: resultId={}", analysisResult.getId());
            return analysisResult.getId();

        } catch (Exception e) {
            log.error("保存分析结果失败: documentId={}, agentType={}", documentId, agentType, e);
            return null;
        }
    }

    /**
     * 解析分析内容，将MaxKB返回的JSON转换为结构化数据
     */
    private Map<String, Object> parseAnalysisContent(Map<String, Object> result, MaxKBApiClient.AgentType agentType) {
        Map<String, Object> parsedContent = new java.util.HashMap<>();

        try {
            // 获取data字段
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            if (data == null) {
                log.warn("分析结果中没有data字段");
                return parsedContent;
            }

            // 获取content字段
            String content = (String) data.get("content");
            if (content == null || content.trim().isEmpty()) {
                log.warn("分析结果中没有content字段或内容为空");
                return parsedContent;
            }

            log.info("开始解析{}内容，长度: {}", agentType.getDescription(), content.length());

            // 根据智能体类型解析内容
            if (agentType == MaxKBApiClient.AgentType.EXTRACT) {
                parsedContent = parseExtractContent(content);
            } else {
                parsedContent = parseDetectContent(content);
            }

            // 添加原始数据
            parsedContent.put("originalData", data);
            parsedContent.put("rawContent", content);

        } catch (Exception e) {
            log.error("解析分析内容失败", e);
            parsedContent.put("error", "解析失败: " + e.getMessage());
            parsedContent.put("originalResult", result);
        }

        return parsedContent;
    }

    /**
     * 解析提取内容
     */
    private Map<String, Object> parseExtractContent(String content) {
        Map<String, Object> result = new java.util.HashMap<>();
        java.util.List<Map<String, Object>> extractedData = new java.util.ArrayList<>();

        try {
            // 按行分割内容
            String[] lines = content.split("\n");
            Map<String, Object> currentItem = null;
            StringBuilder contentBuilder = new StringBuilder();

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // 检查是否是新的提取点（格式：数字、提取点名称：xxx）
                if (line.matches("^\\d+、提取点名称：.*")) {
                    // 保存上一个项目
                    if (currentItem != null) {
                        currentItem.put("content", contentBuilder.toString().trim());
                        extractedData.add(currentItem);
                    }

                    // 开始新项目
                    currentItem = new java.util.HashMap<>();
                    String title = line.replaceFirst("^\\d+、提取点名称：", "");
                    currentItem.put("type", "extract");
                    currentItem.put("title", title);
                    currentItem.put("name", title);
                    currentItem.put("importance", "normal");
                    contentBuilder = new StringBuilder();

                } else if (currentItem != null) {
                    // 添加到当前项目的内容
                    if (contentBuilder.length() > 0) {
                        contentBuilder.append("\n");
                    }
                    contentBuilder.append(line);
                }
            }

            // 保存最后一个项目
            if (currentItem != null) {
                currentItem.put("content", contentBuilder.toString().trim());
                extractedData.add(currentItem);
            }

            result.put("extractedData", extractedData);
            result.put("totalCount", extractedData.size());

            log.info("解析提取内容完成，共{}个提取点", extractedData.size());

        } catch (Exception e) {
            log.error("解析提取内容失败", e);
            result.put("error", "解析失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 解析检测内容
     */
    private Map<String, Object> parseDetectContent(String content) {
        Map<String, Object> result = new java.util.HashMap<>();
        java.util.List<Map<String, Object>> detectedIssues = new java.util.ArrayList<>();

        try {
            // 按行分割内容
            String[] lines = content.split("\n");
            Map<String, Object> currentItem = null;
            StringBuilder contentBuilder = new StringBuilder();

            for (String line : lines) {
                line = line.trim();
                if (line.isEmpty()) continue;

                // 检查是否是新的检测点（格式：数字、检测点名称：xxx）
                if (line.matches("^\\d+、检测点名称：.*")) {
                    // 保存上一个项目
                    if (currentItem != null) {
                        currentItem.put("description", contentBuilder.toString().trim());
                        detectedIssues.add(currentItem);
                    }

                    // 开始新项目
                    currentItem = new java.util.HashMap<>();
                    String title = line.replaceFirst("^\\d+、检测点名称：", "");
                    currentItem.put("type", "detect");
                    currentItem.put("title", title);
                    currentItem.put("name", title);
                    currentItem.put("severity", "medium"); // 默认中等严重性
                    contentBuilder = new StringBuilder();

                } else if (currentItem != null) {
                    // 检查是否包含检测结果
                    if (line.contains("检测结果：异常结果")) {
                        currentItem.put("severity", "high");
                        currentItem.put("status", "异常");
                    } else if (line.contains("检测结果：正常结果")) {
                        currentItem.put("severity", "low");
                        currentItem.put("status", "正常");
                    }

                    // 添加到当前项目的内容
                    if (contentBuilder.length() > 0) {
                        contentBuilder.append("\n");
                    }
                    contentBuilder.append(line);
                }
            }

            // 保存最后一个项目
            if (currentItem != null) {
                currentItem.put("description", contentBuilder.toString().trim());
                detectedIssues.add(currentItem);
            }

            result.put("detectedIssues", detectedIssues);
            result.put("totalCount", detectedIssues.size());

            // 统计异常和正常的数量
            long abnormalCount = detectedIssues.stream()
                    .filter(item -> "异常".equals(item.get("status")))
                    .count();
            long normalCount = detectedIssues.size() - abnormalCount;

            result.put("abnormalCount", abnormalCount);
            result.put("normalCount", normalCount);

            log.info("解析检测内容完成，共{}个检测点，异常{}个，正常{}个",
                    detectedIssues.size(), abnormalCount, normalCount);

        } catch (Exception e) {
            log.error("解析检测内容失败", e);
            result.put("error", "解析失败: " + e.getMessage());
        }

        return result;
    }
}
