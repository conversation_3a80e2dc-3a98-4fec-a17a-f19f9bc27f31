package com.docanalysis.service;

import com.docanalysis.dto.StructuredAnalysisResultDTO;
import com.docanalysis.entity.AnalysisResult;

import java.util.List;


public interface AnalysisContentParserService {

    /**
     * 将原始分析结果转换为结构化数据
     * 
     * @param documentId 文档ID
     * @param analysisResults 原始分析结果列表
     * @return 结构化分析结果
     */
    StructuredAnalysisResultDTO parseToStructuredResult(Long documentId, List<AnalysisResult> analysisResults);

    /**
     * 解析提取类型的分析结果
     * 
     * @param analysisResult 分析结果
     * @return 提取项列表
     */
    List<StructuredAnalysisResultDTO.ExtractItem> parseExtractResult(AnalysisResult analysisResult);

    /**
     * 解析检测类型的分析结果
     * 
     * @param analysisResult 分析结果
     * @return 检测项列表
     */
    List<StructuredAnalysisResultDTO.DetectItem> parseDetectResult(AnalysisResult analysisResult);

    /**
     * 从文本内容中解析提取项
     * 
     * @param content 文本内容
     * @return 提取项列表
     */
    List<StructuredAnalysisResultDTO.ExtractItem> parseExtractContent(String content);

    /**
     * 从文本内容中解析检测项
     * 
     * @param content 文本内容
     * @return 检测项列表
     */
    List<StructuredAnalysisResultDTO.DetectItem> parseDetectContent(String content);
}
