package com.docanalysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.docanalysis.entity.OperationLog;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Mapper
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 根据操作类型查询日志
     */
    @Select("SELECT * FROM operation_logs WHERE operation_type = #{operationType} ORDER BY created_time DESC")
    List<OperationLog> selectByOperationType(@Param("operationType") String operationType);

    /**
     * 根据操作类型分页查询
     */
    @Select("SELECT * FROM operation_logs WHERE operation_type = #{operationType} ORDER BY created_time DESC")
    IPage<OperationLog> selectPageByOperationType(Page<OperationLog> page, @Param("operationType") String operationType);

    /**
     * 根据文档ID查询操作日志
     */
    @Select("SELECT * FROM operation_logs WHERE document_id = #{documentId} ORDER BY created_time DESC")
    List<OperationLog> selectByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据状态查询日志
     */
    @Select("SELECT * FROM operation_logs WHERE status = #{status} ORDER BY created_time DESC")
    List<OperationLog> selectByStatus(@Param("status") OperationLog.OperationStatus status);

    /**
     * 查询指定时间范围内的日志
     */
    @Select("SELECT * FROM operation_logs WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY created_time DESC")
    List<OperationLog> selectByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询失败的操作日志
     */
    @Select("SELECT * FROM operation_logs WHERE status = 'FAILED' ORDER BY created_time DESC")
    List<OperationLog> selectFailedOperations();

    /**
     * 查询执行时间超过阈值的操作
     */
    @Select("SELECT * FROM operation_logs WHERE execution_time > #{threshold} ORDER BY execution_time DESC")
    List<OperationLog> selectByExecutionTimeThreshold(@Param("threshold") Integer threshold);

    /**
     * 统计各操作类型的数量
     */
    @Select("SELECT operation_type, COUNT(*) as count FROM operation_logs GROUP BY operation_type")
    List<Map<String, Object>> countByOperationType();

    /**
     * 统计各状态的操作数量
     */
    @Select("SELECT status, COUNT(*) as count FROM operation_logs GROUP BY status")
    List<Map<String, Object>> countByStatus();

    /**
     * 查询操作成功率
     */
    @Select("SELECT " +
            "operation_type, " +
            "COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) * 100.0 / COUNT(*) as success_rate " +
            "FROM operation_logs " +
            "WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY operation_type")
    List<Map<String, Object>> getSuccessRateByOperationType(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询平均执行时间
     */
    @Select("SELECT operation_type, AVG(execution_time) as avg_time, COUNT(*) as count " +
            "FROM operation_logs WHERE execution_time IS NOT NULL " +
            "GROUP BY operation_type")
    List<Map<String, Object>> getAverageExecutionTimeByOperationType();

    /**
     * 查询最近的操作日志
     */
    @Select("SELECT * FROM operation_logs ORDER BY created_time DESC LIMIT #{limit}")
    List<OperationLog> selectRecentLogs(@Param("limit") Integer limit);

    /**
     * 查询指定IP地址的操作日志
     */
    @Select("SELECT * FROM operation_logs WHERE ip_address = #{ipAddress} ORDER BY created_time DESC")
    List<OperationLog> selectByIpAddress(@Param("ipAddress") String ipAddress);

    /**
     * 查询包含错误信息的日志
     */
    @Select("SELECT * FROM operation_logs WHERE error_message IS NOT NULL AND error_message != '' " +
            "ORDER BY created_time DESC")
    List<OperationLog> selectWithErrors();

    /**
     * 查询操作趋势（按小时统计）
     */
    @Select("SELECT " +
            "DATE_FORMAT(created_time, '%Y-%m-%d %H:00:00') as hour_time, " +
            "COUNT(*) as operation_count, " +
            "COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_count, " +
            "COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_count " +
            "FROM operation_logs " +
            "WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE_FORMAT(created_time, '%Y-%m-%d %H:00:00') " +
            "ORDER BY hour_time")
    List<Map<String, Object>> getOperationTrendByHour(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 删除过期的操作日志
     */
    @Delete("DELETE FROM operation_logs WHERE created_time < #{expireTime}")
    int deleteExpiredLogs(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 复合条件查询操作日志
     */
    @Select("<script>" +
            "SELECT * FROM operation_logs WHERE 1=1 " +
            "<if test='operationType != null and operationType != &quot;&quot;'> AND operation_type = #{operationType} </if>" +
            "<if test='documentId != null'> AND document_id = #{documentId} </if>" +
            "<if test='status != null'> AND status = #{status} </if>" +
            "<if test='ipAddress != null and ipAddress != &quot;&quot;'> AND ip_address = #{ipAddress} </if>" +
            "<if test='minExecutionTime != null'> AND execution_time &gt;= #{minExecutionTime} </if>" +
            "<if test='maxExecutionTime != null'> AND execution_time &lt;= #{maxExecutionTime} </if>" +
            "<if test='hasError != null and hasError'> AND error_message IS NOT NULL AND error_message != '' </if>" +
            "<if test='startTime != null'> AND created_time &gt;= #{startTime} </if>" +
            "<if test='endTime != null'> AND created_time &lt;= #{endTime} </if>" +
            "ORDER BY created_time DESC" +
            "</script>")
    IPage<OperationLog> selectByConditions(
            Page<OperationLog> page,
            @Param("operationType") String operationType,
            @Param("documentId") Long documentId,
            @Param("status") OperationLog.OperationStatus status,
            @Param("ipAddress") String ipAddress,
            @Param("minExecutionTime") Integer minExecutionTime,
            @Param("maxExecutionTime") Integer maxExecutionTime,
            @Param("hasError") Boolean hasError,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );
}
