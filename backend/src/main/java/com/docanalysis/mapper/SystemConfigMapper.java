package com.docanalysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.docanalysis.entity.SystemConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


@Mapper
public interface SystemConfigMapper extends BaseMapper<SystemConfig> {

    /**
     * 根据配置键查询配置
     */
    @Select("SELECT * FROM system_config WHERE config_key = #{configKey}")
    SystemConfig selectByConfigKey(@Param("configKey") String configKey);

    /**
     * 查询所有启用的配置
     */
    @Select("SELECT * FROM system_config WHERE is_active = true ORDER BY config_key")
    List<SystemConfig> selectActiveConfigs();

    /**
     * 根据配置类型查询配置
     */
    @Select("SELECT * FROM system_config WHERE config_type = #{configType} ORDER BY config_key")
    List<SystemConfig> selectByConfigType(@Param("configType") SystemConfig.ConfigType configType);

    /**
     * 根据配置类型查询启用的配置
     */
    @Select("SELECT * FROM system_config WHERE config_type = #{configType} AND is_active = true ORDER BY config_key")
    List<SystemConfig> selectActiveConfigsByType(@Param("configType") SystemConfig.ConfigType configType);

    /**
     * 查询配置键包含指定文本的配置
     */
    @Select("SELECT * FROM system_config WHERE config_key LIKE CONCAT('%', #{keyPattern}, '%') ORDER BY config_key")
    List<SystemConfig> selectByConfigKeyPattern(@Param("keyPattern") String keyPattern);

    /**
     * 批量查询配置
     */
    @Select("<script>" +
            "SELECT * FROM system_config WHERE config_key IN " +
            "<foreach collection='configKeys' item='key' open='(' separator=',' close=')'>" +
            "#{key}" +
            "</foreach>" +
            "ORDER BY config_key" +
            "</script>")
    List<SystemConfig> selectByConfigKeys(@Param("configKeys") List<String> configKeys);

    /**
     * 查询所有配置键
     */
    @Select("SELECT config_key FROM system_config ORDER BY config_key")
    List<String> selectAllConfigKeys();

    /**
     * 检查配置键是否存在
     */
    @Select("SELECT COUNT(*) FROM system_config WHERE config_key = #{configKey}")
    int countByConfigKey(@Param("configKey") String configKey);

    /**
     * 更新配置值
     */
    @Update("UPDATE system_config SET config_value = #{configValue}, updated_time = NOW() " +
            "WHERE config_key = #{configKey}")
    int updateConfigValue(@Param("configKey") String configKey, @Param("configValue") String configValue);

    /**
     * 启用配置
     */
    @Update("UPDATE system_config SET is_active = true, updated_time = NOW() WHERE config_key = #{configKey}")
    int enableConfig(@Param("configKey") String configKey);

    /**
     * 禁用配置
     */
    @Update("UPDATE system_config SET is_active = false, updated_time = NOW() WHERE config_key = #{configKey}")
    int disableConfig(@Param("configKey") String configKey);

    /**
     * 批量启用配置
     */
    @Update("<script>" +
            "UPDATE system_config SET is_active = true, updated_time = NOW() WHERE config_key IN " +
            "<foreach collection='configKeys' item='key' open='(' separator=',' close=')'>" +
            "#{key}" +
            "</foreach>" +
            "</script>")
    int batchEnableConfigs(@Param("configKeys") List<String> configKeys);

    /**
     * 批量禁用配置
     */
    @Update("<script>" +
            "UPDATE system_config SET is_active = false, updated_time = NOW() WHERE config_key IN " +
            "<foreach collection='configKeys' item='key' open='(' separator=',' close=')'>" +
            "#{key}" +
            "</foreach>" +
            "</script>")
    int batchDisableConfigs(@Param("configKeys") List<String> configKeys);
}
