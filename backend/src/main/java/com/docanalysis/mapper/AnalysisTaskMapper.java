package com.docanalysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.docanalysis.entity.AnalysisTask;
import com.docanalysis.util.MaxKBApiClient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;


@Mapper
public interface AnalysisTaskMapper extends BaseMapper<AnalysisTask> {

    /**
     * 根据文档ID获取所有分析任务
     */
    @Select("SELECT * FROM analysis_tasks WHERE document_id = #{documentId} ORDER BY created_time DESC")
    List<AnalysisTask> selectByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据文档ID和智能体类型获取任务
     */
    @Select("SELECT * FROM analysis_tasks WHERE document_id = #{documentId} AND agent_type = #{agentType} ORDER BY created_time DESC LIMIT 1")
    AnalysisTask selectByDocumentIdAndAgentType(@Param("documentId") Long documentId, @Param("agentType") MaxKBApiClient.AgentType agentType);

    /**
     * 获取文档的分析进度概览
     */
    @Select("SELECT * FROM v_document_analysis_overview WHERE document_id = #{documentId}")
    Map<String, Object> selectDocumentAnalysisOverview(@Param("documentId") Long documentId);

    /**
     * 获取所有进行中的任务
     */
    @Select("SELECT * FROM analysis_tasks WHERE task_status = 'PROCESSING' ORDER BY created_time ASC")
    List<AnalysisTask> selectProcessingTasks();

    /**
     * 获取等待中的任务
     */
    @Select("SELECT * FROM analysis_tasks WHERE task_status = 'PENDING' ORDER BY created_time ASC")
    List<AnalysisTask> selectPendingTasks();

    /**
     * 批量更新任务进度
     */
    @Update("UPDATE analysis_tasks SET progress_percentage = #{progress}, status_message = #{message} WHERE id = #{taskId}")
    int updateProgress(@Param("taskId") Long taskId, @Param("progress") Integer progress, @Param("message") String message);

    /**
     * 更新任务状态
     */
    @Update("UPDATE analysis_tasks SET task_status = #{status}, status_message = #{message}, " +
            "started_time = CASE WHEN #{status} = 'PROCESSING' AND started_time IS NULL THEN NOW() ELSE started_time END, " +
            "completed_time = CASE WHEN #{status} IN ('COMPLETED', 'FAILED') THEN NOW() ELSE completed_time END " +
            "WHERE id = #{taskId}")
    int updateTaskStatus(@Param("taskId") Long taskId, @Param("status") String status, @Param("message") String message);

    /**
     * 获取任务统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_tasks, " +
            "SUM(CASE WHEN task_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks, " +
            "SUM(CASE WHEN task_status = 'PROCESSING' THEN 1 ELSE 0 END) as processing_tasks, " +
            "SUM(CASE WHEN task_status = 'PENDING' THEN 1 ELSE 0 END) as pending_tasks, " +
            "SUM(CASE WHEN task_status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks, " +
            "AVG(processing_duration_ms) as avg_processing_time " +
            "FROM analysis_tasks WHERE document_id = #{documentId}")
    Map<String, Object> selectTaskStatistics(@Param("documentId") Long documentId);

    /**
     * 获取最近的任务列表（用于监控）
     */
    @Select("SELECT at.*, d.original_name as document_name " +
            "FROM analysis_tasks at " +
            "LEFT JOIN documents d ON at.document_id = d.id " +
            "WHERE at.created_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR) " +
            "ORDER BY at.created_time DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectRecentTasks(@Param("hours") int hours, @Param("limit") int limit);

    /**
     * 清理过期的已完成任务
     */
    @Update("DELETE FROM analysis_tasks WHERE task_status IN ('COMPLETED', 'FAILED') " +
            "AND completed_time < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    int cleanupExpiredTasks(@Param("days") int days);

    /**
     * 获取任务详细进度信息
     */
    @Select("SELECT " +
            "at.id, " +
            "at.document_id, " +
            "d.original_name as document_name, " +
            "at.agent_type, " +
            "at.task_status, " +
            "at.progress_percentage, " +
            "at.status_message, " +
            "at.error_message, " +
            "at.created_time, " +
            "at.started_time, " +
            "at.completed_time, " +
            "at.processing_duration_ms, " +
            "at.retry_count, " +
            "CASE " +
            "  WHEN at.agent_type = 'EXTRACT' THEN '数据提取' " +
            "  WHEN at.agent_type = 'DETECT' THEN '异常检测' " +
            "  ELSE '未知类型' " +
            "END as agent_display, " +
            "CASE " +
            "  WHEN at.task_status = 'COMPLETED' THEN '已完成' " +
            "  WHEN at.task_status = 'PROCESSING' THEN '处理中' " +
            "  WHEN at.task_status = 'PENDING' THEN '等待中' " +
            "  WHEN at.task_status = 'FAILED' THEN '失败' " +
            "  ELSE '未知' " +
            "END as status_display " +
            "FROM analysis_tasks at " +
            "LEFT JOIN documents d ON at.document_id = d.id " +
            "WHERE at.document_id = #{documentId} " +
            "ORDER BY at.created_time DESC")
    List<Map<String, Object>> selectTaskProgressDetails(@Param("documentId") Long documentId);

    /**
     * 检查文档是否有进行中的任务
     */
    @Select("SELECT COUNT(*) > 0 FROM analysis_tasks " +
            "WHERE document_id = #{documentId} AND task_status IN ('PENDING', 'PROCESSING')")
    boolean hasActiveTasksForDocument(@Param("documentId") Long documentId);

    /**
     * 获取文档的整体分析状态
     */
    @Select("SELECT " +
            "document_id, " +
            "COUNT(*) as total_tasks, " +
            "SUM(CASE WHEN task_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_count, " +
            "SUM(CASE WHEN task_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count, " +
            "SUM(CASE WHEN task_status = 'PROCESSING' THEN 1 ELSE 0 END) as processing_count, " +
            "AVG(progress_percentage) as overall_progress, " +
            "CASE " +
            "  WHEN SUM(CASE WHEN task_status = 'COMPLETED' THEN 1 ELSE 0 END) = COUNT(*) THEN 'COMPLETED' " +
            "  WHEN SUM(CASE WHEN task_status = 'FAILED' THEN 1 ELSE 0 END) > 0 THEN 'FAILED' " +
            "  WHEN SUM(CASE WHEN task_status = 'PROCESSING' THEN 1 ELSE 0 END) > 0 THEN 'PROCESSING' " +
            "  ELSE 'PENDING' " +
            "END as overall_status " +
            "FROM analysis_tasks " +
            "WHERE document_id = #{documentId} " +
            "GROUP BY document_id")
    Map<String, Object> selectDocumentOverallStatus(@Param("documentId") Long documentId);
}
