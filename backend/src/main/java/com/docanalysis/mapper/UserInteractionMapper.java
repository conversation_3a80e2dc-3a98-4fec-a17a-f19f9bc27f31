package com.docanalysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.docanalysis.entity.UserInteraction;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Mapper
public interface UserInteractionMapper extends BaseMapper<UserInteraction> {

    /**
     * 根据文档ID查询用户交互记录
     */
    @Select("SELECT * FROM user_interactions WHERE document_id = #{documentId} ORDER BY created_time DESC")
    List<UserInteraction> selectByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据会话ID查询用户交互记录
     */
    @Select("SELECT * FROM user_interactions WHERE session_id = #{sessionId} ORDER BY created_time ASC")
    List<UserInteraction> selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据交互类型查询
     */
    @Select("SELECT * FROM user_interactions WHERE interaction_type = #{interactionType} " +
            "ORDER BY created_time DESC")
    List<UserInteraction> selectByInteractionType(@Param("interactionType") String interactionType);

    /**
     * 根据交互类型分页查询
     */
    @Select("SELECT * FROM user_interactions WHERE interaction_type = #{interactionType} " +
            "ORDER BY created_time DESC")
    IPage<UserInteraction> selectPageByInteractionType(
            Page<UserInteraction> page, 
            @Param("interactionType") String interactionType
    );

    /**
     * 查询指定时间范围内的用户交互
     */
    @Select("SELECT * FROM user_interactions WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY created_time DESC")
    List<UserInteraction> selectByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 根据IP地址查询用户交互
     */
    @Select("SELECT * FROM user_interactions WHERE ip_address = #{ipAddress} ORDER BY created_time DESC")
    List<UserInteraction> selectByIpAddress(@Param("ipAddress") String ipAddress);

    /**
     * 统计各交互类型的数量
     */
    @Select("SELECT interaction_type, COUNT(*) as count FROM user_interactions GROUP BY interaction_type")
    List<Map<String, Object>> countByInteractionType();

    /**
     * 统计指定时间段内的交互类型分布
     */
    @Select("SELECT interaction_type, COUNT(*) as count " +
            "FROM user_interactions " +
            "WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY interaction_type")
    List<Map<String, Object>> countByInteractionTypeAndTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询活跃用户（根据会话ID统计）
     */
    @Select("SELECT session_id, COUNT(*) as interaction_count, MAX(created_time) as last_interaction " +
            "FROM user_interactions " +
            "WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY session_id " +
            "ORDER BY interaction_count DESC")
    List<Map<String, Object>> getActiveUsersByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询文档的用户交互统计
     */
    @Select("SELECT " +
            "document_id, " +
            "COUNT(*) as total_interactions, " +
            "COUNT(DISTINCT session_id) as unique_users, " +
            "COUNT(CASE WHEN interaction_type = 'highlight' THEN 1 END) as highlight_count, " +
            "COUNT(CASE WHEN interaction_type = 'click' THEN 1 END) as click_count " +
            "FROM user_interactions " +
            "WHERE document_id = #{documentId} " +
            "GROUP BY document_id")
    Map<String, Object> getDocumentInteractionStatistics(@Param("documentId") Long documentId);

    /**
     * 查询热点位置（高频交互位置）
     */
    @Select("SELECT " +
            "JSON_EXTRACT(position_info, '$.page') as page_number, " +
            "COUNT(*) as interaction_count " +
            "FROM user_interactions " +
            "WHERE document_id = #{documentId} " +
            "AND position_info IS NOT NULL " +
            "AND JSON_EXTRACT(position_info, '$.page') IS NOT NULL " +
            "GROUP BY JSON_EXTRACT(position_info, '$.page') " +
            "ORDER BY interaction_count DESC")
    List<Map<String, Object>> getHotspotsByDocument(@Param("documentId") Long documentId);

    /**
     * 查询用户行为路径（按会话ID和时间排序）
     */
    @Select("SELECT interaction_type, content, position_info, created_time " +
            "FROM user_interactions " +
            "WHERE session_id = #{sessionId} " +
            "ORDER BY created_time ASC")
    List<UserInteraction> getUserBehaviorPath(@Param("sessionId") String sessionId);

    /**
     * 查询最近的用户交互
     */
    @Select("SELECT * FROM user_interactions ORDER BY created_time DESC LIMIT #{limit}")
    List<UserInteraction> selectRecentInteractions(@Param("limit") Integer limit);

    /**
     * 查询指定文档的最近交互
     */
    @Select("SELECT * FROM user_interactions WHERE document_id = #{documentId} " +
            "ORDER BY created_time DESC LIMIT #{limit}")
    List<UserInteraction> selectRecentInteractionsByDocument(
            @Param("documentId") Long documentId,
            @Param("limit") Integer limit
    );

    /**
     * 查询用户交互趋势（按小时统计）
     */
    @Select("SELECT " +
            "DATE_FORMAT(created_time, '%Y-%m-%d %H:00:00') as hour_time, " +
            "COUNT(*) as interaction_count " +
            "FROM user_interactions " +
            "WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY DATE_FORMAT(created_time, '%Y-%m-%d %H:00:00') " +
            "ORDER BY hour_time")
    List<Map<String, Object>> getInteractionTrendByHour(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 删除过期的用户交互记录
     */
    @Delete("DELETE FROM user_interactions WHERE created_time < #{expireTime}")
    int deleteExpiredInteractions(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 查询包含特定内容的交互记录
     */
    @Select("SELECT * FROM user_interactions WHERE content LIKE CONCAT('%', #{searchText}, '%') " +
            "ORDER BY created_time DESC")
    List<UserInteraction> selectByContentSearch(@Param("searchText") String searchText);

    /**
     * 复合条件查询用户交互
     */
    @Select("<script>" +
            "SELECT * FROM user_interactions WHERE 1=1 " +
            "<if test='documentId != null'> AND document_id = #{documentId} </if>" +
            "<if test='sessionId != null and sessionId != &quot;&quot;'> AND session_id = #{sessionId} </if>" +
            "<if test='interactionType != null and interactionType != &quot;&quot;'> AND interaction_type = #{interactionType} </if>" +
            "<if test='ipAddress != null and ipAddress != &quot;&quot;'> AND ip_address = #{ipAddress} </if>" +
            "<if test='content != null and content != &quot;&quot;'> AND content LIKE CONCAT('%', #{content}, '%') </if>" +
            "<if test='startTime != null'> AND created_time &gt;= #{startTime} </if>" +
            "<if test='endTime != null'> AND created_time &lt;= #{endTime} </if>" +
            "ORDER BY created_time DESC" +
            "</script>")
    IPage<UserInteraction> selectByConditions(
            Page<UserInteraction> page,
            @Param("documentId") Long documentId,
            @Param("sessionId") String sessionId,
            @Param("interactionType") String interactionType,
            @Param("ipAddress") String ipAddress,
            @Param("content") String content,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );
}
