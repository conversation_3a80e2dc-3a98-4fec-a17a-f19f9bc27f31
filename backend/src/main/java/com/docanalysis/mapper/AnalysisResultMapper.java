package com.docanalysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.docanalysis.entity.AnalysisResult;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Mapper
public interface AnalysisResultMapper extends BaseMapper<AnalysisResult> {

    /**
     * 根据文档ID查询分析结果
     */
    @Select("SELECT * FROM analysis_results WHERE document_id = #{documentId} ORDER BY created_time DESC")
    List<AnalysisResult> selectByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据文档ID和智能体类型查询分析结果
     */
    @Select("SELECT * FROM analysis_results WHERE document_id = #{documentId} AND agent_type = #{agentType} " +
            "ORDER BY created_time DESC")
    List<AnalysisResult> selectByDocumentIdAndAgentType(
            @Param("documentId") Long documentId,
            @Param("agentType") AnalysisResult.AgentType agentType
    );

    /**
     * 查询最新的分析结果
     */
    @Select("SELECT * FROM analysis_results WHERE document_id = #{documentId} AND agent_type = #{agentType} " +
            "ORDER BY created_time DESC LIMIT 1")
    AnalysisResult selectLatestByDocumentIdAndAgentType(
            @Param("documentId") Long documentId,
            @Param("agentType") AnalysisResult.AgentType agentType
    );

    /**
     * 根据智能体类型分页查询
     */
    @Select("SELECT * FROM analysis_results WHERE agent_type = #{agentType} ORDER BY created_time DESC")
    IPage<AnalysisResult> selectPageByAgentType(Page<AnalysisResult> page, @Param("agentType") AnalysisResult.AgentType agentType);

    /**
     * 查询指定时间范围内的分析结果
     */
    @Select("SELECT * FROM analysis_results WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY created_time DESC")
    List<AnalysisResult> selectByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 统计各智能体类型的分析结果数量
     */
    @Select("SELECT agent_type, COUNT(*) as count FROM analysis_results GROUP BY agent_type")
    List<Map<String, Object>> countByAgentType();

    /**
     * 查询处理时间超过阈值的分析结果
     */
    @Select("SELECT * FROM analysis_results WHERE processing_time > #{threshold} ORDER BY processing_time DESC")
    List<AnalysisResult> selectByProcessingTimeThreshold(@Param("threshold") Integer threshold);

    /**
     * 查询API调用失败的分析结果
     */
    @Select("SELECT * FROM analysis_results WHERE api_response_code != 200 OR api_response_code IS NULL " +
            "ORDER BY created_time DESC")
    List<AnalysisResult> selectFailedApiCalls();

    /**
     * 查询成功的分析结果
     */
    @Select("SELECT * FROM analysis_results WHERE api_response_code = 200 " +
            "AND JSON_EXTRACT(result_content, '$.status') = 'success' " +
            "ORDER BY created_time DESC")
    List<AnalysisResult> selectSuccessfulResults();

    /**
     * 根据结果状态查询
     */
    @Select("SELECT * FROM analysis_results WHERE JSON_EXTRACT(result_content, '$.status') = #{status} " +
            "ORDER BY created_time DESC")
    List<AnalysisResult> selectByResultStatus(@Param("status") String status);

    /**
     * 查询平均处理时间
     */
    @Select("SELECT agent_type, AVG(processing_time) as avg_time, COUNT(*) as count " +
            "FROM analysis_results WHERE processing_time IS NOT NULL " +
            "GROUP BY agent_type")
    List<Map<String, Object>> getAverageProcessingTimeByAgentType();

    /**
     * 查询指定文档的分析完成情况
     */
    @Select("SELECT " +
            "document_id, " +
            "COUNT(CASE WHEN agent_type = 'EXTRACT' THEN 1 END) as extract_count, " +
            "COUNT(CASE WHEN agent_type = 'DETECT' THEN 1 END) as detect_count, " +
            "COUNT(CASE WHEN api_response_code = 200 THEN 1 END) as success_count " +
            "FROM analysis_results " +
            "WHERE document_id = #{documentId} " +
            "GROUP BY document_id")
    Map<String, Object> getAnalysisCompletionStatus(@Param("documentId") Long documentId);

    /**
     * 查询分析结果统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "COUNT(CASE WHEN api_response_code = 200 THEN 1 END) as success_count, " +
            "COUNT(CASE WHEN api_response_code != 200 OR api_response_code IS NULL THEN 1 END) as failed_count, " +
            "AVG(processing_time) as avg_processing_time, " +
            "MAX(processing_time) as max_processing_time, " +
            "MIN(processing_time) as min_processing_time " +
            "FROM analysis_results " +
            "WHERE created_time BETWEEN #{startTime} AND #{endTime}")
    Map<String, Object> getStatisticsByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询API调用成功率
     */
    @Select("SELECT " +
            "agent_type, " +
            "COUNT(CASE WHEN api_response_code = 200 THEN 1 END) * 100.0 / COUNT(*) as success_rate " +
            "FROM analysis_results " +
            "WHERE created_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY agent_type")
    List<Map<String, Object>> getApiSuccessRateByAgentType(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询包含特定内容的分析结果
     */
    @Select("SELECT * FROM analysis_results " +
            "WHERE JSON_SEARCH(result_content, 'one', #{searchText}) IS NOT NULL " +
            "ORDER BY created_time DESC")
    List<AnalysisResult> selectByContentSearch(@Param("searchText") String searchText);

    /**
     * 删除过期的分析结果
     */
    @Delete("DELETE FROM analysis_results WHERE created_time < #{expireTime}")
    int deleteExpiredResults(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 复合条件查询分析结果
     */
    @Select("<script>" +
            "SELECT * FROM analysis_results WHERE 1=1 " +
            "<if test='documentId != null'> AND document_id = #{documentId} </if>" +
            "<if test='agentType != null'> AND agent_type = #{agentType} </if>" +
            "<if test='minProcessingTime != null'> AND processing_time &gt;= #{minProcessingTime} </if>" +
            "<if test='maxProcessingTime != null'> AND processing_time &lt;= #{maxProcessingTime} </if>" +
            "<if test='apiResponseCode != null'> AND api_response_code = #{apiResponseCode} </if>" +
            "<if test='resultStatus != null'> AND JSON_EXTRACT(result_content, '$.status') = #{resultStatus} </if>" +
            "<if test='startTime != null'> AND created_time &gt;= #{startTime} </if>" +
            "<if test='endTime != null'> AND created_time &lt;= #{endTime} </if>" +
            "ORDER BY created_time DESC" +
            "</script>")
    IPage<AnalysisResult> selectByConditions(
            Page<AnalysisResult> page,
            @Param("documentId") Long documentId,
            @Param("agentType") AnalysisResult.AgentType agentType,
            @Param("minProcessingTime") Integer minProcessingTime,
            @Param("maxProcessingTime") Integer maxProcessingTime,
            @Param("apiResponseCode") Integer apiResponseCode,
            @Param("resultStatus") String resultStatus,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 调试方法：查询原始JSON数据
     */
    @Select("SELECT id, document_id, agent_type, " +
            "result_content as resultContentRaw, " +
            "CAST(result_content AS CHAR) as resultContentString, " +
            "JSON_VALID(result_content) as isValidJson, " +
            "JSON_TYPE(result_content) as jsonType, " +
            "created_time " +
            "FROM analysis_results " +
            "WHERE document_id = #{documentId} " +
            "ORDER BY created_time DESC")
    List<Map<String, Object>> selectRawJsonData(@Param("documentId") Long documentId);

    /**
     * 调试方法：简单查询不使用类型处理器
     */
    @Select("SELECT id, document_id, agent_type, result_content, created_time " +
            "FROM analysis_results " +
            "WHERE document_id = #{documentId} " +
            "ORDER BY created_time DESC")
    List<Map<String, Object>> selectSimpleData(@Param("documentId") Long documentId);
}
