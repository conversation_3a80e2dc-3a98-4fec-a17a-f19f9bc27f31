package com.docanalysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.docanalysis.entity.Document;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Mapper
public interface DocumentMapper extends BaseMapper<Document> {

    /**
     * 根据状态分页查询文档
     */
    @Select("SELECT * FROM documents WHERE status = #{status} ORDER BY upload_time DESC")
    IPage<Document> selectPageByStatus(Page<Document> page, @Param("status") Document.DocumentStatus status);

    /**
     * 根据状态和时间范围查询文档
     */
    @Select("SELECT * FROM documents WHERE status = #{status} " +
            "AND upload_time BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY upload_time DESC")
    List<Document> selectByStatusAndTimeRange(
            @Param("status") Document.DocumentStatus status,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 根据文件名模糊查询
     */
    @Select("SELECT * FROM documents WHERE original_name LIKE CONCAT('%', #{filename}, '%') " +
            "ORDER BY upload_time DESC")
    List<Document> selectByFilenameContaining(@Param("filename") String filename);

    /**
     * 查询指定大小范围的文档
     */
    @Select("SELECT * FROM documents WHERE file_size BETWEEN #{minSize} AND #{maxSize} " +
            "ORDER BY file_size ASC")
    List<Document> selectByFileSizeRange(
            @Param("minSize") Long minSize,
            @Param("maxSize") Long maxSize
    );

    /**
     * 统计各状态的文档数量
     */
    @Select("SELECT status, COUNT(*) as count FROM documents GROUP BY status")
    List<Map<String, Object>> countByStatus();

    /**
     * 查询最近上传的文档
     */
    @Select("SELECT * FROM documents ORDER BY upload_time DESC LIMIT #{limit}")
    List<Document> selectRecentDocuments(@Param("limit") Integer limit);

    /**
     * 查询处理失败的文档
     */
    @Select("SELECT * FROM documents WHERE status = 'FAILED' ORDER BY upload_time DESC")
    List<Document> selectFailedDocuments();

    /**
     * 查询处理中的文档
     */
    @Select("SELECT * FROM documents WHERE status = 'PROCESSING' ORDER BY upload_time ASC")
    List<Document> selectProcessingDocuments();

    /**
     * 查询已完成的文档
     */
    @Select("SELECT * FROM documents WHERE status = 'COMPLETED' ORDER BY upload_time DESC")
    List<Document> selectCompletedDocuments();

    /**
     * 根据文件路径查询文档
     */
    @Select("SELECT * FROM documents WHERE file_path = #{filePath}")
    Document selectByFilePath(@Param("filePath") String filePath);

    /**
     * 查询指定时间段内的文档统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_count, " +
            "COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_count, " +
            "COUNT(CASE WHEN status = 'PROCESSING' THEN 1 END) as processing_count, " +
            "AVG(file_size) as avg_file_size, " +
            "SUM(file_size) as total_file_size " +
            "FROM documents " +
            "WHERE upload_time BETWEEN #{startTime} AND #{endTime}")
    Map<String, Object> getStatisticsByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 查询大文件（超过指定大小）
     */
    @Select("SELECT * FROM documents WHERE file_size > #{sizeThreshold} ORDER BY file_size DESC")
    List<Document> selectLargeFiles(@Param("sizeThreshold") Long sizeThreshold);

    /**
     * 查询过期文档（超过指定天数）
     */
    @Select("SELECT * FROM documents WHERE upload_time < #{expireTime} AND status = 'COMPLETED'")
    List<Document> selectExpiredDocuments(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 批量更新文档状态
     */
    @Update("UPDATE documents SET status = #{newStatus}, updated_time = NOW() " +
            "WHERE id IN (${ids}) AND status = #{oldStatus}")
    int batchUpdateStatus(
            @Param("ids") String ids,
            @Param("oldStatus") Document.DocumentStatus oldStatus,
            @Param("newStatus") Document.DocumentStatus newStatus
    );

    /**
     * 查询文档处理成功率
     */
    @Select("SELECT " +
            "COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) * 100.0 / " +
            "NULLIF(COUNT(CASE WHEN status IN ('COMPLETED', 'FAILED') THEN 1 END), 0) as success_rate " +
            "FROM documents " +
            "WHERE upload_time BETWEEN #{startTime} AND #{endTime}")
    Double getSuccessRateByTimeRange(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );

    /**
     * 复合条件查询文档
     */
    @Select("<script>" +
            "SELECT * FROM documents WHERE 1=1 " +
            "<if test='status != null'> AND status = #{status} </if>" +
            "<if test='filename != null and filename != &quot;&quot;'> AND original_name LIKE CONCAT('%', #{filename}, '%') </if>" +
            "<if test='minSize != null'> AND file_size &gt;= #{minSize} </if>" +
            "<if test='maxSize != null'> AND file_size &lt;= #{maxSize} </if>" +
            "<if test='startTime != null'> AND upload_time &gt;= #{startTime} </if>" +
            "<if test='endTime != null'> AND upload_time &lt;= #{endTime} </if>" +
            "ORDER BY upload_time DESC" +
            "</script>")
    IPage<Document> selectByConditions(
            Page<Document> page,
            @Param("status") Document.DocumentStatus status,
            @Param("filename") String filename,
            @Param("minSize") Long minSize,
            @Param("maxSize") Long maxSize,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime
    );
}
