package com.docanalysis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "analysis_results", autoResultMap = true)
public class AnalysisResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分析结果ID，主键自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的文档ID
     */
    @TableField("document_id")
    private Long documentId;

    /**
     * 智能体类型
     */
    @TableField("agent_type")
    private AgentType agentType;

    /**
     * 分析结果内容（JSON格式）
     */
    @TableField(value = "result_content", typeHandler = JacksonTypeHandler.class)
    @JsonIgnoreProperties(ignoreUnknown = true)
    private Map<String, Object> resultContent;

    /**
     * 处理耗时（毫秒）
     */
    @TableField("processing_time")
    private Integer processingTime;

    /**
     * API响应状态码
     */
    @TableField("api_response_code")
    private Integer apiResponseCode;

    /**
     * 分析完成时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 智能体类型枚举
     */
    public enum AgentType {
        EXTRACT("EXTRACT", "提取智能体"),
        DETECT("DETECT", "检测智能体");

        private final String code;
        private final String description;

        AgentType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static AgentType fromCode(String code) {
            for (AgentType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown agent type code: " + code);
        }
    }

    /**
     * 获取分析状态
     */
    public String getAnalysisStatus() {
        if (resultContent != null && resultContent.containsKey("status")) {
            return (String) resultContent.get("status");
        }
        return "unknown";
    }

    /**
     * 获取分析数据
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getAnalysisData() {
        if (resultContent != null && resultContent.containsKey("data")) {
            return (Map<String, Object>) resultContent.get("data");
        }
        return null;
    }

    /**
     * 检查分析是否成功
     */
    public boolean isAnalysisSuccessful() {
        return "success".equals(getAnalysisStatus()) && 
               apiResponseCode != null && 
               apiResponseCode == 200;
    }

    /**
     * 获取处理时间的可读格式
     */
    public String getReadableProcessingTime() {
        if (processingTime == null) {
            return "未知";
        }
        
        if (processingTime < 1000) {
            return processingTime + "ms";
        } else if (processingTime < 60000) {
            return String.format("%.1fs", processingTime / 1000.0);
        } else {
            int minutes = processingTime / 60000;
            int seconds = (processingTime % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        }
    }

    /**
     * 获取错误信息（如果有）
     */
    public String getErrorMessage() {
        if (resultContent != null && resultContent.containsKey("error")) {
            return (String) resultContent.get("error");
        }
        return null;
    }
}
