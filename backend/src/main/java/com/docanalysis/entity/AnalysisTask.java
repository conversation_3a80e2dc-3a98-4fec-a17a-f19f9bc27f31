package com.docanalysis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.docanalysis.util.MaxKBApiClient;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = false)
@TableName("analysis_tasks")
public class AnalysisTask {

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("等待中"),
        PROCESSING("处理中"),
        COMPLETED("已完成"),
        FAILED("失败");

        private final String description;

        TaskStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联文档ID
     */
    @TableField("document_id")
    private Long documentId;

    /**
     * 智能体类型
     */
    @TableField("agent_type")
    private MaxKBApiClient.AgentType agentType;

    /**
     * 任务状态
     */
    @TableField("task_status")
    private TaskStatus taskStatus;

    /**
     * MaxKB会话ID
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * MaxKB文件ID
     */
    @TableField("file_id")
    private String fileId;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 开始处理时间
     */
    @TableField("started_time")
    private LocalDateTime startedTime;

    /**
     * 完成时间
     */
    @TableField("completed_time")
    private LocalDateTime completedTime;

    /**
     * 进度百分比(0-100)
     */
    @TableField("progress_percentage")
    private Integer progressPercentage;

    /**
     * 状态描述信息
     */
    @TableField("status_message")
    private String statusMessage;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 处理耗时(毫秒)
     */
    @TableField("processing_duration_ms")
    private Long processingDurationMs;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 关联的分析结果ID
     */
    @TableField("result_id")
    private Long resultId;

    /**
     * 获取智能体类型显示名称
     */
    public String getAgentTypeDisplay() {
        return agentType != null ? agentType.getDescription() : "未知类型";
    }

    /**
     * 获取任务状态显示名称
     */
    public String getTaskStatusDisplay() {
        return taskStatus != null ? taskStatus.getDescription() : "未知状态";
    }

    /**
     * 判断任务是否已完成
     */
    public boolean isCompleted() {
        return TaskStatus.COMPLETED.equals(taskStatus);
    }

    /**
     * 判断任务是否失败
     */
    public boolean isFailed() {
        return TaskStatus.FAILED.equals(taskStatus);
    }

    /**
     * 判断任务是否正在处理
     */
    public boolean isProcessing() {
        return TaskStatus.PROCESSING.equals(taskStatus);
    }

    /**
     * 判断任务是否等待中
     */
    public boolean isPending() {
        return TaskStatus.PENDING.equals(taskStatus);
    }

    /**
     * 计算处理耗时
     */
    public Long calculateDuration() {
        if (startedTime != null && completedTime != null) {
            return java.time.Duration.between(startedTime, completedTime).toMillis();
        }
        return null;
    }

    /**
     * 更新进度
     */
    public void updateProgress(int percentage, String message) {
        this.progressPercentage = Math.max(0, Math.min(100, percentage));
        this.statusMessage = message;
    }

    /**
     * 标记任务开始
     */
    public void markStarted(String message) {
        this.taskStatus = TaskStatus.PROCESSING;
        this.startedTime = LocalDateTime.now();
        this.statusMessage = message;
        this.progressPercentage = 0;
    }

    /**
     * 标记任务完成
     */
    public void markCompleted(String message, Long resultId) {
        this.taskStatus = TaskStatus.COMPLETED;
        this.completedTime = LocalDateTime.now();
        this.statusMessage = message;
        this.progressPercentage = 100;
        this.resultId = resultId;
        
        if (startedTime != null) {
            this.processingDurationMs = calculateDuration();
        }
    }

    /**
     * 标记任务失败
     */
    public void markFailed(String errorMessage) {
        this.taskStatus = TaskStatus.FAILED;
        this.completedTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
        this.statusMessage = "处理失败";
        
        if (startedTime != null) {
            this.processingDurationMs = calculateDuration();
        }
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
    }
}
