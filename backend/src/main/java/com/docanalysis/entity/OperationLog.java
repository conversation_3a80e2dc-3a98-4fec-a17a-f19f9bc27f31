package com.docanalysis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "operation_logs", autoResultMap = true)
public class OperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @TableField("operation_desc")
    private String operationDesc;

    /**
     * 关联文档ID（如果有）
     */
    @TableField("document_id")
    private Long documentId;

    /**
     * 请求参数（JSON格式）
     */
    @TableField(value = "request_params", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> requestParams;

    /**
     * 响应数据（JSON格式）
     */
    @TableField(value = "response_data", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> responseData;

    /**
     * 执行耗时（毫秒）
     */
    @TableField("execution_time")
    private Integer executionTime;

    /**
     * 执行状态
     */
    @TableField("status")
    private OperationStatus status;

    /**
     * 错误信息（如果有）
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 操作时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 操作状态枚举
     */
    public enum OperationStatus {
        SUCCESS("SUCCESS", "成功"),
        FAILED("FAILED", "失败"),
        PARTIAL("PARTIAL", "部分成功");

        private final String code;
        private final String description;

        OperationStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static OperationStatus fromCode(String code) {
            for (OperationStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown operation status code: " + code);
        }
    }

    /**
     * 操作类型常量
     */
    public static class OperationType {
        public static final String DOCUMENT_UPLOAD = "DOCUMENT_UPLOAD";
        public static final String DOCUMENT_PARSE = "DOCUMENT_PARSE";
        public static final String MAXKB_API_CALL = "MAXKB_API_CALL";
        public static final String ANALYSIS_PROCESS = "ANALYSIS_PROCESS";
        public static final String USER_INTERACTION = "USER_INTERACTION";
        public static final String SYSTEM_CONFIG = "SYSTEM_CONFIG";
        public static final String DATA_CLEANUP = "DATA_CLEANUP";
        public static final String ERROR_HANDLE = "ERROR_HANDLE";
    }

    /**
     * 检查操作是否成功
     */
    public boolean isSuccessful() {
        return status == OperationStatus.SUCCESS;
    }

    /**
     * 检查操作是否失败
     */
    public boolean isFailed() {
        return status == OperationStatus.FAILED;
    }

    /**
     * 检查操作是否部分成功
     */
    public boolean isPartialSuccess() {
        return status == OperationStatus.PARTIAL;
    }

    /**
     * 获取执行时间的可读格式
     */
    public String getReadableExecutionTime() {
        if (executionTime == null) {
            return "未知";
        }
        
        if (executionTime < 1000) {
            return executionTime + "ms";
        } else if (executionTime < 60000) {
            return String.format("%.1fs", executionTime / 1000.0);
        } else {
            int minutes = executionTime / 60000;
            int seconds = (executionTime % 60000) / 1000;
            return String.format("%dm %ds", minutes, seconds);
        }
    }

    /**
     * 获取操作类型的显示名称
     */
    public String getOperationTypeDisplayName() {
        switch (operationType) {
            case OperationType.DOCUMENT_UPLOAD:
                return "文档上传";
            case OperationType.DOCUMENT_PARSE:
                return "文档解析";
            case OperationType.MAXKB_API_CALL:
                return "MaxKB API调用";
            case OperationType.ANALYSIS_PROCESS:
                return "分析处理";
            case OperationType.USER_INTERACTION:
                return "用户交互";
            case OperationType.SYSTEM_CONFIG:
                return "系统配置";
            case OperationType.DATA_CLEANUP:
                return "数据清理";
            case OperationType.ERROR_HANDLE:
                return "错误处理";
            default:
                return operationType;
        }
    }

    /**
     * 获取请求参数中的特定值
     */
    public Object getRequestParam(String key) {
        return requestParams != null ? requestParams.get(key) : null;
    }

    /**
     * 获取响应数据中的特定值
     */
    public Object getResponseData(String key) {
        return responseData != null ? responseData.get(key) : null;
    }

    /**
     * 检查是否有错误信息
     */
    public boolean hasError() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }
}
