package com.docanalysis.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("system_config")
public class SystemConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 配置键
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 配置值类型
     */
    @TableField("config_type")
    private ConfigType configType;

    /**
     * 配置描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否启用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 配置值类型枚举
     */
    public enum ConfigType {
        STRING("STRING", "字符串"),
        NUMBER("NUMBER", "数字"),
        BOOLEAN("BOOLEAN", "布尔值"),
        JSON("JSON", "JSON对象");

        private final String code;
        private final String description;

        ConfigType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ConfigType fromCode(String code) {
            for (ConfigType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown config type code: " + code);
        }
    }

    /**
     * 获取字符串值
     */
    public String getStringValue() {
        return configValue;
    }

    /**
     * 获取数字值
     */
    public Long getNumberValue() {
        if (configType == ConfigType.NUMBER && configValue != null) {
            try {
                return Long.parseLong(configValue);
            } catch (NumberFormatException e) {
                throw new IllegalStateException("Config value is not a valid number: " + configValue);
            }
        }
        return null;
    }

    /**
     * 获取整数值
     */
    public Integer getIntegerValue() {
        Long numberValue = getNumberValue();
        return numberValue != null ? numberValue.intValue() : null;
    }

    /**
     * 获取布尔值
     */
    public Boolean getBooleanValue() {
        if (configType == ConfigType.BOOLEAN && configValue != null) {
            return Boolean.parseBoolean(configValue);
        }
        return null;
    }

    /**
     * 获取JSON值
     */
    public String getJsonValue() {
        if (configType == ConfigType.JSON) {
            return configValue;
        }
        return null;
    }

    /**
     * 设置字符串值
     */
    public SystemConfig setStringValue(String value) {
        this.configType = ConfigType.STRING;
        this.configValue = value;
        return this;
    }

    /**
     * 设置数字值
     */
    public SystemConfig setNumberValue(Long value) {
        this.configType = ConfigType.NUMBER;
        this.configValue = value != null ? value.toString() : null;
        return this;
    }

    /**
     * 设置整数值
     */
    public SystemConfig setIntegerValue(Integer value) {
        this.configType = ConfigType.NUMBER;
        this.configValue = value != null ? value.toString() : null;
        return this;
    }

    /**
     * 设置布尔值
     */
    public SystemConfig setBooleanValue(Boolean value) {
        this.configType = ConfigType.BOOLEAN;
        this.configValue = value != null ? value.toString() : null;
        return this;
    }

    /**
     * 设置JSON值
     */
    public SystemConfig setJsonValue(String value) {
        this.configType = ConfigType.JSON;
        this.configValue = value;
        return this;
    }

    /**
     * 检查配置是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(isActive);
    }

    /**
     * 启用配置
     */
    public SystemConfig enable() {
        this.isActive = true;
        return this;
    }

    /**
     * 禁用配置
     */
    public SystemConfig disable() {
        this.isActive = false;
        return this;
    }
}
