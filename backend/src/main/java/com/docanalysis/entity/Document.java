package com.docanalysis.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("documents")
public class Document implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文档ID，主键自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 存储文件名（系统生成的唯一文件名）
     */
    @TableField("filename")
    private String filename;

    /**
     * 原始文件名（用户上传时的文件名）
     */
    @TableField("original_name")
    private String originalName;

    /**
     * 文件存储路径
     */
    @TableField("file_path")
    private String filePath;

    @TableField("pdf_filename")
    private String pdfFilename;

    @TableField("pdf_file_path")
    private String pdfFilePath;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    private LocalDateTime uploadTime;

    /**
     * 文档处理状态
     */
    @TableField("status")
    private DocumentStatus status;

    /**
     * 记录创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 记录更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 文档处理状态枚举
     */
    public enum DocumentStatus {
        UPLOADED("UPLOADED", "已上传"),
        PROCESSING("PROCESSING", "处理中"),
        COMPLETED("COMPLETED", "已完成"),
        FAILED("FAILED", "处理失败");

        private final String code;
        private final String description;

        DocumentStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static DocumentStatus fromCode(String code) {
            for (DocumentStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown status code: " + code);
        }
    }

    /**
     * 获取文件大小的可读格式
     */
    public String getReadableFileSize() {
        if (fileSize == null) {
            return "0 B";
        }
        
        long size = fileSize;
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", (double) size, units[unitIndex]);
    }

    /**
     * 检查文档是否处理完成
     */
    public boolean isProcessed() {
        return status == DocumentStatus.COMPLETED || status == DocumentStatus.FAILED;
    }

    /**
     * 检查文档是否处理成功
     */
    public boolean isProcessedSuccessfully() {
        return status == DocumentStatus.COMPLETED;
    }
}
