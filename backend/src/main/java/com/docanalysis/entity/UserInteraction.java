package com.docanalysis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "user_interactions", autoResultMap = true)
public class UserInteraction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交互记录ID，主键自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的文档ID
     */
    @TableField("document_id")
    private Long documentId;

    /**
     * 交互类型
     */
    @TableField("interaction_type")
    private String interactionType;

    /**
     * 交互内容描述
     */
    @TableField("content")
    private String content;

    /**
     * 位置信息（JSON格式）
     */
    @TableField(value = "position_info", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> positionInfo;

    /**
     * 用户会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 用户代理信息
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 用户IP地址
     */
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 交互发生时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 交互类型常量
     */
    public static class InteractionType {
        public static final String HIGHLIGHT = "highlight";
        public static final String CLICK = "click";
        public static final String SEARCH = "search";
        public static final String SCROLL = "scroll";
        public static final String ZOOM = "zoom";
        public static final String SELECT = "select";
        public static final String COPY = "copy";
        public static final String DOWNLOAD = "download";
    }

    /**
     * 获取位置信息中的起始位置
     */
    public Integer getStartPosition() {
        if (positionInfo != null && positionInfo.containsKey("start")) {
            Object start = positionInfo.get("start");
            if (start instanceof Number) {
                return ((Number) start).intValue();
            }
        }
        return null;
    }

    /**
     * 获取位置信息中的结束位置
     */
    public Integer getEndPosition() {
        if (positionInfo != null && positionInfo.containsKey("end")) {
            Object end = positionInfo.get("end");
            if (end instanceof Number) {
                return ((Number) end).intValue();
            }
        }
        return null;
    }

    /**
     * 获取位置信息中的页码
     */
    public Integer getPageNumber() {
        if (positionInfo != null && positionInfo.containsKey("page")) {
            Object page = positionInfo.get("page");
            if (page instanceof Number) {
                return ((Number) page).intValue();
            }
        }
        return null;
    }

    /**
     * 获取坐标信息
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> getCoordinates() {
        if (positionInfo != null && positionInfo.containsKey("coordinates")) {
            return (Map<String, Object>) positionInfo.get("coordinates");
        }
        return null;
    }

    /**
     * 获取X坐标
     */
    public Integer getXCoordinate() {
        Map<String, Object> coordinates = getCoordinates();
        if (coordinates != null && coordinates.containsKey("x")) {
            Object x = coordinates.get("x");
            if (x instanceof Number) {
                return ((Number) x).intValue();
            }
        }
        return null;
    }

    /**
     * 获取Y坐标
     */
    public Integer getYCoordinate() {
        Map<String, Object> coordinates = getCoordinates();
        if (coordinates != null && coordinates.containsKey("y")) {
            Object y = coordinates.get("y");
            if (y instanceof Number) {
                return ((Number) y).intValue();
            }
        }
        return null;
    }

    /**
     * 检查是否为高亮交互
     */
    public boolean isHighlightInteraction() {
        return InteractionType.HIGHLIGHT.equals(interactionType);
    }

    /**
     * 检查是否为点击交互
     */
    public boolean isClickInteraction() {
        return InteractionType.CLICK.equals(interactionType);
    }

    /**
     * 检查是否有位置信息
     */
    public boolean hasPositionInfo() {
        return positionInfo != null && !positionInfo.isEmpty();
    }

    /**
     * 获取交互类型的显示名称
     */
    public String getInteractionTypeDisplayName() {
        switch (interactionType) {
            case InteractionType.HIGHLIGHT:
                return "高亮";
            case InteractionType.CLICK:
                return "点击";
            case InteractionType.SEARCH:
                return "搜索";
            case InteractionType.SCROLL:
                return "滚动";
            case InteractionType.ZOOM:
                return "缩放";
            case InteractionType.SELECT:
                return "选择";
            case InteractionType.COPY:
                return "复制";
            case InteractionType.DOWNLOAD:
                return "下载";
            default:
                return interactionType;
        }
    }
}
