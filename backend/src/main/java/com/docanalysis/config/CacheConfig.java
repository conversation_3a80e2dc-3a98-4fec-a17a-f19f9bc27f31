package com.docanalysis.config;

import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 配置缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();

        // 配置缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList(
            "documentContent",    // 文档内容缓存
            "documentParse",      // 文档解析结果缓存
            "documentMetadata",   // 文档元数据缓存
            "maxkbResults",       // MaxKB分析结果缓存
            "analysisResults"     // 分析结果缓存
        ));

        // 允许空值缓存
        cacheManager.setAllowNullValues(false);

        log.info("缓存管理器初始化完成，配置的缓存: {}", cacheManager.getCacheNames());

        return cacheManager;
    }

    /**
     * 缓存工具方法 - 清除指定文档的所有相关缓存
     */
    public void clearDocumentCaches(Long documentId) {
        if (documentId == null) return;

        try {
            CacheManager manager = cacheManager();

            // 清除文档内容缓存
            Cache documentContentCache = manager.getCache("documentContent");
            if (documentContentCache != null) {
                documentContentCache.evict(documentId);
                log.debug("清除文档内容缓存: documentId={}", documentId);
            }

            // 清除分析结果缓存
            Cache analysisResultsCache = manager.getCache("analysisResults");
            if (analysisResultsCache != null) {
                analysisResultsCache.evict(documentId);
                analysisResultsCache.evict(documentId + "_EXTRACT");
                analysisResultsCache.evict(documentId + "_DETECT");
                log.debug("清除分析结果缓存: documentId={}", documentId);
            }

            // 清除MaxKB结果缓存
            Cache maxkbResultsCache = manager.getCache("maxkbResults");
            if (maxkbResultsCache != null) {
                maxkbResultsCache.evict(documentId);
                log.debug("清除MaxKB结果缓存: documentId={}", documentId);
            }

            log.info("文档相关缓存清除完成: documentId={}", documentId);

        } catch (Exception e) {
            log.error("清除文档缓存失败: documentId={}", documentId, e);
        }
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCaches() {
        try {
            CacheManager manager = cacheManager();
            manager.getCacheNames().forEach(cacheName -> {
                Cache cache = manager.getCache(cacheName);
                if (cache != null) {
                    cache.clear();
                    log.debug("清除缓存: {}", cacheName);
                }
            });
            log.info("所有缓存清除完成");
        } catch (Exception e) {
            log.error("清除所有缓存失败", e);
        }
    }
}
