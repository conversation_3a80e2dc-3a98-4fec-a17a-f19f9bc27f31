package com.docanalysis.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;


@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "app.file")
public class FileStorageConfig {

    /**
     * 上传目录路径
     * 支持以下格式：
     * - 绝对路径: /home/<USER>/uploads 或 C:\\uploads
     * - 相对路径: ./uploads
     * - 用户目录: ${user.home}/uploads
     * - 临时目录: ${java.io.tmpdir}/uploads
     */
    private String uploadDir = "${user.home}/document-analysis/uploads";

    /**
     * 最大文件大小（字节）
     */
    private long maxSize = 52428800L; // 50MB

    /**
     * 允许的文件类型
     */
    private String allowedTypes = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";

    /**
     * 是否启用文件去重（基于MD5）
     */
    private boolean enableDeduplication = true;

    /**
     * 是否保留原始文件名
     */
    private boolean keepOriginalName = false;

    /**
     * 文件存储策略：date（按日期分目录）、flat（平铺）、hash（按哈希分目录）
     */
    private String storageStrategy = "date";

    /**
     * 解析后的绝对路径
     */
    private String resolvedUploadDir;

    @PostConstruct
    public void init() {
        try {
            resolvedUploadDir = resolveUploadDir();
            Path uploadPath = Paths.get(resolvedUploadDir);
            
            // 创建上传目录
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                log.info("创建文件存储目录: {}", uploadPath.toAbsolutePath());
            }
            
            // 验证目录权限
            if (!Files.isWritable(uploadPath)) {
                throw new RuntimeException("文件存储目录不可写: " + uploadPath.toAbsolutePath());
            }
            
            log.info("文件存储配置初始化完成:");
            log.info("  - 存储目录: {}", uploadPath.toAbsolutePath());
            log.info("  - 最大文件大小: {} MB", maxSize / 1024 / 1024);
            log.info("  - 存储策略: {}", storageStrategy);
            log.info("  - 启用去重: {}", enableDeduplication);
            
        } catch (IOException e) {
            log.error("文件存储配置初始化失败", e);
            throw new RuntimeException("文件存储配置初始化失败", e);
        }
    }

    /**
     * 解析上传目录路径
     */
    private String resolveUploadDir() {
        String resolved = uploadDir;
        
        // 替换系统属性
        resolved = resolved.replace("${user.home}", System.getProperty("user.home"));
        resolved = resolved.replace("${java.io.tmpdir}", System.getProperty("java.io.tmpdir"));
        resolved = resolved.replace("${user.dir}", System.getProperty("user.dir"));
        
        // 处理相对路径
        if (resolved.startsWith("./")) {
            String currentDir = System.getProperty("user.dir");
            resolved = Paths.get(currentDir, resolved.substring(2)).toString();
        }
        
        // 标准化路径
        Path path = Paths.get(resolved);
        String absolutePath = path.toAbsolutePath().normalize().toString();
        
        log.debug("上传目录路径解析: {} -> {}", uploadDir, absolutePath);
        return absolutePath;
    }

    /**
     * 获取操作系统类型
     */
    public String getOsType() {
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("win")) {
            return "windows";
        } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("aix")) {
            return "linux";
        } else if (osName.contains("mac")) {
            return "macos";
        } else {
            return "unknown";
        }
    }

    /**
     * 检查是否为Windows系统
     */
    public boolean isWindows() {
        return "windows".equals(getOsType());
    }

    /**
     * 检查是否为Linux系统
     */
    public boolean isLinux() {
        return "linux".equals(getOsType());
    }

    /**
     * 获取默认的上传目录（根据操作系统）
     */
    public String getDefaultUploadDir() {
        String userHome = System.getProperty("user.home");
        if (isWindows()) {
            return Paths.get(userHome, "Documents", "DocumentAnalysis", "uploads").toString();
        } else {
            return Paths.get(userHome, "document-analysis", "uploads").toString();
        }
    }
}
