package com.docanalysis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Data
@Component
@ConfigurationProperties(prefix = "docanalysis.header-footer")
public class HeaderFooterConfig {

    /**
     * 是否启用页眉页脚功能
     */
    private boolean enabled = true;

    /**
     * 是否启用页码重建功能
     */
    private boolean pageBreakEnabled = true;

    /**
     * 是否启用缓存
     */
    private boolean cacheEnabled = true;

    /**
     * 缓存过期时间（分钟）
     */
    private int cacheExpirationMinutes = 60;

    /**
     * 页码计算超时时间（秒）
     */
    private int pageBreakTimeoutSeconds = 30;

    /**
     * 最大文档大小限制（MB），超过此大小不进行页码计算
     */
    private int maxDocumentSizeMB = 50;

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitoringEnabled = true;

    /**
     * 是否在解析失败时记录详细错误
     */
    private boolean detailedErrorLogging = false;

    /**
     * 页码计算的最大页数限制
     */
    private int maxPagesLimit = 1000;

    /**
     * 检查页眉页脚功能是否完全启用
     */
    public boolean isFullyEnabled() {
        return enabled && pageBreakEnabled;
    }

    /**
     * 检查是否应该进行页码计算
     */
    public boolean shouldCalculatePageBreaks() {
        return enabled && pageBreakEnabled;
    }

    /**
     * 检查是否应该使用缓存
     */
    public boolean shouldUseCache() {
        return enabled && cacheEnabled;
    }

    /**
     * 获取页码计算超时时间（毫秒）
     */
    public long getPageBreakTimeoutMillis() {
        return pageBreakTimeoutSeconds * 1000L;
    }

    /**
     * 获取最大文档大小（字节）
     */
    public long getMaxDocumentSizeBytes() {
        return maxDocumentSizeMB * 1024L * 1024L;
    }

    /**
     * 检查文档大小是否超过限制
     */
    public boolean isDocumentSizeExceeded(long fileSizeBytes) {
        return fileSizeBytes > getMaxDocumentSizeBytes();
    }

    /**
     * 检查页数是否超过限制
     */
    public boolean isPagesLimitExceeded(int pageCount) {
        return pageCount > maxPagesLimit;
    }

    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format("HeaderFooter[enabled=%s, pageBreak=%s, cache=%s, timeout=%ds, maxSize=%dMB]",
                enabled, pageBreakEnabled, cacheEnabled, pageBreakTimeoutSeconds, maxDocumentSizeMB);
    }
}
