package com.docanalysis.util;

import com.docanalysis.dto.HeaderFooterInfo;
import com.docanalysis.dto.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.stereotype.Component;

import java.util.*;


@Slf4j
@Component
public class PageBreakCalculator {

    // 默认页面设置常量
    private static final int DEFAULT_PAGE_WIDTH = 595;  // A4页面宽度（点）
    private static final int DEFAULT_PAGE_HEIGHT = 842; // A4页面高度（点）
    private static final int DEFAULT_MARGIN = 72;       // 默认页边距（点）
    private static final int DEFAULT_LINE_HEIGHT = 14;  // 默认行高（点）
    private static final int DEFAULT_CHARS_PER_LINE = 50; // 每行字符数（中文）
    private static final int DEFAULT_LINES_PER_PAGE = 40; // 每页行数
    
    // 中文文档特殊处理常量
    private static final double CHINESE_CHAR_FACTOR = 1.5; // 中文字符占用空间系数
    private static final int TABLE_OVERHEAD_LINES = 2;     // 表格额外占用行数
    private static final int IMAGE_OVERHEAD_LINES = 3;     // 图片额外占用行数

    /**
     * 页面配置类
     */
    @lombok.Data
    public static class PageSettings {
        private int pageWidth = DEFAULT_PAGE_WIDTH;
        private int pageHeight = DEFAULT_PAGE_HEIGHT;
        private int marginTop = DEFAULT_MARGIN;
        private int marginBottom = DEFAULT_MARGIN;
        private int marginLeft = DEFAULT_MARGIN;
        private int marginRight = DEFAULT_MARGIN;
        private int lineHeight = DEFAULT_LINE_HEIGHT;
        private int charsPerLine = DEFAULT_CHARS_PER_LINE;
        private int linesPerPage = DEFAULT_LINES_PER_PAGE;
        
        /**
         * 计算有效页面高度
         */
        public int getEffectivePageHeight() {
            return pageHeight - marginTop - marginBottom;
        }
        
        /**
         * 计算有效页面宽度
         */
        public int getEffectivePageWidth() {
            return pageWidth - marginLeft - marginRight;
        }
        
        /**
         * 计算每页最大行数
         */
        public int getMaxLinesPerPage() {
            return getEffectivePageHeight() / lineHeight;
        }
    }

    /**
     * 计算文档的页码分割点
     */
    public List<PageInfo> calculatePageBreaks(XWPFDocument document, HeaderFooterInfo headerFooterInfo) {
        List<PageInfo> pages = new ArrayList<>();
        
        try {
            log.debug("开始计算页码分割点");
            
            // 1. 分析文档页面设置
            PageSettings pageSettings = analyzePageSettings(document);
            
            // 2. 分析文档内容
            List<ContentBlock> contentBlocks = analyzeDocumentContent(document);
            
            // 3. 执行页码重建算法
            pages = performPageBreakCalculation(contentBlocks, pageSettings, headerFooterInfo);
            
            log.debug("页码计算完成，共{}页", pages.size());
            
        } catch (Exception e) {
            log.warn("页码计算过程中出现错误: {}", e.getMessage(), e);
            // 返回默认的单页信息
            pages.add(createDefaultPageInfo());
        }
        
        return pages;
    }

    /**
     * 分析文档页面设置
     */
    private PageSettings analyzePageSettings(XWPFDocument document) {
        PageSettings settings = new PageSettings();
        
        try {
            // 尝试从文档中获取页面设置
            // 注意：POI 5.x中页面设置的API可能有所不同
            // 这里使用默认设置，可以根据需要扩展
            
            log.debug("使用默认页面设置: {}x{}", settings.getPageWidth(), settings.getPageHeight());
            
        } catch (Exception e) {
            log.debug("无法获取文档页面设置，使用默认值: {}", e.getMessage());
        }
        
        return settings;
    }

    /**
     * 内容块类
     */
    @lombok.Data
    private static class ContentBlock {
        private String text;
        private int position;
        private int length;
        private ContentType type;
        private boolean hasPageBreak;
        private int estimatedLines;
        private double confidence;
        
        public enum ContentType {
            PARAGRAPH, TABLE, IMAGE, PAGE_BREAK
        }
    }

    /**
     * 分析文档内容
     */
    private List<ContentBlock> analyzeDocumentContent(XWPFDocument document) {
        List<ContentBlock> blocks = new ArrayList<>();
        int currentPosition = 0;
        
        try {
            // 分析段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                ContentBlock block = analyzeParagraph(paragraph, currentPosition);
                if (block != null) {
                    blocks.add(block);
                    currentPosition += block.getLength();
                }
            }
            
            // 分析表格
            for (XWPFTable table : document.getTables()) {
                ContentBlock block = analyzeTable(table, currentPosition);
                if (block != null) {
                    blocks.add(block);
                    currentPosition += block.getLength();
                }
            }
            
            // 按位置排序
            blocks.sort(Comparator.comparingInt(ContentBlock::getPosition));
            
        } catch (Exception e) {
            log.warn("分析文档内容时出现错误: {}", e.getMessage());
        }
        
        return blocks;
    }

    /**
     * 分析段落
     */
    private ContentBlock analyzeParagraph(XWPFParagraph paragraph, int position) {
        ContentBlock block = new ContentBlock();
        block.setType(ContentBlock.ContentType.PARAGRAPH);
        block.setPosition(position);
        
        String text = paragraph.getText();
        if (text == null) text = "";
        
        block.setText(text);
        block.setLength(text.length());
        
        // 检查是否有分页符
        block.setHasPageBreak(hasPageBreak(paragraph));
        
        // 估算行数
        int estimatedLines = estimateParagraphLines(text);
        block.setEstimatedLines(estimatedLines);
        
        // 设置置信度
        block.setConfidence(0.8); // 段落分析置信度较高
        
        return block;
    }

    /**
     * 分析表格
     */
    private ContentBlock analyzeTable(XWPFTable table, int position) {
        ContentBlock block = new ContentBlock();
        block.setType(ContentBlock.ContentType.TABLE);
        block.setPosition(position);
        
        // 计算表格文本长度
        StringBuilder tableText = new StringBuilder();
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                tableText.append(cell.getText()).append(" ");
            }
        }
        
        String text = tableText.toString();
        block.setText(text);
        block.setLength(text.length());
        
        // 估算表格占用行数
        int rows = table.getRows().size();
        int estimatedLines = Math.max(rows + TABLE_OVERHEAD_LINES, 3);
        block.setEstimatedLines(estimatedLines);
        
        // 表格分析置信度中等
        block.setConfidence(0.6);
        
        return block;
    }

    /**
     * 检查段落是否包含分页符
     */
    private boolean hasPageBreak(XWPFParagraph paragraph) {
        try {
            // 检查段落的运行中是否有分页符
            for (XWPFRun run : paragraph.getRuns()) {
                // 在POI 5.x中检查分页符的方法
                if (run.getText(0) != null && run.getText(0).contains("\f")) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.debug("检查分页符时出现错误: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 估算段落行数
     */
    private int estimateParagraphLines(String text) {
        if (text == null || text.trim().isEmpty()) {
            return 1;
        }
        
        // 计算中文字符数
        int chineseChars = 0;
        int englishChars = 0;
        
        for (char c : text.toCharArray()) {
            if (isChinese(c)) {
                chineseChars++;
            } else {
                englishChars++;
            }
        }
        
        // 中文字符占用更多空间
        double effectiveLength = chineseChars * CHINESE_CHAR_FACTOR + englishChars;
        
        // 计算行数
        int lines = (int) Math.ceil(effectiveLength / DEFAULT_CHARS_PER_LINE);
        return Math.max(lines, 1);
    }

    /**
     * 判断是否为中文字符
     */
    private boolean isChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FFF;
    }

    /**
     * 执行页码重建计算
     */
    private List<PageInfo> performPageBreakCalculation(List<ContentBlock> contentBlocks, 
                                                      PageSettings pageSettings, 
                                                      HeaderFooterInfo headerFooterInfo) {
        List<PageInfo> pages = new ArrayList<>();
        
        int currentPage = 1;
        int currentLines = 0;
        int pageStartPosition = 0;
        int currentPosition = 0;
        boolean hasTable = false;
        boolean hasImage = false;
        boolean hasPageBreak = false;
        
        for (ContentBlock block : contentBlocks) {
            // 检查是否需要分页
            boolean needPageBreak = false;
            
            // 1. 显式分页符
            if (block.isHasPageBreak()) {
                needPageBreak = true;
                hasPageBreak = true;
            }
            
            // 2. 内容超出页面容量
            if (currentLines + block.getEstimatedLines() > pageSettings.getMaxLinesPerPage()) {
                needPageBreak = true;
            }
            
            // 如果需要分页，创建当前页面信息
            if (needPageBreak && currentPage > 0) {
                PageInfo pageInfo = createPageInfo(currentPage, pageStartPosition, currentPosition,
                                                 headerFooterInfo, hasTable, hasImage, hasPageBreak);
                pages.add(pageInfo);
                
                // 重置下一页的状态
                currentPage++;
                currentLines = 0;
                pageStartPosition = currentPosition;
                hasTable = false;
                hasImage = false;
                hasPageBreak = false;
            }
            
            // 更新当前页状态
            currentLines += block.getEstimatedLines();
            currentPosition += block.getLength();
            
            if (block.getType() == ContentBlock.ContentType.TABLE) {
                hasTable = true;
            }
            
            if (block.isHasPageBreak()) {
                hasPageBreak = true;
            }
        }
        
        // 添加最后一页
        if (currentPosition > pageStartPosition) {
            PageInfo lastPage = createPageInfo(currentPage, pageStartPosition, currentPosition,
                                             headerFooterInfo, hasTable, hasImage, hasPageBreak);
            pages.add(lastPage);
        }
        
        // 如果没有页面，创建默认页面
        if (pages.isEmpty()) {
            pages.add(createDefaultPageInfo());
        }
        
        return pages;
    }

    /**
     * 创建页面信息
     */
    private PageInfo createPageInfo(int pageNumber, int startPosition, int endPosition,
                                   HeaderFooterInfo headerFooterInfo, boolean hasTable, 
                                   boolean hasImage, boolean hasPageBreak) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNumber(pageNumber);
        pageInfo.setStartPosition(startPosition);
        pageInfo.setEndPosition(endPosition);
        pageInfo.setContentLength(endPosition - startPosition);
        pageInfo.setHasTable(hasTable);
        pageInfo.setHasImage(hasImage);
        pageInfo.setHasPageBreak(hasPageBreak);
        
        // 设置页眉页脚
        if (headerFooterInfo != null) {
            pageInfo.setHeaderText(headerFooterInfo.getFullHeaderText());
            pageInfo.setFooterText(headerFooterInfo.getFullFooterText());
        }
        
        // 估算行数
        int estimatedLines = Math.max(1, pageInfo.getContentLength() / DEFAULT_CHARS_PER_LINE);
        pageInfo.setEstimatedLines(estimatedLines);
        
        // 计算置信度
        double confidence = calculatePageConfidence(pageInfo, hasPageBreak);
        pageInfo.setConfidence(confidence);
        
        return pageInfo;
    }

    /**
     * 计算页面置信度
     */
    private double calculatePageConfidence(PageInfo pageInfo, boolean hasExplicitBreak) {
        double confidence = 0.7; // 基础置信度
        
        // 有显式分页符的页面置信度更高
        if (hasExplicitBreak) {
            confidence += 0.2;
        }
        
        // 内容长度合理的页面置信度更高
        int contentLength = pageInfo.getContentLength();
        if (contentLength > 500 && contentLength < 3000) {
            confidence += 0.1;
        }
        
        return Math.min(confidence, 1.0);
    }

    /**
     * 创建默认页面信息
     */
    private PageInfo createDefaultPageInfo() {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNumber(1);
        pageInfo.setStartPosition(0);
        pageInfo.setEndPosition(1000); // 默认长度
        pageInfo.setContentLength(1000);
        pageInfo.setHasTable(false);
        pageInfo.setHasImage(false);
        pageInfo.setHasPageBreak(false);
        pageInfo.setEstimatedLines(DEFAULT_LINES_PER_PAGE);
        pageInfo.setConfidence(0.5); // 默认页面置信度较低
        
        return pageInfo;
    }
}
