package com.docanalysis.util;

import com.docanalysis.dto.HeaderFooterInfo;
import com.docanalysis.dto.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;


@Slf4j
@Component
public class POIDocumentParser {

    @Autowired
    private PageBreakCalculator pageBreakCalculator;

    /**
     * 文档解析结果
     */
    public static class DocumentParseResult {
        private String htmlContent;
        private List<DocumentElement> elements;
        private Map<String, Object> metadata;
        private List<PositionMapping> positionMappings;

        // Getters and Setters
        public String getHtmlContent() { return htmlContent; }
        public void setHtmlContent(String htmlContent) { this.htmlContent = htmlContent; }
        
        public List<DocumentElement> getElements() { return elements; }
        public void setElements(List<DocumentElement> elements) { this.elements = elements; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
        
        public List<PositionMapping> getPositionMappings() { return positionMappings; }
        public void setPositionMappings(List<PositionMapping> positionMappings) { this.positionMappings = positionMappings; }
    }

    /**
     * 文档元素
     */
    public static class DocumentElement {
        private String type; // paragraph, table, list, image
        private String content;
        private Map<String, Object> style;
        private int startPosition;
        private int endPosition;
        private String elementId;

        // Constructors
        public DocumentElement() {}
        
        public DocumentElement(String type, String content, int startPosition, int endPosition) {
            this.type = type;
            this.content = content;
            this.startPosition = startPosition;
            this.endPosition = endPosition;
            this.elementId = UUID.randomUUID().toString();
            this.style = new HashMap<>();
        }

        // Getters and Setters
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public Map<String, Object> getStyle() { return style; }
        public void setStyle(Map<String, Object> style) { this.style = style; }
        
        public int getStartPosition() { return startPosition; }
        public void setStartPosition(int startPosition) { this.startPosition = startPosition; }
        
        public int getEndPosition() { return endPosition; }
        public void setEndPosition(int endPosition) { this.endPosition = endPosition; }
        
        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
    }

    /**
     * 位置映射
     */
    public static class PositionMapping {
        private String elementId;
        private int originalStart;
        private int originalEnd;
        private int htmlStart;
        private int htmlEnd;
        private String htmlElementId;

        // Constructors
        public PositionMapping() {}
        
        public PositionMapping(String elementId, int originalStart, int originalEnd, int htmlStart, int htmlEnd) {
            this.elementId = elementId;
            this.originalStart = originalStart;
            this.originalEnd = originalEnd;
            this.htmlStart = htmlStart;
            this.htmlEnd = htmlEnd;
            this.htmlElementId = "html_" + elementId;
        }

        // Getters and Setters
        public String getElementId() { return elementId; }
        public void setElementId(String elementId) { this.elementId = elementId; }
        
        public int getOriginalStart() { return originalStart; }
        public void setOriginalStart(int originalStart) { this.originalStart = originalStart; }
        
        public int getOriginalEnd() { return originalEnd; }
        public void setOriginalEnd(int originalEnd) { this.originalEnd = originalEnd; }
        
        public int getHtmlStart() { return htmlStart; }
        public void setHtmlStart(int htmlStart) { this.htmlStart = htmlStart; }
        
        public int getHtmlEnd() { return htmlEnd; }
        public void setHtmlEnd(int htmlEnd) { this.htmlEnd = htmlEnd; }
        
        public String getHtmlElementId() { return htmlElementId; }
        public void setHtmlElementId(String htmlElementId) { this.htmlElementId = htmlElementId; }
    }

    /**
     * 解析DOCX文档
     */
    public DocumentParseResult parseDocument(String filePath) throws IOException {
        log.info("开始解析文档: {}", filePath);
        
        try (InputStream inputStream = new FileInputStream(filePath);
             XWPFDocument document = new XWPFDocument(inputStream)) {
            
            DocumentParseResult result = new DocumentParseResult();
            List<DocumentElement> elements = new ArrayList<>();
            List<PositionMapping> positionMappings = new ArrayList<>();
            
            int currentPosition = 0;
            
            // 解析段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                DocumentElement element = parseParagraph(paragraph, currentPosition);
                if (element != null && !element.getContent().trim().isEmpty()) {
                    elements.add(element);
                    
                    // 创建位置映射
                    PositionMapping mapping = new PositionMapping(
                        element.getElementId(),
                        element.getStartPosition(),
                        element.getEndPosition(),
                        currentPosition,
                        currentPosition + element.getContent().length()
                    );
                    positionMappings.add(mapping);
                    
                    currentPosition = element.getEndPosition();
                }
            }
            
            // 解析表格
            for (XWPFTable table : document.getTables()) {
                DocumentElement element = parseTable(table, currentPosition);
                if (element != null) {
                    elements.add(element);
                    
                    PositionMapping mapping = new PositionMapping(
                        element.getElementId(),
                        element.getStartPosition(),
                        element.getEndPosition(),
                        currentPosition,
                        currentPosition + element.getContent().length()
                    );
                    positionMappings.add(mapping);
                    
                    currentPosition = element.getEndPosition();
                }
            }
            
            // 设置结果
            result.setElements(elements);
            result.setPositionMappings(positionMappings);
            result.setMetadata(extractMetadata(document));
            
            log.info("文档解析完成，共解析 {} 个元素", elements.size());
            return result;
            
        } catch (Exception e) {
            log.error("文档解析失败: {}", filePath, e);
            throw new IOException("文档解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析段落
     */
    private DocumentElement parseParagraph(XWPFParagraph paragraph, int startPosition) {
        String text = paragraph.getText();
        if (text == null || text.trim().isEmpty()) {
            return null;
        }
        
        DocumentElement element = new DocumentElement("paragraph", text, startPosition, startPosition + text.length());
        
        // 提取样式信息
        Map<String, Object> style = new HashMap<>();
        if (paragraph.getStyle() != null) {
            style.put("style", paragraph.getStyle());
        }
        
        // 提取对齐方式
        if (paragraph.getAlignment() != null) {
            style.put("alignment", paragraph.getAlignment().toString());
        }
        
        // 提取字体信息（从第一个run获取）
        List<XWPFRun> runs = paragraph.getRuns();
        if (!runs.isEmpty()) {
            XWPFRun firstRun = runs.get(0);
            if (firstRun.getFontFamily() != null) {
                style.put("fontFamily", firstRun.getFontFamily());
            }
            if (firstRun.getFontSize() != -1) {
                style.put("fontSize", firstRun.getFontSize());
            }
            style.put("bold", firstRun.isBold());
            style.put("italic", firstRun.isItalic());
        }
        
        element.setStyle(style);
        return element;
    }

    /**
     * 解析表格
     */
    private DocumentElement parseTable(XWPFTable table, int startPosition) {
        StringBuilder tableContent = new StringBuilder();
        
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                String cellText = cell.getText();
                if (cellText != null && !cellText.trim().isEmpty()) {
                    tableContent.append(cellText).append("\t");
                }
            }
            tableContent.append("\n");
        }
        
        String content = tableContent.toString().trim();
        if (content.isEmpty()) {
            return null;
        }
        
        DocumentElement element = new DocumentElement("table", content, startPosition, startPosition + content.length());
        
        // 表格样式信息
        Map<String, Object> style = new HashMap<>();
        style.put("rows", table.getRows().size());
        style.put("columns", table.getRows().isEmpty() ? 0 : table.getRows().get(0).getTableCells().size());
        element.setStyle(style);
        
        return element;
    }

    /**
     * 提取文档元数据
     */
    private Map<String, Object> extractMetadata(XWPFDocument document) {
        Map<String, Object> metadata = new HashMap<>();
        
        try {
            // 基本信息
            if (document.getProperties() != null && document.getProperties().getCoreProperties() != null) {
                var coreProps = document.getProperties().getCoreProperties();
                metadata.put("title", coreProps.getTitle());
                metadata.put("creator", coreProps.getCreator());
                metadata.put("description", coreProps.getDescription());
                metadata.put("subject", coreProps.getSubject());
                metadata.put("created", coreProps.getCreated());
                metadata.put("modified", coreProps.getModified());
            }
            
            // 统计信息
            metadata.put("paragraphCount", document.getParagraphs().size());
            metadata.put("tableCount", document.getTables().size());
            
            // 计算字符数
            int totalChars = document.getParagraphs().stream()
                    .mapToInt(p -> p.getText() != null ? p.getText().length() : 0)
                    .sum();
            metadata.put("characterCount", totalChars);
            
        } catch (Exception e) {
            log.warn("提取文档元数据时出现错误", e);
        }
        
        return metadata;
    }

    /**
     * 检查文档是否可以解析
     */
    public boolean canParse(String filePath) {
        try (InputStream inputStream = new FileInputStream(filePath);
             XWPFDocument document = new XWPFDocument(inputStream)) {
            return true;
        } catch (Exception e) {
            log.warn("文档无法解析: {}", filePath, e);
            return false;
        }
    }


    public HeaderFooterInfo extractHeaderFooterInfo(XWPFDocument document) {
        HeaderFooterInfo headerFooterInfo = new HeaderFooterInfo();

        try {
            log.debug("开始提取页眉页脚信息");

            // 在POI 5.x中，直接从document获取页眉页脚
            // 提取默认页眉
            extractHeaderInfoFromDocument(document, headerFooterInfo);

            // 提取默认页脚
            extractFooterInfoFromDocument(document, headerFooterInfo);

            log.debug("页眉页脚信息提取完成: hasHeader={}, hasFooter={}",
                     headerFooterInfo.hasHeader(), headerFooterInfo.hasFooter());

        } catch (Exception e) {
            log.warn("提取页眉页脚信息时出现错误: {}", e.getMessage(), e);
            // 返回空的HeaderFooterInfo，不影响主流程
        }

        return headerFooterInfo;
    }

    /**
     * 从文档中提取页眉信息
     */
    private void extractHeaderInfoFromDocument(XWPFDocument document, HeaderFooterInfo headerFooterInfo) {
        try {
            // 在POI 5.x中，简化处理：通过段落查找页眉相关信息
            // 由于页眉页脚API在不同版本中变化较大，我们采用更通用的方法

            // 检查文档属性中是否有标题信息
            if (document.getProperties() != null && document.getProperties().getCoreProperties() != null) {
                String title = document.getProperties().getCoreProperties().getTitle();
                if (title != null && !title.trim().isEmpty()) {
                    headerFooterInfo.setHeaderCenter(title.trim());
                }
            }

            // 如果没有找到标题，尝试从第一个段落获取可能的页眉信息
            if ((headerFooterInfo.getHeaderCenter() == null || headerFooterInfo.getHeaderCenter().trim().isEmpty())
                && !document.getParagraphs().isEmpty()) {

                XWPFParagraph firstParagraph = document.getParagraphs().get(0);
                String firstText = firstParagraph.getText();

                // 如果第一个段落很短且看起来像标题，可能是页眉信息
                if (firstText != null && firstText.trim().length() > 0 && firstText.trim().length() < 100) {
                    // 检查是否包含常见的页眉关键词
                    if (firstText.contains("招标") || firstText.contains("项目") || firstText.contains("文件")
                        || firstText.contains("标书") || firstText.contains("合同")) {
                        headerFooterInfo.setHeaderCenter(firstText.trim());
                    }
                }
            }

        } catch (Exception e) {
            log.debug("提取页眉信息时出现错误: {}", e.getMessage());
        }
    }

    /**
     * 从文档中提取页脚信息
     */
    private void extractFooterInfoFromDocument(XWPFDocument document, HeaderFooterInfo headerFooterInfo) {
        try {
            // 在POI 5.x中，简化处理：查找可能的页脚信息

            // 检查文档属性中的作者或公司信息作为页脚
            if (document.getProperties() != null && document.getProperties().getCoreProperties() != null) {
                String creator = document.getProperties().getCoreProperties().getCreator();
                if (creator != null && !creator.trim().isEmpty()) {
                    headerFooterInfo.setFooterLeft(creator.trim());
                }

                String company = document.getProperties().getCoreProperties().getCategory();
                if (company != null && !company.trim().isEmpty()) {
                    headerFooterInfo.setFooterRight(company.trim());
                }
            }

            // 尝试从最后几个段落查找页脚信息
            List<XWPFParagraph> paragraphs = document.getParagraphs();
            if (!paragraphs.isEmpty()) {
                // 检查最后几个段落是否包含页脚信息
                for (int i = Math.max(0, paragraphs.size() - 3); i < paragraphs.size(); i++) {
                    XWPFParagraph paragraph = paragraphs.get(i);
                    String text = paragraph.getText();

                    if (text != null && text.trim().length() > 0 && text.trim().length() < 50) {
                        // 检查是否包含常见的页脚关键词
                        if (text.contains("页") || text.contains("第") || text.contains("/")
                            || text.matches(".*\\d+.*")) {
                            if (headerFooterInfo.getFooterCenter() == null || headerFooterInfo.getFooterCenter().trim().isEmpty()) {
                                headerFooterInfo.setFooterCenter(text.trim());
                                break;
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.debug("提取页脚信息时出现错误: {}", e.getMessage());
        }
    }

    /**
     * 从页眉页脚对象中提取文本
     */
    private String extractHeaderFooterText(XWPFHeaderFooter headerFooter) {
        if (headerFooter == null) {
            return null;
        }

        try {
            StringBuilder text = new StringBuilder();

            // 提取段落文本
            for (XWPFParagraph paragraph : headerFooter.getParagraphs()) {
                String paragraphText = paragraph.getText();
                if (paragraphText != null && !paragraphText.trim().isEmpty()) {
                    if (text.length() > 0) {
                        text.append(" ");
                    }
                    text.append(paragraphText.trim());
                }
            }

            // 提取表格文本（如果页眉页脚中有表格）
            for (XWPFTable table : headerFooter.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        String cellText = cell.getText();
                        if (cellText != null && !cellText.trim().isEmpty()) {
                            if (text.length() > 0) {
                                text.append(" ");
                            }
                            text.append(cellText.trim());
                        }
                    }
                }
            }

            return text.toString();

        } catch (Exception e) {
            log.debug("提取页眉页脚文本时出现错误: {}", e.getMessage());
            return null;
        }
    }


    public List<PageInfo> calculatePageBreaks(XWPFDocument document, HeaderFooterInfo headerFooterInfo) {
        try {
            log.debug("开始计算文档页码分割点");

            if (document == null) {
                log.warn("文档为空，无法计算页码");
                return Collections.singletonList(createDefaultPageInfo());
            }

            // 使用PageBreakCalculator计算页码
            List<PageInfo> pages = pageBreakCalculator.calculatePageBreaks(document, headerFooterInfo);

            log.debug("页码计算完成，共{}页", pages.size());
            return pages;

        } catch (Exception e) {
            log.warn("计算页码分割点时出现错误: {}", e.getMessage(), e);
            // 返回默认页面信息，不影响主流程
            return Collections.singletonList(createDefaultPageInfo());
        }
    }

    /**
     * 创建默认页面信息
     */
    private PageInfo createDefaultPageInfo() {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageNumber(1);
        pageInfo.setStartPosition(0);
        pageInfo.setEndPosition(1000);
        pageInfo.setContentLength(1000);
        pageInfo.setHasTable(false);
        pageInfo.setHasImage(false);
        pageInfo.setHasPageBreak(false);
        pageInfo.setEstimatedLines(40);
        pageInfo.setConfidence(0.5);
        pageInfo.setHeaderText("默认页眉");
        pageInfo.setFooterText("第1页");

        return pageInfo;
    }

    /**
     * 检查页码计算功能是否可用
     */
    public boolean canCalculatePageBreaks() {
        return pageBreakCalculator != null;
    }


    public HeaderFooterInfo extractHeaderFooterInfoFromFile(String filePath) {
        try (InputStream inputStream = new FileInputStream(filePath);
             XWPFDocument document = new XWPFDocument(inputStream)) {

            return extractHeaderFooterInfo(document);

        } catch (Exception e) {
            log.warn("从文件提取页眉页脚信息失败: filePath={}", filePath, e);
            return new HeaderFooterInfo(); // 返回空的HeaderFooterInfo
        }
    }

    /**
     * 从文件路径计算页码分割点
     */
    public List<PageInfo> calculatePageBreaksFromFile(String filePath, HeaderFooterInfo headerFooterInfo) {
        try (InputStream inputStream = new FileInputStream(filePath);
             XWPFDocument document = new XWPFDocument(inputStream)) {

            return calculatePageBreaks(document, headerFooterInfo);

        } catch (Exception e) {
            log.warn("从文件计算页码分割点失败: filePath={}", filePath, e);
            return Collections.singletonList(createDefaultPageInfo()); // 返回默认页面信息
        }
    }
}
