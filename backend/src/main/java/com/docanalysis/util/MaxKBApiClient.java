package com.docanalysis.util;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.MultipartBodyBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.io.File;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Slf4j
@Component
public class MaxKBApiClient {

    @Value("${app.maxkb.base-url}")
    private String baseUrl;

    @Value("${app.maxkb.extract-agent.application-id}")
    private String extractAgentId;

    @Value("${app.maxkb.extract-agent.api-key}")
    private String extractApiKey;

    @Value("${app.maxkb.detect-agent.application-id}")
    private String detectAgentId;

    @Value("${app.maxkb.detect-agent.api-key}")
    private String detectApiKey;

    @Value("${app.maxkb.timeout:30000}")
    private int timeout;

    @Value("${app.maxkb.retry-attempts:3}")
    private int retryAttempts;


    @Value("${app.maxkb.qa-agent.qa-url}")
    private String qaUrl;
    @Value("${app.maxkb.qa-agent.access_token}")
    private String accessToken;
    @Value("${app.maxkb.qa-agent.api-key}")
    private String apiKey;

    private final WebClient webClient;

    public MaxKBApiClient(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(50 * 1024 * 1024)) // 50MB
                .build();
    }

    /**
     * 智能体类型枚举
     */
    public enum AgentType {
        EXTRACT("提取智能体"),
        DETECT("检测智能体");

        private final String description;

        AgentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 获取代码值（枚举名称）
         */
        public String getCode() {
            return this.name();
        }
    }

    /**
     * 创建会话
     */
    public CompletableFuture<String> createSession(AgentType agentType) {
        String applicationId = getApplicationId(agentType);
        String apiKey = getApiKey(agentType);
        String requestUrl = baseUrl + "/api/application/" + applicationId + "/chat/open";

        log.info("=== 创建{}会话 ===", agentType.getDescription());
        log.info("请求URL: {}", requestUrl);
        log.info("请求方法: POST");
        log.info("请求头:");
        log.info("  Authorization: {}", apiKey);
        log.info("  Content-Type: {}", MediaType.APPLICATION_JSON_VALUE);
        log.info("applicationId: {}", applicationId);

        return webClient.get()
                .uri(requestUrl)
                .header(HttpHeaders.AUTHORIZATION, apiKey)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .retrieve()
                .bodyToMono(Map.class)
                .map(response -> {
                    log.info("=== {}会话创建响应 ===", agentType.getDescription());
                    log.info("完整响应数据: {}", response);

                    String chatId = (String) response.get("data");
                    log.info("提取的chatId: {}", chatId);
                    log.info("{}会话创建成功", agentType.getDescription());
                    log.info("=== 会话创建完成 ===");

                    return chatId;
                })
                .doOnError(error -> {
                    log.error("=== {}会话创建失败 ===", agentType.getDescription());
                    log.error("请求URL: {}", requestUrl);
                    log.error("错误信息: {}", error.getMessage(), error);
                    log.error("=== 会话创建错误结束 ===");
                })
                .toFuture();
    }

    /**
     * 上传文件
     */
    public CompletableFuture<Map<String, Object>> uploadFile(AgentType agentType, String chatId, String filePath) {
        String applicationId = getApplicationId(agentType);
        String apiKey = getApiKey(agentType);
        String requestUrl = baseUrl + "/api/application/" + applicationId + "/chat/" + chatId + "/upload_file";

        log.info("=== 上传文件到{} ===", agentType.getDescription());
        log.info("请求URL: {}", requestUrl);
        log.info("请求方法: POST");
        log.info("chatId: {}", chatId);
        log.info("filePath: {}", filePath);
        log.info("请求头:");
        log.info("  Authorization: {}", apiKey);
        log.info("  Content-Type: multipart/form-data");

        File file = new File(filePath);
        if (!file.exists()) {
            log.error("文件不存在: {}", filePath);
            CompletableFuture<Map<String, Object>> future = new CompletableFuture<>();
            future.completeExceptionally(new IllegalArgumentException("文件不存在: " + filePath));
            return future;
        }

        log.info("文件信息:");
        log.info("  文件名: {}", file.getName());
        log.info("  文件大小: {} bytes", file.length());
        log.info("  文件路径: {}", file.getAbsolutePath());

        MultipartBodyBuilder builder = new MultipartBodyBuilder();
        builder.part("file", new FileSystemResource(file));
        MultiValueMap<String, org.springframework.http.HttpEntity<?>> parts = builder.build();

        return webClient.post()
                .uri(requestUrl)
                .header(HttpHeaders.AUTHORIZATION, apiKey)
                .body(BodyInserters.fromMultipartData(parts))
                .retrieve()
                .bodyToMono(Map.class)
                .timeout(Duration.ofMillis(timeout * 2)) // 文件上传使用更长的超时时间
                .retryWhen(Retry.backoff(retryAttempts, Duration.ofSeconds(2))
                        .filter(this::isRetryableException))
                .map(response -> {
                    log.info("=== 文件上传到{}响应 ===", agentType.getDescription());
                    log.info("完整响应数据: {}", response);

                    // 处理响应数据，data字段可能是数组或对象
                    Object dataObj = response.get("data");
                    Map<String, Object> fileInfo = null;

                    if (dataObj instanceof java.util.List) {
                        // 如果data是数组，取第一个元素
                        @SuppressWarnings("unchecked")
                        java.util.List<Map<String, Object>> dataList = (java.util.List<Map<String, Object>>) dataObj;
                        if (!dataList.isEmpty()) {
                            fileInfo = dataList.get(0);
                            log.info("从数组中提取文件信息: {}", fileInfo);
                        } else {
                            log.warn("响应数据数组为空");
                            fileInfo = new java.util.HashMap<>();
                        }
                    } else if (dataObj instanceof Map) {
                        // 如果data是对象，直接使用
                        @SuppressWarnings("unchecked")
                        Map<String, Object> dataMap = (Map<String, Object>) dataObj;
                        fileInfo = dataMap;
                        log.info("直接提取文件信息: {}", fileInfo);
                    } else {
                        log.error("未知的响应数据类型: {}", dataObj != null ? dataObj.getClass() : "null");
                        fileInfo = new java.util.HashMap<>();
                    }

                    log.info("最终文件信息: {}", fileInfo);
                    log.info("文件上传到{}成功", agentType.getDescription());
                    log.info("=== 文件上传完成 ===");

                    return fileInfo;
                })
                .doOnError(error -> {
                    log.error("=== 文件上传到{}失败 ===", agentType.getDescription());
                    log.error("请求URL: {}", requestUrl);
                    log.error("文件路径: {}", filePath);
                    log.error("错误信息: {}", error.getMessage(), error);
                    log.error("=== 文件上传错误结束 ===");
                })
                .toFuture();
    }

    /**
     * 发送对话请求
     */
    public CompletableFuture<Map<String, Object>> sendChatRequest(AgentType agentType, String chatId, String message, Map<String, Object> fileInfo) {
        // ========== 临时测试代码开始 ==========
        // 注意：这是临时测试实现，用于快速测试文档分析流程
        // 测试完成后需要恢复原有的MaxKB API调用实现
        log.info("使用临时测试数据进行{}: chatId={}, message={}", agentType.getDescription(), chatId,
                message.length() > 100 ? message.substring(0, 100) + "..." : message);

        if (agentType.getCode().equals("EXTRACT")) {
            // 返回模拟的数据提取结果
            Map<String, Object> extractResult = createMockExtractResult();
            log.info("返回模拟数据提取结果: {}", extractResult);
            return CompletableFuture.completedFuture(extractResult);
        } else {
            // 返回模拟的异常检测结果
            Map<String, Object> detectResult = createMockDetectResult();
            log.info("返回模拟异常检测结果: {}", detectResult);
            return CompletableFuture.completedFuture(detectResult);
        }
        // ========== 临时测试代码结束 ==========
//        String applicationId = getApplicationId(agentType);
//        String apiKey = getApiKey(agentType);
//
//        log.info("发送对话请求到{}: chatId={}, message={}", agentType.getDescription(), chatId,
//                message.length() > 100 ? message.substring(0, 100) + "..." : message);
//
//        // 构建请求体，包含document_list
//        String requestUrl = baseUrl + "/api/application/chat_message" + "/" + chatId;
//
//        Map<String, Object> requestBody = new java.util.HashMap<>();
//        requestBody.put("message", message);
//        requestBody.put("stream", false);
//
//        // 添加文档列表
//        if (fileInfo != null) {
//            java.util.List<Map<String, Object>> documentList = java.util.Arrays.asList(fileInfo);
//            requestBody.put("document_list", documentList);
//            log.info("添加文档到请求: {}", fileInfo);
//        }
//
//        log.info("=== 发送对话请求到{} ===", agentType.getDescription());
//        log.info("请求URL: {}", requestUrl);
//        log.info("请求方法: POST");
//        log.info("请求体: {}", requestBody);
//        log.info("请求头:");
//        log.info("  Authorization: {}", apiKey);
//        log.info("  Content-Type: {}", MediaType.APPLICATION_JSON_VALUE);
//
//        return webClient.post()
//                .uri(requestUrl)
//                .header(HttpHeaders.AUTHORIZATION, apiKey)
//                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
//                .bodyValue(requestBody)
//                .retrieve()
//                .bodyToMono(Map.class)
//                .map(response -> {
//                    log.info("=== {}对话请求响应 ===", agentType.getDescription());
//                    log.info("完整响应数据: {}", response);
//
//                    @SuppressWarnings("unchecked")
//                    Map<String, Object> typedResponse = (Map<String, Object>) response;
//
//                    // 提取响应中的关键信息
//                    if (typedResponse.containsKey("data")) {
//                        log.info("响应数据字段: {}", typedResponse.get("data"));
//                    }
//                    if (typedResponse.containsKey("code")) {
//                        log.info("响应状态码: {}", typedResponse.get("code"));
//                    }
//                    if (typedResponse.containsKey("message")) {
//                        log.info("响应消息: {}", typedResponse.get("message"));
//                    }
//
//                    log.info("{}对话请求成功", agentType.getDescription());
//                    log.info("=== 对话请求完成 ===");
//
//                    return typedResponse;
//                })
//                .doOnError(error -> {
//                    log.error("=== {}对话请求失败 ===", agentType.getDescription());
//                    log.error("请求URL: {}", requestUrl);
//                    log.error("错误信息: {}", error.getMessage(), error);
//                    log.error("=== 对话请求错误结束 ===");
//                })
//                .toFuture();

    }



    /**
     * 并行创建两个智能体的会话
     */
    public CompletableFuture<Map<AgentType, String>> createBothSessions() {
        log.info("开始并行创建两个智能体会话");

        CompletableFuture<String> extractSessionFuture = createSession(AgentType.EXTRACT);
        CompletableFuture<String> detectSessionFuture = createSession(AgentType.DETECT);

        return CompletableFuture.allOf(extractSessionFuture, detectSessionFuture)
                .thenApply(v -> {
                    Map<AgentType, String> sessions = Map.of(
                            AgentType.EXTRACT, extractSessionFuture.join(),
                            AgentType.DETECT, detectSessionFuture.join()
                    );
                    log.info("两个智能体会话创建完成: extract={}, detect={}",
                            sessions.get(AgentType.EXTRACT), sessions.get(AgentType.DETECT));
                    return sessions;
                })
                .exceptionally(throwable -> {
                    log.error("创建智能体会话失败", throwable);
                    throw new RuntimeException("创建智能体会话失败", throwable);
                });
    }

    /**
     * 并行上传文件到两个智能体
     */
    public CompletableFuture<Map<AgentType, Map<String, Object>>> uploadFileToBoth(Map<AgentType, String> sessions, String filePath) {
        log.info("开始并行上传文件到两个智能体: filePath={}", filePath);

        CompletableFuture<Map<String, Object>> extractUploadFuture = uploadFile(AgentType.EXTRACT, sessions.get(AgentType.EXTRACT), filePath);
        CompletableFuture<Map<String, Object>> detectUploadFuture = uploadFile(AgentType.DETECT, sessions.get(AgentType.DETECT), filePath);

        return CompletableFuture.allOf(extractUploadFuture, detectUploadFuture)
                .thenApply(v -> {
                    Map<AgentType, Map<String, Object>> fileInfos = Map.of(
                            AgentType.EXTRACT, extractUploadFuture.join(),
                            AgentType.DETECT, detectUploadFuture.join()
                    );
                    log.info("文件上传到两个智能体完成: extract={}, detect={}",
                            fileInfos.get(AgentType.EXTRACT), fileInfos.get(AgentType.DETECT));
                    return fileInfos;
                })
                .exceptionally(throwable -> {
                    log.error("文件上传失败", throwable);
                    throw new RuntimeException("文件上传失败", throwable);
                });
    }

    /**
     * 并行发送对话请求到两个智能体
     */
    public CompletableFuture<Map<AgentType, Map<String, Object>>> sendChatRequestToBoth(
            Map<AgentType, String> sessions, Map<AgentType, Map<String, Object>> fileInfos,
            String extractMessage, String detectMessage) {

        log.info("开始并行发送对话请求到两个智能体");

        CompletableFuture<Map<String, Object>> extractChatFuture = sendChatRequest(
                AgentType.EXTRACT,
                sessions.get(AgentType.EXTRACT),
                extractMessage,
                fileInfos.get(AgentType.EXTRACT)
        );
        CompletableFuture<Map<String, Object>> detectChatFuture = sendChatRequest(
                AgentType.DETECT,
                sessions.get(AgentType.DETECT),
                detectMessage,
                fileInfos.get(AgentType.DETECT)
        );

        return CompletableFuture.allOf(extractChatFuture, detectChatFuture)
                .thenApply(v -> {
                    Map<AgentType, Map<String, Object>> results = Map.of(
                            AgentType.EXTRACT, extractChatFuture.join(),
                            AgentType.DETECT, detectChatFuture.join()
                    );
                    log.info("两个智能体对话请求完成");
                    return results;
                })
                .exceptionally(throwable -> {
                    log.error("对话请求失败", throwable);
                    throw new RuntimeException("对话请求失败", throwable);
                });
    }

    /**
     * 获取应用ID
     */
    private String getApplicationId(AgentType agentType) {
        return agentType == AgentType.EXTRACT ? extractAgentId : detectAgentId;
    }

    /**
     * 获取API密钥
     */
    private String getApiKey(AgentType agentType) {
        return agentType == AgentType.EXTRACT ? extractApiKey : detectApiKey;
    }

    /**
     * 判断是否为可重试的异常
     */
    private boolean isRetryableException(Throwable throwable) {
        if (throwable instanceof WebClientResponseException) {
            WebClientResponseException ex = (WebClientResponseException) throwable;
            int statusCode = ex.getStatusCode().value();
            // 5xx服务器错误和429限流错误可以重试
            return statusCode >= 500 || statusCode == 429;
        }
        // 网络超时等异常也可以重试
        return throwable instanceof java.net.SocketTimeoutException ||
                throwable instanceof java.util.concurrent.TimeoutException;
    }

    // ========== 临时测试数据创建方法 ==========
    // 注意：这些是临时测试方法，测试完成后可以删除

    /**
     * 创建模拟的数据提取结果 - 从提取.json文件读取
     */
    private Map<String, Object> createMockExtractResult() {
        try {
            // 读取提取.json文件
            String jsonContent = java.nio.file.Files.readString(
                java.nio.file.Paths.get("提取.json"),
                java.nio.charset.StandardCharsets.UTF_8
            );

            // 解析JSON
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            @SuppressWarnings("unchecked")
            Map<String, Object> result = mapper.readValue(jsonContent, Map.class);

            log.info("成功从提取.json文件读取数据: {}", result);
            return result;

        } catch (Exception e) {
            log.error("读取提取.json文件失败，使用备用数据", e);

            // 备用数据（如果文件读取失败）
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("code", 200);
            result.put("message", "成功");

            Map<String, Object> data = new java.util.HashMap<>();
            data.put("content", "根据文档分析，提取到以下关键信息：\n\n" +
                    "1. **项目名称**：合肥市房屋建筑和市政基础设施工程施工招标文件示范文本\n" +
                    "2. **招标编号**：2025BFLGZ00671\n" +
                    "3. **项目类型**：房屋建筑工程\n" +
                    "（备用数据 - 提取.json文件读取失败）");

            data.put("chat_id", "backup-extract-chat-id");
            data.put("is_end", true);

            result.put("data", data);
            return result;
        }
    }

    /**
     * 创建模拟的异常检测结果 - 从审查.json文件读取
     */
    private Map<String, Object> createMockDetectResult() {
        try {
            // 读取审查.json文件
            String jsonContent = java.nio.file.Files.readString(
                java.nio.file.Paths.get("审查.json"),
                java.nio.charset.StandardCharsets.UTF_8
            );

            // 解析JSON
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            @SuppressWarnings("unchecked")
            Map<String, Object> result = mapper.readValue(jsonContent, Map.class);

            return result;

        } catch (Exception e) {
            log.error("读取审查.json文件失败，使用备用数据", e);

            // 备用数据（如果文件读取失败）
            Map<String, Object> result = new java.util.HashMap<>();
            result.put("code", 200);
            result.put("message", "成功");

            Map<String, Object> data = new java.util.HashMap<>();
            data.put("content", "经过文档异常检测分析，发现以下问题：\n\n" +
                    "**🔍 检测到的异常问题：**\n\n" +
                    "1. **信息不完整**（中等风险）\n" +
                    "2. **格式规范性问题**（低风险）\n" +
                    "（备用数据 - 审查.json文件读取失败）");

            data.put("chat_id", "backup-detect-chat-id");
            data.put("is_end", true);

            result.put("data", data);
            return result;
        }
    }

    public String createToken() {
        String requestUrl = qaUrl + "/api/application/authentication";
        Map<String,String> map = new HashMap<>();
        map.put("access_token", accessToken);
        String json = JSONUtil.toJsonStr(map);

        String result2 = HttpRequest.post(requestUrl)
                .header(Header.AUTHORIZATION,apiKey)
                .body(json)
                .execute().body();

        return JSONUtil.parseObj(result2).getStr("data");
    }

}
