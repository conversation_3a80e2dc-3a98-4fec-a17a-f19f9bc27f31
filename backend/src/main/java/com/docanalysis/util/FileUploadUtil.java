package com.docanalysis.util;

import com.docanalysis.config.FileStorageConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;


@Slf4j
@Component
public class FileUploadUtil {

    @Autowired
    private FileStorageConfig fileStorageConfig;

    /**
     * 允许的文件扩展名
     */
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList(".docx");

    /**
     * 允许的MIME类型
     */
    private static final List<String> ALLOWED_MIME_TYPES = Arrays.asList(
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    );



    /**
     * 验证上传文件
     */
    public void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 验证文件大小
        if (file.getSize() > fileStorageConfig.getMaxSize()) {
            throw new IllegalArgumentException(
                    String.format("文件大小超过限制，最大允许 %s，当前文件 %s",
                            formatFileSize(fileStorageConfig.getMaxSize()),
                            formatFileSize(file.getSize()))
            );
        }

        // 验证文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String extension = getFileExtension(originalFilename);
        if (!ALLOWED_EXTENSIONS.contains(extension.toLowerCase())) {
            throw new IllegalArgumentException(
                    String.format("不支持的文件类型，仅支持: %s", String.join(", ", ALLOWED_EXTENSIONS))
            );
        }

        // 验证MIME类型
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_MIME_TYPES.contains(contentType)) {
            throw new IllegalArgumentException(
                    String.format("不支持的文件格式，检测到: %s", contentType)
            );
        }

        // 验证文件名安全性（防止路径遍历攻击）
        if (containsInvalidCharacters(originalFilename)) {
            throw new IllegalArgumentException("文件名包含非法字符");
        }
    }

    /**
     * 生成唯一的文件名
     */
    public String generateUniqueFilename(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("doc_%s_%s%s", timestamp, uuid, extension);
    }

    /**
     * 创建文件存储路径
     */
    public String createStoragePath(String filename) {
        try {
            String baseDir = fileStorageConfig.getResolvedUploadDir();
            Path storagePath;

            // 根据存储策略创建路径
            switch (fileStorageConfig.getStorageStrategy()) {
                case "date":
                    String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                    storagePath = Paths.get(baseDir, dateDir);
                    break;
                case "hash":
                    String hashDir = filename.substring(0, 2); // 使用文件名前两个字符作为哈希目录
                    storagePath = Paths.get(baseDir, hashDir);
                    break;
                case "flat":
                default:
                    storagePath = Paths.get(baseDir);
                    break;
            }

            // 确保目录存在
            if (!Files.exists(storagePath)) {
                Files.createDirectories(storagePath);
                log.debug("创建存储目录: {}", storagePath.toAbsolutePath());
            }

            // 返回完整的文件路径
            Path filePath = storagePath.resolve(filename);
            String absolutePath = filePath.toAbsolutePath().toString();

            log.debug("创建存储路径: {}", absolutePath);
            return absolutePath;

        } catch (IOException e) {
            log.error("创建存储目录失败: baseDir={}, filename={}",
                    fileStorageConfig.getResolvedUploadDir(), filename, e);
            throw new RuntimeException("创建存储目录失败", e);
        }
    }

    /**
     * 保存文件到指定路径
     */
    public void saveFile(MultipartFile file, String filePath) {
        try {
            Path targetPath = Paths.get(filePath);
            Path parentDir = targetPath.getParent();

            log.debug("开始保存文件: {} -> {}", file.getOriginalFilename(), targetPath.toAbsolutePath());

            // 确保父目录存在
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                log.debug("创建父目录: {}", parentDir.toAbsolutePath());
            }

            // 检查文件是否已存在
            if (Files.exists(targetPath)) {
                log.warn("目标文件已存在，将被覆盖: {}", targetPath.toAbsolutePath());
            }

            // 保存文件
            file.transferTo(targetPath.toFile());

            // 验证文件是否保存成功
            if (Files.exists(targetPath)) {
                long savedSize = Files.size(targetPath);
                log.info("文件保存成功: {} (大小: {} bytes)", targetPath.toAbsolutePath(), savedSize);
            } else {
                throw new RuntimeException("文件保存后验证失败，文件不存在");
            }

        } catch (IOException e) {
            log.error("文件保存失败: filePath={}, error={}", filePath, e.getMessage(), e);
            throw new RuntimeException("文件保存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算文件MD5哈希值
     */
    public String calculateMD5(MultipartFile file) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(file.getBytes());
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            log.error("计算文件MD5失败", e);
            return null;
        }
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                log.info("文件删除成功: {}", filePath);
                return true;
            }
            return false;
        } catch (IOException e) {
            log.error("文件删除失败: {}", filePath, e);
            return false;
        }
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex) : "";
    }

    /**
     * 检查文件名是否包含非法字符
     */
    private boolean containsInvalidCharacters(String filename) {
        // 检查路径遍历攻击
        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            return true;
        }
        
        // 检查其他非法字符
        String[] invalidChars = {"<", ">", ":", "\"", "|", "?", "*"};
        for (String invalidChar : invalidChars) {
            if (filename.contains(invalidChar)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double fileSize = size;
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", fileSize, units[unitIndex]);
    }
}
