package com.docanalysis.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


@Slf4j
@Component
public class DocumentHtmlConverter {

    /**
     * HTML转换结果
     */
    public static class HtmlConvertResult {
        private String htmlContent;
        private String cssStyles;
        private Map<String, Object> metadata;

        // Getters and Setters
        public String getHtmlContent() { return htmlContent; }
        public void setHtmlContent(String htmlContent) { this.htmlContent = htmlContent; }
        
        public String getCssStyles() { return cssStyles; }
        public void setCssStyles(String cssStyles) { this.cssStyles = cssStyles; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 将解析结果转换为HTML
     */
    public HtmlConvertResult convertToHtml(POIDocumentParser.DocumentParseResult parseResult) {
        log.info("开始转换文档为HTML格式");
        
        HtmlConvertResult result = new HtmlConvertResult();
        StringBuilder htmlBuilder = new StringBuilder();
        StringBuilder cssBuilder = new StringBuilder();
        
        // 添加HTML头部
        htmlBuilder.append("<!DOCTYPE html>\n");
        htmlBuilder.append("<html lang=\"zh-CN\">\n");
        htmlBuilder.append("<head>\n");
        htmlBuilder.append("    <meta charset=\"UTF-8\">\n");
        htmlBuilder.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        htmlBuilder.append("    <title>文档内容</title>\n");
        htmlBuilder.append("    <style>\n");
        
        // 添加基础CSS样式
        cssBuilder.append(generateBaseCss());
        
        htmlBuilder.append(cssBuilder.toString());
        htmlBuilder.append("    </style>\n");
        htmlBuilder.append("</head>\n");
        htmlBuilder.append("<body>\n");
        htmlBuilder.append("    <div class=\"document-container\">\n");
        
        // 转换文档元素
        List<POIDocumentParser.DocumentElement> elements = parseResult.getElements();
        if (elements != null) {
            for (POIDocumentParser.DocumentElement element : elements) {
                String elementHtml = convertElementToHtml(element);
                htmlBuilder.append(elementHtml);
            }
        }
        
        // 添加HTML尾部
        htmlBuilder.append("    </div>\n");
        htmlBuilder.append("</body>\n");
        htmlBuilder.append("</html>");
        
        result.setHtmlContent(htmlBuilder.toString());
        result.setCssStyles(cssBuilder.toString());
        result.setMetadata(parseResult.getMetadata());
        
        log.info("HTML转换完成，生成内容长度: {} 字符", htmlBuilder.length());
        return result;
    }

    /**
     * 转换单个元素为HTML
     */
    private String convertElementToHtml(POIDocumentParser.DocumentElement element) {
        StringBuilder html = new StringBuilder();
        
        switch (element.getType()) {
            case "paragraph":
                html.append(convertParagraphToHtml(element));
                break;
            case "table":
                html.append(convertTableToHtml(element));
                break;
            case "list":
                html.append(convertListToHtml(element));
                break;
            case "image":
                html.append(convertImageToHtml(element));
                break;
            default:
                html.append(convertDefaultToHtml(element));
                break;
        }
        
        return html.toString();
    }

    /**
     * 转换段落为HTML
     */
    private String convertParagraphToHtml(POIDocumentParser.DocumentElement element) {
        StringBuilder html = new StringBuilder();
        
        html.append("        <p");
        html.append(" id=\"").append(element.getElementId()).append("\"");
        html.append(" class=\"document-paragraph\"");
        html.append(" data-start=\"").append(element.getStartPosition()).append("\"");
        html.append(" data-end=\"").append(element.getEndPosition()).append("\"");
        
        // 添加样式
        String style = generateElementStyle(element.getStyle());
        if (!style.isEmpty()) {
            html.append(" style=\"").append(style).append("\"");
        }
        
        html.append(">");
        html.append(escapeHtml(element.getContent()));
        html.append("</p>\n");
        
        return html.toString();
    }

    /**
     * 转换表格为HTML
     */
    private String convertTableToHtml(POIDocumentParser.DocumentElement element) {
        StringBuilder html = new StringBuilder();
        
        html.append("        <div");
        html.append(" id=\"").append(element.getElementId()).append("\"");
        html.append(" class=\"document-table\"");
        html.append(" data-start=\"").append(element.getStartPosition()).append("\"");
        html.append(" data-end=\"").append(element.getEndPosition()).append("\"");
        html.append(">\n");
        
        html.append("            <table class=\"table table-bordered\">\n");
        
        // 解析表格内容
        String[] rows = element.getContent().split("\n");
        for (int i = 0; i < rows.length; i++) {
            if (rows[i].trim().isEmpty()) continue;
            
            html.append("                <tr>\n");
            String[] cells = rows[i].split("\t");
            for (String cell : cells) {
                if (i == 0) {
                    html.append("                    <th>").append(escapeHtml(cell.trim())).append("</th>\n");
                } else {
                    html.append("                    <td>").append(escapeHtml(cell.trim())).append("</td>\n");
                }
            }
            html.append("                </tr>\n");
        }
        
        html.append("            </table>\n");
        html.append("        </div>\n");
        
        return html.toString();
    }

    /**
     * 转换列表为HTML
     */
    private String convertListToHtml(POIDocumentParser.DocumentElement element) {
        StringBuilder html = new StringBuilder();
        
        html.append("        <ul");
        html.append(" id=\"").append(element.getElementId()).append("\"");
        html.append(" class=\"document-list\"");
        html.append(" data-start=\"").append(element.getStartPosition()).append("\"");
        html.append(" data-end=\"").append(element.getEndPosition()).append("\"");
        html.append(">\n");
        
        String[] items = element.getContent().split("\n");
        for (String item : items) {
            if (!item.trim().isEmpty()) {
                html.append("            <li>").append(escapeHtml(item.trim())).append("</li>\n");
            }
        }
        
        html.append("        </ul>\n");
        
        return html.toString();
    }

    /**
     * 转换图片为HTML
     */
    private String convertImageToHtml(POIDocumentParser.DocumentElement element) {
        StringBuilder html = new StringBuilder();
        
        html.append("        <div");
        html.append(" id=\"").append(element.getElementId()).append("\"");
        html.append(" class=\"document-image\"");
        html.append(" data-start=\"").append(element.getStartPosition()).append("\"");
        html.append(" data-end=\"").append(element.getEndPosition()).append("\"");
        html.append(">\n");
        
        html.append("            <img src=\"").append(element.getContent()).append("\"");
        html.append(" alt=\"文档图片\" class=\"img-responsive\">\n");
        
        html.append("        </div>\n");
        
        return html.toString();
    }

    /**
     * 默认元素转换
     */
    private String convertDefaultToHtml(POIDocumentParser.DocumentElement element) {
        StringBuilder html = new StringBuilder();
        
        html.append("        <div");
        html.append(" id=\"").append(element.getElementId()).append("\"");
        html.append(" class=\"document-element document-").append(element.getType()).append("\"");
        html.append(" data-start=\"").append(element.getStartPosition()).append("\"");
        html.append(" data-end=\"").append(element.getEndPosition()).append("\"");
        html.append(">");
        html.append(escapeHtml(element.getContent()));
        html.append("</div>\n");
        
        return html.toString();
    }

    /**
     * 生成元素样式
     */
    private String generateElementStyle(Map<String, Object> styleMap) {
        if (styleMap == null || styleMap.isEmpty()) {
            return "";
        }
        
        StringBuilder style = new StringBuilder();
        
        // 字体相关
        if (styleMap.containsKey("fontFamily")) {
            style.append("font-family: ").append(styleMap.get("fontFamily")).append("; ");
        }
        if (styleMap.containsKey("fontSize")) {
            style.append("font-size: ").append(styleMap.get("fontSize")).append("pt; ");
        }
        if (Boolean.TRUE.equals(styleMap.get("bold"))) {
            style.append("font-weight: bold; ");
        }
        if (Boolean.TRUE.equals(styleMap.get("italic"))) {
            style.append("font-style: italic; ");
        }
        
        // 对齐方式
        if (styleMap.containsKey("alignment")) {
            String alignment = styleMap.get("alignment").toString().toLowerCase();
            switch (alignment) {
                case "center":
                    style.append("text-align: center; ");
                    break;
                case "right":
                    style.append("text-align: right; ");
                    break;
                case "justify":
                    style.append("text-align: justify; ");
                    break;
                default:
                    style.append("text-align: left; ");
                    break;
            }
        }
        
        return style.toString();
    }

    /**
     * 生成基础CSS样式
     */
    private String generateBaseCss() {
        StringBuilder css = new StringBuilder();
        css.append(".document-container {\n");
        css.append("    max-width: 800px;\n");
        css.append("    margin: 0 auto;\n");
        css.append("    padding: 20px;\n");
        css.append("    font-family: 'Microsoft YaHei', Arial, sans-serif;\n");
        css.append("    line-height: 1.6;\n");
        css.append("    color: #333;\n");
        css.append("}\n\n");

        css.append(".document-paragraph {\n");
        css.append("    margin: 12px 0;\n");
        css.append("    padding: 8px;\n");
        css.append("    border-radius: 4px;\n");
        css.append("    transition: background-color 0.3s ease;\n");
        css.append("}\n\n");

        css.append(".document-paragraph:hover {\n");
        css.append("    background-color: #f8f9fa;\n");
        css.append("}\n\n");

        css.append(".document-paragraph.highlighted {\n");
        css.append("    background-color: #fff3cd;\n");
        css.append("    border-left: 4px solid #ffc107;\n");
        css.append("    padding-left: 12px;\n");
        css.append("}\n\n");

        css.append(".document-table {\n");
        css.append("    margin: 20px 0;\n");
        css.append("    overflow-x: auto;\n");
        css.append("}\n\n");

        css.append(".table {\n");
        css.append("    width: 100%;\n");
        css.append("    border-collapse: collapse;\n");
        css.append("    margin-bottom: 1rem;\n");
        css.append("}\n\n");

        css.append(".table th, .table td {\n");
        css.append("    padding: 8px 12px;\n");
        css.append("    border: 1px solid #dee2e6;\n");
        css.append("    text-align: left;\n");
        css.append("}\n\n");

        css.append(".table th {\n");
        css.append("    background-color: #f8f9fa;\n");
        css.append("    font-weight: bold;\n");
        css.append("}\n\n");

        css.append(".document-list {\n");
        css.append("    margin: 16px 0;\n");
        css.append("    padding-left: 24px;\n");
        css.append("}\n\n");

        css.append(".document-list li {\n");
        css.append("    margin: 8px 0;\n");
        css.append("}\n\n");

        css.append(".document-image {\n");
        css.append("    margin: 20px 0;\n");
        css.append("    text-align: center;\n");
        css.append("}\n\n");

        css.append(".img-responsive {\n");
        css.append("    max-width: 100%;\n");
        css.append("    height: auto;\n");
        css.append("    border-radius: 4px;\n");
        css.append("    box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n");
        css.append("}\n\n");

        css.append(".document-element {\n");
        css.append("    margin: 12px 0;\n");
        css.append("    padding: 8px;\n");
        css.append("    border-radius: 4px;\n");
        css.append("}\n\n");

        css.append("/* 高亮样式 */\n");
        css.append(".highlight-extract {\n");
        css.append("    background-color: #d4edda !important;\n");
        css.append("    border-left: 4px solid #28a745 !important;\n");
        css.append("}\n\n");

        css.append(".highlight-detect {\n");
        css.append("    background-color: #f8d7da !important;\n");
        css.append("    border-left: 4px solid #dc3545 !important;\n");
        css.append("}\n\n");

        css.append("/* 响应式设计 */\n");
        css.append("@media (max-width: 768px) {\n");
        css.append("    .document-container {\n");
        css.append("        padding: 10px;\n");
        css.append("    }\n");
        css.append("    .table {\n");
        css.append("        font-size: 14px;\n");
        css.append("    }\n");
        css.append("    .table th, .table td {\n");
        css.append("        padding: 6px 8px;\n");
        css.append("    }\n");
        css.append("}\n");

        return css.toString();
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#39;")
                   .replace("\n", "<br>");
    }
}
