package com.docanalysis.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;


@Slf4j
@Component
public class HeaderFooterPerformanceMonitor {

    private final ConcurrentHashMap<String, PerformanceMetrics> metrics = new ConcurrentHashMap<>();
    private final AtomicLong totalProcessedDocuments = new AtomicLong(0);
    private final AtomicLong totalSuccessfulExtractions = new AtomicLong(0);
    private final AtomicLong totalFailedExtractions = new AtomicLong(0);

    /**
     * 性能指标数据类
     */
    @Data
    public static class PerformanceMetrics {
        private long totalExecutions = 0;
        private long totalExecutionTime = 0;
        private long minExecutionTime = Long.MAX_VALUE;
        private long maxExecutionTime = 0;
        private long successCount = 0;
        private long failureCount = 0;
        private long lastExecutionTime = 0;

        public double getAverageExecutionTime() {
            return totalExecutions > 0 ? (double) totalExecutionTime / totalExecutions : 0;
        }

        public double getSuccessRate() {
            return totalExecutions > 0 ? (double) successCount / totalExecutions * 100 : 0;
        }
    }

    /**
     * 记录操作开始
     */
    public long startOperation(String operationType) {
        return System.currentTimeMillis();
    }

    /**
     * 记录操作成功完成
     */
    public void recordSuccess(String operationType, long startTime) {
        long executionTime = System.currentTimeMillis() - startTime;
        recordExecution(operationType, executionTime, true);
        
        if ("HEADER_FOOTER_EXTRACTION".equals(operationType)) {
            totalSuccessfulExtractions.incrementAndGet();
        }
        
        log.debug("操作成功: type={}, time={}ms", operationType, executionTime);
    }

    /**
     * 记录操作失败
     */
    public void recordFailure(String operationType, long startTime, Exception exception) {
        long executionTime = System.currentTimeMillis() - startTime;
        recordExecution(operationType, executionTime, false);
        
        if ("HEADER_FOOTER_EXTRACTION".equals(operationType)) {
            totalFailedExtractions.incrementAndGet();
        }
        
        log.warn("操作失败: type={}, time={}ms, error={}", operationType, executionTime, exception.getMessage());
    }

    /**
     * 记录执行信息
     */
    private void recordExecution(String operationType, long executionTime, boolean success) {
        totalProcessedDocuments.incrementAndGet();
        
        metrics.compute(operationType, (key, existing) -> {
            PerformanceMetrics metrics = existing != null ? existing : new PerformanceMetrics();
            
            metrics.totalExecutions++;
            metrics.totalExecutionTime += executionTime;
            metrics.lastExecutionTime = executionTime;
            
            if (executionTime < metrics.minExecutionTime) {
                metrics.minExecutionTime = executionTime;
            }
            if (executionTime > metrics.maxExecutionTime) {
                metrics.maxExecutionTime = executionTime;
            }
            
            if (success) {
                metrics.successCount++;
            } else {
                metrics.failureCount++;
            }
            
            return metrics;
        });
    }

    /**
     * 获取操作类型的性能指标
     */
    public PerformanceMetrics getMetrics(String operationType) {
        return metrics.get(operationType);
    }

    /**
     * 获取所有性能指标
     */
    public ConcurrentHashMap<String, PerformanceMetrics> getAllMetrics() {
        return new ConcurrentHashMap<>(metrics);
    }

    /**
     * 获取总体统计信息
     */
    public String getOverallStatistics() {
        long totalProcessed = totalProcessedDocuments.get();
        long totalSuccess = totalSuccessfulExtractions.get();
        long totalFailed = totalFailedExtractions.get();
        
        double successRate = totalProcessed > 0 ? (double) totalSuccess / totalProcessed * 100 : 0;
        
        return String.format("总处理文档: %d, 成功: %d, 失败: %d, 成功率: %.2f%%",
                totalProcessed, totalSuccess, totalFailed, successRate);
    }

    /**
     * 获取详细的性能报告
     */
    public String getDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 页眉页码功能性能报告 ===\n");
        report.append(getOverallStatistics()).append("\n\n");
        
        metrics.forEach((operationType, metrics) -> {
            report.append(String.format("操作类型: %s\n", operationType));
            report.append(String.format("  总执行次数: %d\n", metrics.getTotalExecutions()));
            report.append(String.format("  成功次数: %d\n", metrics.getSuccessCount()));
            report.append(String.format("  失败次数: %d\n", metrics.getFailureCount()));
            report.append(String.format("  成功率: %.2f%%\n", metrics.getSuccessRate()));
            report.append(String.format("  平均执行时间: %.2fms\n", metrics.getAverageExecutionTime()));
            report.append(String.format("  最小执行时间: %dms\n", metrics.getMinExecutionTime()));
            report.append(String.format("  最大执行时间: %dms\n", metrics.getMaxExecutionTime()));
            report.append(String.format("  最近执行时间: %dms\n", metrics.getLastExecutionTime()));
            report.append("\n");
        });
        
        return report.toString();
    }

    /**
     * 重置所有统计信息
     */
    public void resetStatistics() {
        metrics.clear();
        totalProcessedDocuments.set(0);
        totalSuccessfulExtractions.set(0);
        totalFailedExtractions.set(0);
        log.info("性能统计信息已重置");
    }

    /**
     * 检查是否有性能问题
     */
    public boolean hasPerformanceIssues() {
        return metrics.values().stream().anyMatch(m -> 
            m.getAverageExecutionTime() > 10000 || // 平均执行时间超过10秒
            m.getSuccessRate() < 80 // 成功率低于80%
        );
    }

    /**
     * 获取性能警告信息
     */
    public String getPerformanceWarnings() {
        StringBuilder warnings = new StringBuilder();
        
        metrics.forEach((operationType, metrics) -> {
            if (metrics.getAverageExecutionTime() > 10000) {
                warnings.append(String.format("警告: %s 平均执行时间过长 (%.2fms)\n", 
                    operationType, metrics.getAverageExecutionTime()));
            }
            if (metrics.getSuccessRate() < 80) {
                warnings.append(String.format("警告: %s 成功率过低 (%.2f%%)\n", 
                    operationType, metrics.getSuccessRate()));
            }
        });
        
        return warnings.toString();
    }
}
