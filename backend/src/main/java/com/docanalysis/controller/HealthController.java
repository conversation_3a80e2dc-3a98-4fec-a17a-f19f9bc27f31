package com.docanalysis.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/health")
public class HealthController {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private WebClient webClient;

    /**
     * Basic health check endpoint
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("service", "Document Analysis Backend");
        health.put("version", "1.0.0");
        
        return ResponseEntity.ok(health);
    }

    /**
     * Detailed health check with dependencies
     */
    @GetMapping("/detailed")
    public ResponseEntity<Map<String, Object>> detailedHealth() {
        Map<String, Object> health = new HashMap<>();
        Map<String, Object> components = new HashMap<>();
        
        // Check database connection
        try (Connection connection = dataSource.getConnection()) {
            components.put("database", Map.of(
                "status", "UP",
                "details", "MySQL connection successful"
            ));
        } catch (Exception e) {
            components.put("database", Map.of(
                "status", "DOWN",
                "details", e.getMessage()
            ));
        }
        
        // Check WebClient configuration
        try {
            components.put("webclient", Map.of(
                "status", "UP",
                "details", "WebClient configured successfully"
            ));
        } catch (Exception e) {
            components.put("webclient", Map.of(
                "status", "DOWN",
                "details", e.getMessage()
            ));
        }
        
        health.put("status", "UP");
        health.put("components", components);
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(health);
    }
}
