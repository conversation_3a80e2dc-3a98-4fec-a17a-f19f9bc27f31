package com.docanalysis.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.docanalysis.dto.StructuredAnalysisResultDTO;
import com.docanalysis.entity.AnalysisResult;
import com.docanalysis.service.AnalysisContentParserService;
import com.docanalysis.service.AnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/analysis")
@RequiredArgsConstructor
public class AnalysisController {

    private final AnalysisService analysisService;
    private final AnalysisContentParserService contentParserService;

    /**
     * 获取结构化分析结果（新格式）
     */
    @GetMapping("/structured/{documentId}")
    public ResponseEntity<Map<String, Object>> getStructuredAnalysisResults(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();

        try {
            List<AnalysisResult> results = analysisService.getAnalysisResultsByDocumentId(documentId);

            if (results.isEmpty()) {
                response.put("success", false);
                response.put("message", "暂无分析结果，请先进行文档分析");
                response.put("code", 404);
                return ResponseEntity.ok(response);
            }

            // 解析为结构化格式
            StructuredAnalysisResultDTO structuredResult = contentParserService.parseToStructuredResult(documentId, results);

            response.put("success", true);
            response.put("data", structuredResult);
            response.put("message", "获取结构化分析结果成功");
            response.put("timestamp", System.currentTimeMillis());

            log.info("获取文档{}的结构化分析结果成功，提取项{}个，检测项{}个",
                documentId,
                structuredResult.getExtractItems().size(),
                structuredResult.getDetectItems().size());

        } catch (Exception e) {
            log.error("获取文档{}的结构化分析结果失败", documentId, e);
            response.put("success", false);
            response.put("message", "获取结构化分析结果失败: " + e.getMessage());
            response.put("code", 500);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }

        return ResponseEntity.ok(response);
    }

    /**
     * 获取文档的分析结果（原始格式）
     */
    @GetMapping("/document/{documentId}/raw")
    public ResponseEntity<Map<String, Object>> getDocumentAnalysisResultsRaw(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();

        try {
            List<AnalysisResult> results = analysisService.getAnalysisResultsByDocumentId(documentId);
            response.put("success", true);
            response.put("data", results);
            response.put("count", results.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取文档分析结果失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "获取分析结果失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取文档的结构化分析结果（用于前端渲染）
     */
    @GetMapping("/document/{documentId}")
    public ResponseEntity<Map<String, Object>> getDocumentAnalysisResults(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("获取文档结构化分析结果: documentId={}", documentId);

            // 获取原始分析结果
            List<AnalysisResult> results = analysisService.getAnalysisResultsByDocumentId(documentId);

            if (results.isEmpty()) {
                log.warn("文档没有分析结果: documentId={}", documentId);
                response.put("success", true);
                response.put("data", contentParserService.parseToStructuredResult(documentId, results));
                response.put("message", "文档暂无分析结果");
                return ResponseEntity.ok(response);
            }

            // 解析为结构化数据
            StructuredAnalysisResultDTO structuredResult = contentParserService.parseToStructuredResult(documentId, results);

            response.put("success", true);
            response.put("data", structuredResult);
            response.put("message", "获取分析结果成功");

            log.info("结构化分析结果获取成功: documentId={}, 提取项={}, 检测项={}",
                    documentId,
                    structuredResult.getExtractItems().size(),
                    structuredResult.getDetectItems().size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取文档结构化分析结果失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "获取分析结果失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取特定类型的分析结果
     */
    @GetMapping("/document/{documentId}/{agentType}")
    public ResponseEntity<Map<String, Object>> getAnalysisResult(
            @PathVariable Long documentId,
            @PathVariable String agentType) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            AnalysisResult.AgentType type = AnalysisResult.AgentType.valueOf(agentType.toUpperCase());
            AnalysisResult result = analysisService.getAnalysisResult(documentId, type);
            
            if (result == null) {
                response.put("success", false);
                response.put("message", "分析结果不存在");
                return ResponseEntity.notFound().build();
            }
            
            response.put("success", true);
            response.put("data", result);
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", "无效的智能体类型: " + agentType);
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            log.error("获取分析结果失败: documentId={}, agentType={}", documentId, agentType, e);
            response.put("success", false);
            response.put("message", "获取分析结果失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 分页查询分析结果
     */
    @GetMapping("/page")
    public ResponseEntity<Map<String, Object>> getAnalysisResultsPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long documentId,
            @RequestParam(required = false) String agentType,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Page<AnalysisResult> pageParam = new Page<>(page, size);
            AnalysisResult.AgentType type = agentType != null ? 
                    AnalysisResult.AgentType.valueOf(agentType.toUpperCase()) : null;
            
            IPage<AnalysisResult> results = analysisService.getAnalysisResultsPage(
                    pageParam, documentId, type, startTime, endTime);
            
            response.put("success", true);
            response.put("data", results.getRecords());
            response.put("total", results.getTotal());
            response.put("page", results.getCurrent());
            response.put("size", results.getSize());
            response.put("pages", results.getPages());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("分页查询分析结果失败", e);
            response.put("success", false);
            response.put("message", "查询失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取分析统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getAnalysisStatistics(
            @RequestParam(required = false) Long documentId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> statistics = analysisService.getAnalysisStatistics(documentId, startTime, endTime);
            
            response.put("success", true);
            response.put("data", statistics);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取分析统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除文档的分析结果
     */
    @DeleteMapping("/document/{documentId}")
    public ResponseEntity<Map<String, Object>> deleteDocumentAnalysisResults(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            int deletedCount = analysisService.deleteAnalysisResultsByDocumentId(documentId);
            
            response.put("success", true);
            response.put("message", "分析结果删除成功");
            response.put("deletedCount", deletedCount);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("删除分析结果失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 清理过期的分析结果
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupExpiredResults(
            @RequestParam(defaultValue = "30") Integer retentionDays) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            int cleanedCount = analysisService.cleanupExpiredResults(retentionDays);
            
            response.put("success", true);
            response.put("message", "过期结果清理完成");
            response.put("cleanedCount", cleanedCount);
            response.put("retentionDays", retentionDays);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("清理过期结果失败", e);
            response.put("success", false);
            response.put("message", "清理失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 检查分析结果是否存在
     */
    @GetMapping("/exists/{documentId}/{agentType}")
    public ResponseEntity<Map<String, Object>> checkAnalysisResultExists(
            @PathVariable Long documentId,
            @PathVariable String agentType) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            AnalysisResult.AgentType type = AnalysisResult.AgentType.valueOf(agentType.toUpperCase());
            boolean exists = analysisService.existsAnalysisResult(documentId, type);
            
            response.put("success", true);
            response.put("exists", exists);
            response.put("documentId", documentId);
            response.put("agentType", agentType);
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            response.put("success", false);
            response.put("message", "无效的智能体类型: " + agentType);
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            log.error("检查分析结果存在性失败: documentId={}, agentType={}", documentId, agentType, e);
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取最近的分析结果
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentAnalysisResults(
            @RequestParam(defaultValue = "10") Integer limit) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<AnalysisResult> results = analysisService.getRecentAnalysisResults(limit);
            
            response.put("success", true);
            response.put("data", results);
            response.put("count", results.size());
            response.put("limit", limit);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取最近分析结果失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
