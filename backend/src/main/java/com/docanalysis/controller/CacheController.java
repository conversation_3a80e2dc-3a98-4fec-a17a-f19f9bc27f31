package com.docanalysis.controller;

import com.docanalysis.config.CacheConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/cache")
@RequiredArgsConstructor
public class CacheController {

    private final CacheConfig cacheConfig;
    private final CacheManager cacheManager;

    /**
     * 获取缓存状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getCacheStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> cacheStatus = new HashMap<>();
            
            // 获取所有缓存名称
            cacheStatus.put("cacheNames", cacheManager.getCacheNames());
            cacheStatus.put("cacheManagerType", cacheManager.getClass().getSimpleName());
            
            // 检查每个缓存的状态
            Map<String, Object> cacheDetails = new HashMap<>();
            cacheManager.getCacheNames().forEach(cacheName -> {
                var cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    Map<String, Object> details = new HashMap<>();
                    details.put("exists", true);
                    details.put("nativeCache", cache.getNativeCache().getClass().getSimpleName());
                    cacheDetails.put(cacheName, details);
                } else {
                    cacheDetails.put(cacheName, Map.of("exists", false));
                }
            });
            cacheStatus.put("cacheDetails", cacheDetails);
            
            response.put("success", true);
            response.put("data", cacheStatus);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取缓存状态失败", e);
            response.put("success", false);
            response.put("message", "获取缓存状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 清除指定文档的缓存
     */
    @DeleteMapping("/document/{documentId}")
    public ResponseEntity<Map<String, Object>> clearDocumentCache(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            cacheConfig.clearDocumentCaches(documentId);
            
            response.put("success", true);
            response.put("message", "文档缓存清除成功");
            response.put("documentId", documentId);
            
            log.info("文档缓存清除成功: documentId={}", documentId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("清除文档缓存失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "清除文档缓存失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 清除所有缓存
     */
    @DeleteMapping("/all")
    public ResponseEntity<Map<String, Object>> clearAllCaches() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            cacheConfig.clearAllCaches();
            
            response.put("success", true);
            response.put("message", "所有缓存清除成功");
            
            log.info("所有缓存清除成功");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("清除所有缓存失败", e);
            response.put("success", false);
            response.put("message", "清除所有缓存失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 清除指定名称的缓存
     */
    @DeleteMapping("/{cacheName}")
    public ResponseEntity<Map<String, Object>> clearCache(@PathVariable String cacheName) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            var cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                response.put("success", true);
                response.put("message", "缓存清除成功");
                response.put("cacheName", cacheName);
                
                log.info("缓存清除成功: cacheName={}", cacheName);
            } else {
                response.put("success", false);
                response.put("message", "缓存不存在: " + cacheName);
                
                log.warn("尝试清除不存在的缓存: cacheName={}", cacheName);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("清除缓存失败: cacheName={}", cacheName, e);
            response.put("success", false);
            response.put("message", "清除缓存失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 检查指定缓存项是否存在
     */
    @GetMapping("/{cacheName}/key/{key}")
    public ResponseEntity<Map<String, Object>> checkCacheKey(
            @PathVariable String cacheName, 
            @PathVariable String key) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            var cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                var value = cache.get(key);
                
                Map<String, Object> keyInfo = new HashMap<>();
                keyInfo.put("exists", value != null);
                keyInfo.put("hasValue", value != null && value.get() != null);
                keyInfo.put("valueType", value != null && value.get() != null ? 
                    value.get().getClass().getSimpleName() : null);
                
                response.put("success", true);
                response.put("data", keyInfo);
                response.put("cacheName", cacheName);
                response.put("key", key);
            } else {
                response.put("success", false);
                response.put("message", "缓存不存在: " + cacheName);
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检查缓存键失败: cacheName={}, key={}", cacheName, key, e);
            response.put("success", false);
            response.put("message", "检查缓存键失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 预热缓存（测试用）
     */
    @PostMapping("/warmup")
    public ResponseEntity<Map<String, Object>> warmupCache() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这里可以添加缓存预热逻辑
            // 例如预加载常用的文档内容等
            
            response.put("success", true);
            response.put("message", "缓存预热完成");
            
            log.info("缓存预热完成");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("缓存预热失败", e);
            response.put("success", false);
            response.put("message", "缓存预热失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
