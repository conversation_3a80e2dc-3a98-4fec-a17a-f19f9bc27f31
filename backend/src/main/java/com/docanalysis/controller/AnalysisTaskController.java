package com.docanalysis.controller;

import com.docanalysis.entity.AnalysisTask;
import com.docanalysis.service.AnalysisTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("/analysis-tasks")
@RequiredArgsConstructor
public class AnalysisTaskController {

    private final AnalysisTaskService analysisTaskService;

    /**
     * 获取文档的分析任务进度
     */
    @GetMapping("/document/{documentId}/progress")
    public ResponseEntity<Map<String, Object>> getDocumentAnalysisProgress(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 获取任务详细进度信息
            List<Map<String, Object>> taskDetails = analysisTaskService.getTaskProgressDetails(documentId);
            
            // 获取整体状态
            Map<String, Object> overallStatus = analysisTaskService.getDocumentOverallStatus(documentId);
            
            // 获取任务统计
            Map<String, Object> statistics = analysisTaskService.getTaskStatistics(documentId);
            
            response.put("success", true);
            response.put("data", Map.of(
                "taskDetails", taskDetails,
                "overallStatus", overallStatus,
                "statistics", statistics
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取文档分析进度失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "获取分析进度失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取文档的整体分析状态
     */
    @GetMapping("/document/{documentId}/status")
    public ResponseEntity<Map<String, Object>> getDocumentAnalysisStatus(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> overallStatus = analysisTaskService.getDocumentOverallStatus(documentId);
            
            response.put("success", true);
            response.put("data", overallStatus);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取文档分析状态失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "获取分析状态失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取所有分析任务列表
     */
    @GetMapping("/document/{documentId}")
    public ResponseEntity<Map<String, Object>> getDocumentTasks(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<AnalysisTask> tasks = analysisTaskService.getTasksByDocumentId(documentId);
            
            response.put("success", true);
            response.put("data", tasks);
            response.put("count", tasks.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取文档任务列表失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "获取任务列表失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 重试失败的任务
     */
    @PostMapping("/{taskId}/retry")
    public ResponseEntity<Map<String, Object>> retryTask(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            analysisTaskService.retryTask(taskId);
            
            response.put("success", true);
            response.put("message", "任务重试已启动");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("重试任务失败: taskId={}", taskId, e);
            response.put("success", false);
            response.put("message", "重试任务失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 取消任务
     */
    @PostMapping("/{taskId}/cancel")
    public ResponseEntity<Map<String, Object>> cancelTask(@PathVariable Long taskId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            analysisTaskService.cancelTask(taskId);
            
            response.put("success", true);
            response.put("message", "任务已取消");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("取消任务失败: taskId={}", taskId, e);
            response.put("success", false);
            response.put("message", "取消任务失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取系统任务执行摘要
     */
    @GetMapping("/summary")
    public ResponseEntity<Map<String, Object>> getTaskExecutionSummary() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Map<String, Object> summary = analysisTaskService.getTaskExecutionSummary();
            
            response.put("success", true);
            response.put("data", summary);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取任务执行摘要失败", e);
            response.put("success", false);
            response.put("message", "获取任务摘要失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取最近的任务列表（用于监控）
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentTasks(
            @RequestParam(defaultValue = "24") int hours,
            @RequestParam(defaultValue = "20") int limit) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            List<Map<String, Object>> recentTasks = analysisTaskService.getRecentTasks(hours, limit);
            
            response.put("success", true);
            response.put("data", recentTasks);
            response.put("count", recentTasks.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取最近任务列表失败", e);
            response.put("success", false);
            response.put("message", "获取最近任务失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 手动触发文档分析
     */
    @PostMapping("/document/{documentId}/analyze")
    public ResponseEntity<Map<String, Object>> triggerDocumentAnalysis(
            @PathVariable Long documentId,
            @RequestParam String filePath) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查是否已有活跃任务
            if (analysisTaskService.hasActiveTasksForDocument(documentId)) {
                response.put("success", false);
                response.put("message", "文档已有进行中的分析任务");
                return ResponseEntity.badRequest().body(response);
            }
            
            // 启动分析任务
            analysisTaskService.startAllAnalysisTasksAsync(documentId, filePath);
            
            response.put("success", true);
            response.put("message", "分析任务已启动");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("触发文档分析失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "触发分析失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 清理过期任务
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupExpiredTasks(
            @RequestParam(defaultValue = "30") int days) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            int cleanedCount = analysisTaskService.cleanupExpiredTasks(days);
            
            response.put("success", true);
            response.put("message", "清理完成");
            response.put("cleanedCount", cleanedCount);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("清理过期任务失败", e);
            response.put("success", false);
            response.put("message", "清理失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
