package com.docanalysis.controller;

import com.docanalysis.dto.MaxKBResponseDTO;
import com.docanalysis.service.MaxKBService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Slf4j
@RestController
@RequestMapping("/maxkb")
@RequiredArgsConstructor
public class MaxKBController {

    private final MaxKBService maxKBService;

    /**
     * 分析文档
     */
    @PostMapping("/analyze/{documentId}")
    public ResponseEntity<Map<String, Object>> analyzeDocument(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("收到文档分析请求: documentId={}", documentId);
            
            CompletableFuture<MaxKBResponseDTO> analysisTask = maxKBService.analyzeDocument(documentId);
            
            response.put("success", true);
            response.put("message", "文档分析已开始");
            response.put("documentId", documentId);
            response.put("status", "PROCESSING");
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.warn("文档分析参数错误: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            log.error("文档分析启动失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "文档分析启动失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取分析状态
     */
    @GetMapping("/status/{documentId}")
    public ResponseEntity<Map<String, Object>> getAnalysisStatus(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String status = maxKBService.getAnalysisStatus(documentId);
            
            Map<String, Object> statusData = new HashMap<>();
            statusData.put("documentId", documentId);
            statusData.put("status", status);
            statusData.put("isProcessing", "PROCESSING".equals(status));
            statusData.put("isCompleted", "SUCCESS".equals(status) || "PARTIAL_SUCCESS".equals(status) || "FAILED".equals(status));
            
            response.put("success", true);
            response.put("data", statusData);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取分析状态失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "获取分析状态失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取分析结果
     */
    @GetMapping("/result/{documentId}")
    public ResponseEntity<Map<String, Object>> getAnalysisResult(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            MaxKBResponseDTO result = maxKBService.getAnalysisResult(documentId);
            
            if (result == null) {
                response.put("success", false);
                response.put("message", "分析结果不存在，请先启动分析");
                return ResponseEntity.notFound().build();
            }
            
            response.put("success", true);
            response.put("data", result);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取分析结果失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "获取分析结果失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 重新分析文档
     */
    @PostMapping("/reanalyze/{documentId}")
    public ResponseEntity<Map<String, Object>> reanalyzeDocument(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("收到重新分析请求: documentId={}", documentId);
            
            CompletableFuture<MaxKBResponseDTO> analysisTask = maxKBService.reanalyzeDocument(documentId);
            
            response.put("success", true);
            response.put("message", "文档重新分析已开始");
            response.put("documentId", documentId);
            response.put("status", "PROCESSING");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("重新分析启动失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "重新分析启动失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 取消分析
     */
    @PostMapping("/cancel/{documentId}")
    public ResponseEntity<Map<String, Object>> cancelAnalysis(@PathVariable Long documentId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean cancelled = maxKBService.cancelAnalysis(documentId);
            
            if (cancelled) {
                response.put("success", true);
                response.put("message", "分析已取消");
            } else {
                response.put("success", false);
                response.put("message", "没有正在进行的分析任务");
            }
            
            response.put("documentId", documentId);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("取消分析失败: documentId={}", documentId, e);
            response.put("success", false);
            response.put("message", "取消分析失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 批量分析文档
     */
    @PostMapping("/batch-analyze")
    public ResponseEntity<Map<String, Object>> batchAnalyzeDocuments(@RequestBody Map<String, Object> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            java.util.List<Long> documentIds = (java.util.List<Long>) request.get("documentIds");
            
            if (documentIds == null || documentIds.isEmpty()) {
                response.put("success", false);
                response.put("message", "文档ID列表不能为空");
                return ResponseEntity.badRequest().body(response);
            }
            
            log.info("收到批量分析请求: documentIds={}", documentIds);
            
            java.util.List<CompletableFuture<MaxKBResponseDTO>> tasks = new java.util.ArrayList<>();
            for (Long documentId : documentIds) {
                tasks.add(maxKBService.analyzeDocument(documentId));
            }
            
            response.put("success", true);
            response.put("message", "批量分析已开始");
            response.put("documentIds", documentIds);
            response.put("taskCount", tasks.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("批量分析启动失败", e);
            response.put("success", false);
            response.put("message", "批量分析启动失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取系统状态
     */
    @GetMapping("/system/status")
    public ResponseEntity<Map<String, Object>> getSystemStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // TODO: 实现系统状态检查
            Map<String, Object> systemStatus = new HashMap<>();
            systemStatus.put("maxkbApiStatus", "AVAILABLE");
            systemStatus.put("activeAnalyses", 0); // 实际应该从服务中获取
            systemStatus.put("totalAnalysesToday", 0); // 实际应该从数据库统计
            systemStatus.put("successRate", "95%"); // 实际应该计算
            
            response.put("success", true);
            response.put("data", systemStatus);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            response.put("success", false);
            response.put("message", "获取系统状态失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }


    @GetMapping("/qa/token")
    public ResponseEntity<String> getMaxKbToken() {
        String response = maxKBService.getMaxKbToken();
        return ResponseEntity.ok(response);
    }

    @GetMapping("/qa/appId")
    public ResponseEntity<String> getQaAppId() {
        String response = maxKBService.getQaAppId();
        return ResponseEntity.ok(response);
    }
}
