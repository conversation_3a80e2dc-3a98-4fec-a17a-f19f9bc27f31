package com.docanalysis.controller;

import com.docanalysis.config.ConfigConstants;
import com.docanalysis.dto.DocumentContentDTO;
import com.docanalysis.dto.DocumentUploadDTO;
import com.docanalysis.dto.FileAttribute;
import com.docanalysis.entity.Document;
import com.docanalysis.service.AnalysisTaskService;
import com.docanalysis.service.DocumentService;
import com.docanalysis.service.FileHandlerService;
import com.docanalysis.service.FilePreview;
import com.docanalysis.util.FileUploadUtil;
import com.docanalysis.util.KkFileUtils;
import com.docanalysis.util.WebUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.http.CacheControl;
import org.springframework.ui.Model;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;


import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mysql.cj.conf.PropertyKey.logger;


@Slf4j
@RestController
@RequestMapping("/documents")
@RequiredArgsConstructor
public class DocumentController {

    private final DocumentService documentService;
    private final FileUploadUtil fileUploadUtil;
    private final AnalysisTaskService analysisTaskService;

    private final String fileDir = ConfigConstants.getFileDir();

    private final FileHandlerService fileHandlerService;

    private final FilePreview filePreview;

    private final String demoDir = "demo";

    private final String demoPath = demoDir + File.separator;

    /**
     * 上传文档
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadDocument(
            @RequestParam("file") MultipartFile file) {

        Map<String, Object> response = new HashMap<>();

        ResponseEntity<Object> checkResult = this.fileUploadCheck(file);
        try {
            log.info("开始处理文档上传: {}", file.getOriginalFilename());


            File outFile = new File(fileDir + demoPath);
            if (!outFile.exists() && !outFile.mkdirs()) {
                log.error("创建文件夹【{}】失败，请检查目录权限！", fileDir + demoPath);
            }
            String fileName = checkResult.getBody().toString();
            String filePath = fileDir + demoPath + fileName;
            try (InputStream in = file.getInputStream(); OutputStream out = Files.newOutputStream(Paths.get(fileDir + demoPath + fileName))) {
                StreamUtils.copy(in, out);
            } catch (IOException e) {
                log.error("文件上传失败", e);
            }
            FileAttribute fileAttribute = fileHandlerService.getFileAttributeFromLocalFile(filePath);  //这里不在进行URL 处理了

            String s = filePreview.filePreviewHandle(filePath, null, fileAttribute);
            // 6. 创建文档记录
            Document document = documentService.createDocument(
                    fileName,
                    file.getOriginalFilename(),
                    filePath,
                    file.getSize(),
                    fileAttribute.getName(),
                    fileAttribute.getOutFilePath()
            );

            if (document == null) {
                throw new RuntimeException("创建文档记录失败");
            }
            // 8. 构建响应数据
            DocumentUploadDTO uploadDTO = new DocumentUploadDTO()
                    .setId(document.getId())
                    .setFilename(fileAttribute.getName())
                    .setOriginalName(file.getOriginalFilename())
                    .setFilePath(fileAttribute.getOutFilePath())
                    .setFileSize(file.getSize())
                    .setContentType(fileAttribute.getOfficePreviewType())
                    .setUploadTimestamp(System.currentTimeMillis())
                    .setUploadSuccess();

            response.put("success", true);
            response.put("message", "文档上传成功，分析任务已启动");
            response.put("data", uploadDTO);

            log.info("文档上传成功: documentId={}, filename={}", document.getId(), fileName);

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.warn("文档上传验证失败: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            response.put("errorCode", "VALIDATION_ERROR");
            return ResponseEntity.badRequest().body(response);

        } catch (Exception e) {
            log.error("文档上传失败", e);
            response.put("success", false);
            response.put("message", "文档上传失败: " + e.getMessage());
            response.put("errorCode", "UPLOAD_ERROR");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取文档列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getDocuments(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {

        Map<String, Object> response = new HashMap<>();

        try {
            // TODO: 实现分页查询逻辑
            List<Document> documents = documentService.getRecentDocuments(size);

            response.put("success", true);
            response.put("data", documents);
            response.put("total", documents.size());
            response.put("page", page);
            response.put("size", size);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取文档列表失败", e);
            response.put("success", false);
            response.put("message", "获取文档列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取文档详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getDocument(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            Document document = documentService.getById(id);

            if (document == null) {
                response.put("success", false);
                response.put("message", "文档不存在");
                return ResponseEntity.notFound().build();
            }

            response.put("success", true);
            response.put("data", document);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取文档详情失败: documentId={}", id, e);
            response.put("success", false);
            response.put("message", "获取文档详情失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 删除文档
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteDocument(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            Document document = documentService.getById(id);

            if (document == null) {
                response.put("success", false);
                response.put("message", "文档不存在");
                return ResponseEntity.notFound().build();
            }

            // 删除物理文件
            if (document.getFilePath() != null) {
                fileUploadUtil.deleteFile(document.getFilePath());
            }

            // 删除数据库记录
            boolean deleted = documentService.deleteDocumentCompletely(id);

            if (deleted) {
                response.put("success", true);
                response.put("message", "文档删除成功");
            } else {
                response.put("success", false);
                response.put("message", "文档删除失败");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("删除文档失败: documentId={}", id, e);
            response.put("success", false);
            response.put("message", "删除文档失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取上传配置信息
     */
    @GetMapping("/upload-config")
    public ResponseEntity<Map<String, Object>> getUploadConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("maxFileSize", 52428800); // 50MB
        config.put("allowedTypes", List.of("application/vnd.openxmlformats-officedocument.wordprocessingml.document"));
        config.put("allowedExtensions", List.of(".docx"));
        config.put("maxFileSizeText", "50MB");

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", config);

        return ResponseEntity.ok(response);
    }

    /**
     * 解析文档内容
     */
    @PostMapping("/{id}/parse")
    public ResponseEntity<Map<String, Object>> parseDocument(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            DocumentContentDTO contentDTO = documentService.parseDocumentContent(id);

            response.put("success", true);
            response.put("message", "文档解析成功");
            response.put("data", contentDTO);

            return ResponseEntity.ok(response);

        } catch (IllegalArgumentException e) {
            log.warn("文档解析参数错误: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);

        } catch (Exception e) {
            log.error("文档解析失败: documentId={}", id, e);
            response.put("success", false);
            response.put("message", "文档解析失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 获取文档内容
     */
    @GetMapping("/{id}/content")
    public ResponseEntity<Map<String, Object>> getDocumentContent(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            DocumentContentDTO contentDTO = documentService.getDocumentContent(id);

            if (contentDTO == null || !contentDTO.isParseSuccessful()) {
                response.put("success", false);
                response.put("message", "文档内容不可用，请先解析文档");
                return ResponseEntity.notFound().build();
            }

            response.put("success", true);
            response.put("data", contentDTO);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取文档内容失败: documentId={}", id, e);
            response.put("success", false);
            response.put("message", "获取文档内容失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 重新解析文档
     */
    @PostMapping("/{id}/reparse")
    public ResponseEntity<Map<String, Object>> reparseDocument(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            DocumentContentDTO contentDTO = documentService.reparseDocument(id);

            response.put("success", true);
            response.put("message", "文档重新解析成功");
            response.put("data", contentDTO);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("重新解析文档失败: documentId={}", id, e);
            response.put("success", false);
            response.put("message", "重新解析文档失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * 检查文档解析状态
     */
    @GetMapping("/{id}/parse-status")
    public ResponseEntity<Map<String, Object>> getParseStatus(@PathVariable Long id) {
        Map<String, Object> response = new HashMap<>();

        try {
            Document document = documentService.getById(id);

            if (document == null) {
                response.put("success", false);
                response.put("message", "文档不存在");
                return ResponseEntity.notFound().build();
            }

            Map<String, Object> statusData = new HashMap<>();
            statusData.put("documentId", id);
            statusData.put("status", document.getStatus().getCode());
            statusData.put("statusText", document.getStatus().getDescription());
            statusData.put("isParsed", documentService.isDocumentParsed(id));
            statusData.put("canParse", document.getStatus() == Document.DocumentStatus.UPLOADED ||
                    document.getStatus() == Document.DocumentStatus.FAILED);

            response.put("success", true);
            response.put("data", statusData);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("获取解析状态失败: documentId={}", id, e);
            response.put("success", false);
            response.put("message", "获取解析状态失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }


    @GetMapping("/{id}/pdf")
    public ResponseEntity<byte[]> getPdfFile(@PathVariable Long id) {
        try {
            log.info("获取PDF文件: documentId={}", id);

            // 1. 获取文档对象
            Document document = documentService.getById(id);
            if (document == null) {
                log.warn("文档不存在: documentId={}", id);
                return ResponseEntity.notFound().build();
            }

            // 2. 检查文档状态
//            if (!document.isProcessedSuccessfully()) {
//                log.warn("文档未处理完成: documentId={}, status={}", id, document.getStatus());
//                return ResponseEntity.badRequest().build();
//            }

            // 3. 获取PDF文件路径
            String filePath = document.getPdfFilePath();
            if (filePath == null || filePath.trim().isEmpty()) {
                log.error("文档文件路径为空: documentId={}", id);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }

            // 4. 安全检查 - 复用现有安全检查机制
            if (KkFileUtils.isIllegalFileName(filePath)) {
                log.warn("非法文件路径访问: documentId={}, filePath={}", id, filePath);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // 5. 检查文件存在性
            File pdfFile = new File(filePath);
            if (!pdfFile.exists() || !pdfFile.isFile()) {
                log.error("PDF文件不存在: documentId={}, filePath={}", id, filePath);
                return ResponseEntity.notFound().build();
            }

            // 6. 读取文件内容为字节数组
            byte[] pdfBytes;
            try {
                pdfBytes = Files.readAllBytes(pdfFile.toPath());
            } catch (IOException e) {
                log.error("读取PDF文件失败: documentId={}, filePath={}", id, filePath, e);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }

            // 7. 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentLength(pdfBytes.length);

            // 设置文件名，支持中文文件名
            String fileName = document.getOriginalName();
            if (fileName == null || fileName.trim().isEmpty()) {
                fileName = "document_" + id;
            }
            if (!fileName.toLowerCase().endsWith(".pdf")) {
                fileName += ".pdf";
            }

            // 使用 attachment 让浏览器下载，或使用 inline 让浏览器直接显示
            headers.setContentDispositionFormData("inline", fileName);

            // 添加缓存控制头
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);

            log.info("PDF文件读取完成: documentId={}, fileSize={} bytes", id, pdfBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);

        } catch (Exception e) {
            log.error("获取PDF文件失败: documentId={}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }



    /**
     * 上传文件前校验
     *
     * @param file 文件
     * @return 校验结果
     */
    private ResponseEntity<Object> fileUploadCheck(MultipartFile file) {
        if (ConfigConstants.getFileUploadDisable()) {
            return ResponseEntity.status(500).body("文件传接口已禁用");
        }
        String fileName = WebUtils.getFileNameFromMultipartFile(file);
        if (fileName.lastIndexOf(".") == -1) {
            return ResponseEntity.status(500).body("不允许上传的类型");
        }
        if (!KkFileUtils.isAllowedUpload(fileName)) {
            return ResponseEntity.status(500).body("不允许上传的文件类型: " + fileName);
        }
        if (KkFileUtils.isIllegalFileName(fileName)) {
            return ResponseEntity.status(500).body("不允许上传的文件名: " + fileName);
        }
        // 判断是否存在同名文件
//        if (existsFile(fileName)) {
//            return ResponseEntity.status(500).body("存在同名文件，请先删除原有文件再次上传");
//        }
        return ResponseEntity.ok(fileName);
    }

    private boolean existsFile(String fileName) {
        File file = new File(fileDir + demoPath + fileName);
        return file.exists();
    }
}
