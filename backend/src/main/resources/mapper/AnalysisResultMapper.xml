<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.docanalysis.mapper.AnalysisResultMapper">

    <!-- 结果映射，明确指定JSON字段的类型处理器 -->
    <resultMap id="AnalysisResultMap" type="com.docanalysis.entity.AnalysisResult">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="document_id" property="documentId" jdbcType="BIGINT"/>
        <result column="agent_type" property="agentType" jdbcType="VARCHAR"/>
        <result column="result_content" property="resultContent" 
                jdbcType="VARCHAR" 
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

    </resultMap>

    <!-- 根据文档ID查询分析结果 -->
    <select id="selectByDocumentId" resultMap="AnalysisResultMap">
        SELECT document_id,result_content,agent_type FROM analysis_results
        WHERE document_id = #{documentId} 
        ORDER BY created_time DESC
    </select>

    <!-- 根据文档ID和智能体类型查询分析结果 -->
    <select id="selectByDocumentIdAndAgentType" resultMap="AnalysisResultMap">
        SELECT * FROM analysis_results 
        WHERE document_id = #{documentId} 
        AND agent_type = #{agentType}
        ORDER BY created_time DESC 
        LIMIT 1
    </select>

    <!-- 测试查询，用于调试JSON字段 -->
    <select id="selectWithRawJson" resultType="java.util.Map">
        SELECT id, document_id, agent_type, 
               result_content as resultContentRaw,
               CAST(result_content AS CHAR) as resultContentString,
               JSON_VALID(result_content) as isValidJson,
               created_time
        FROM analysis_results 
        WHERE document_id = #{documentId}
        ORDER BY created_time DESC
    </select>

</mapper>
