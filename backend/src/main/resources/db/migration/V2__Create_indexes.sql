-- Document Intelligence Analysis System Database Indexes
-- Version: 1.0.0
-- Created: 2025-08-07


USE doc_analysis;

-- ================================
-- documents表索引优化
-- ================================

-- 上传时间索引：用于按时间范围查询文档
CREATE INDEX idx_documents_upload_time ON documents(upload_time);

-- 状态索引：用于按状态筛选文档
CREATE INDEX idx_documents_status ON documents(status);

-- 文件大小索引：用于按文件大小范围查询
CREATE INDEX idx_documents_file_size ON documents(file_size);

-- 复合索引：状态+上传时间，用于分页查询最新的特定状态文档
CREATE INDEX idx_documents_status_upload_time ON documents(status, upload_time DESC);

-- 原始文件名索引：用于文件名搜索（支持前缀匹配）
CREATE INDEX idx_documents_original_name ON documents(original_name);

-- 创建时间和更新时间索引
CREATE INDEX idx_documents_created_time ON documents(created_time);
CREATE INDEX idx_documents_updated_time ON documents(updated_time);

-- ================================
-- analysis_results表索引优化
-- ================================

-- 文档ID索引：用于查询特定文档的所有分析结果
CREATE INDEX idx_analysis_document_id ON analysis_results(document_id);

-- 智能体类型索引：用于按类型筛选分析结果
CREATE INDEX idx_analysis_agent_type ON analysis_results(agent_type);

-- 复合索引：文档ID+智能体类型，用于查询特定文档的特定类型分析结果
CREATE INDEX idx_analysis_document_agent ON analysis_results(document_id, agent_type);

-- 创建时间索引：用于按时间排序分析结果
CREATE INDEX idx_analysis_created_time ON analysis_results(created_time);

-- 复合索引：文档ID+创建时间，用于查询特定文档按时间排序的分析结果
CREATE INDEX idx_analysis_document_time ON analysis_results(document_id, created_time DESC);

-- API响应状态码索引：用于监控API调用成功率
CREATE INDEX idx_analysis_response_code ON analysis_results(api_response_code);

-- 处理时间索引：用于性能分析
CREATE INDEX idx_analysis_processing_time ON analysis_results(processing_time);

-- JSON字段索引：为result_content中的常用字段创建虚拟列索引
-- 注意：这些索引需要根据实际的JSON结构调整
ALTER TABLE analysis_results 
ADD COLUMN result_status VARCHAR(20) GENERATED ALWAYS AS (JSON_UNQUOTE(JSON_EXTRACT(result_content, '$.status'))) VIRTUAL,
ADD INDEX idx_analysis_result_status (result_status);

-- ================================
-- user_interactions表索引优化
-- ================================

-- 文档ID索引：用于查询特定文档的用户交互记录
CREATE INDEX idx_interactions_document_id ON user_interactions(document_id);

-- 交互类型索引：用于按交互类型统计
CREATE INDEX idx_interactions_type ON user_interactions(interaction_type);

-- 复合索引：文档ID+创建时间，用于查询特定文档按时间排序的交互记录
CREATE INDEX idx_interactions_document_time ON user_interactions(document_id, created_time DESC);

-- 会话ID索引：用于追踪用户会话
CREATE INDEX idx_interactions_session_id ON user_interactions(session_id);

-- 创建时间索引：用于按时间范围查询交互记录
CREATE INDEX idx_interactions_created_time ON user_interactions(created_time);

-- IP地址索引：用于用户行为分析
CREATE INDEX idx_interactions_ip_address ON user_interactions(ip_address);

-- 复合索引：交互类型+创建时间，用于统计特定时间段的交互类型分布
CREATE INDEX idx_interactions_type_time ON user_interactions(interaction_type, created_time DESC);

-- ================================
-- system_config表索引优化
-- ================================

-- 配置键唯一索引（已在表创建时定义）
-- 配置类型索引：用于按类型查询配置
CREATE INDEX idx_config_type ON system_config(config_type);

-- 是否启用索引：用于查询启用的配置
CREATE INDEX idx_config_active ON system_config(is_active);

-- 复合索引：是否启用+配置类型
CREATE INDEX idx_config_active_type ON system_config(is_active, config_type);

-- ================================
-- operation_logs表索引优化
-- ================================
-- 注意：operation_logs表的基础索引已在V1脚本中创建

-- 复合索引：操作类型+状态+创建时间，用于监控特定操作的成功率
CREATE INDEX idx_logs_type_status_time ON operation_logs(operation_type, status, created_time DESC);

-- 复合索引：文档ID+操作类型，用于追踪特定文档的操作历史
CREATE INDEX idx_logs_document_type ON operation_logs(document_id, operation_type);

-- 执行时间索引：用于性能分析
CREATE INDEX idx_logs_execution_time ON operation_logs(execution_time);

-- 复合索引：状态+创建时间，用于查询特定时间段的失败操作
CREATE INDEX idx_logs_status_time ON operation_logs(status, created_time DESC);

-- ================================
-- 分区表优化（可选）
-- ================================

-- 为operation_logs表创建按月分区（MySQL 8.0支持）
-- 注意：这需要在表创建时定义，这里仅作为示例说明
-- ALTER TABLE operation_logs 
-- PARTITION BY RANGE (YEAR(created_time) * 100 + MONTH(created_time)) (
--     PARTITION p202501 VALUES LESS THAN (202502),
--     PARTITION p202502 VALUES LESS THAN (202503),
--     PARTITION p202503 VALUES LESS THAN (202504),
--     PARTITION p202504 VALUES LESS THAN (202505),
--     PARTITION p202505 VALUES LESS THAN (202506),
--     PARTITION p202506 VALUES LESS THAN (202507),
--     PARTITION p202507 VALUES LESS THAN (202508),
--     PARTITION p202508 VALUES LESS THAN (202509),
--     PARTITION p202509 VALUES LESS THAN (202510),
--     PARTITION p202510 VALUES LESS THAN (202511),
--     PARTITION p202511 VALUES LESS THAN (202512),
--     PARTITION p202512 VALUES LESS THAN (202601),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ================================
-- 索引使用情况监控
-- ================================

-- 创建视图用于监控索引使用情况
CREATE VIEW v_index_usage AS
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    CASE 
        WHEN NON_UNIQUE = 0 THEN 'UNIQUE'
        ELSE 'NON_UNIQUE'
    END AS INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'doc_analysis'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
