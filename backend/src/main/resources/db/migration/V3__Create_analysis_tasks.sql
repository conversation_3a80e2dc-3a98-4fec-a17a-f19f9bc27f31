-- 创建分析任务状态表
-- 用于跟踪MaxKB智能体分析任务的实时状态

CREATE TABLE analysis_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    document_id BIGINT NOT NULL COMMENT '关联文档ID',
    agent_type ENUM('EXTRACT', 'DETECT') NOT NULL COMMENT '智能体类型',
    task_status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'PENDING' COMMENT '任务状态',
    
    -- MaxKB相关信息
    chat_id VARCHAR(255) COMMENT 'MaxKB会话ID',
    file_id VARCHAR(255) COMMENT 'MaxKB文件ID',
    
    -- 时间信息
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    started_time TIMESTAMP NULL COMMENT '开始处理时间',
    completed_time TIMESTAMP NULL COMMENT '完成时间',
    
    -- 进度和状态信息
    progress_percentage INT DEFAULT 0 COMMENT '进度百分比(0-100)',
    status_message VARCHAR(500) COMMENT '状态描述信息',
    error_message TEXT COMMENT '错误信息',
    
    -- 性能统计
    processing_duration_ms BIGINT COMMENT '处理耗时(毫秒)',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    
    -- 结果信息
    result_id BIGINT COMMENT '关联的分析结果ID',
    
    -- 索引
    INDEX idx_document_agent (document_id, agent_type),
    INDEX idx_status (task_status),
    INDEX idx_created_time (created_time),
    
    -- 外键约束
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES analysis_results(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析任务状态跟踪表';

-- 创建分析任务进度视图
CREATE VIEW v_analysis_task_progress AS
SELECT 
    at.id as task_id,
    at.document_id,
    d.original_name as document_name,
    at.agent_type,
    at.task_status,
    at.progress_percentage,
    at.status_message,
    at.created_time,
    at.started_time,
    at.completed_time,
    at.processing_duration_ms,
    CASE 
        WHEN at.task_status = 'COMPLETED' THEN '已完成'
        WHEN at.task_status = 'PROCESSING' THEN '处理中'
        WHEN at.task_status = 'PENDING' THEN '等待中'
        WHEN at.task_status = 'FAILED' THEN '失败'
        ELSE '未知'
    END as status_display,
    CASE 
        WHEN at.agent_type = 'EXTRACT' THEN '数据提取'
        WHEN at.agent_type = 'DETECT' THEN '异常检测'
        ELSE '未知类型'
    END as agent_display
FROM analysis_tasks at
LEFT JOIN documents d ON at.document_id = d.id;

-- 创建文档分析总览视图
CREATE VIEW v_document_analysis_overview AS
SELECT 
    d.id as document_id,
    d.original_name,
    d.status as document_status,
    d.upload_time,
    
    -- 提取任务状态
    extract_task.task_status as extract_status,
    extract_task.progress_percentage as extract_progress,
    extract_task.status_message as extract_message,
    
    -- 检测任务状态  
    detect_task.task_status as detect_status,
    detect_task.progress_percentage as detect_progress,
    detect_task.status_message as detect_message,
    
    -- 整体进度计算
    ROUND((COALESCE(extract_task.progress_percentage, 0) + COALESCE(detect_task.progress_percentage, 0)) / 2) as overall_progress,
    
    -- 整体状态判断
    CASE 
        WHEN extract_task.task_status = 'COMPLETED' AND detect_task.task_status = 'COMPLETED' THEN 'COMPLETED'
        WHEN extract_task.task_status = 'FAILED' OR detect_task.task_status = 'FAILED' THEN 'FAILED'
        WHEN extract_task.task_status = 'PROCESSING' OR detect_task.task_status = 'PROCESSING' THEN 'PROCESSING'
        ELSE 'PENDING'
    END as overall_status
    
FROM documents d
LEFT JOIN analysis_tasks extract_task ON d.id = extract_task.document_id AND extract_task.agent_type = 'EXTRACT'
LEFT JOIN analysis_tasks detect_task ON d.id = detect_task.document_id AND detect_task.agent_type = 'DETECT';

-- 插入测试数据（可选）
-- INSERT INTO analysis_tasks (document_id, agent_type, task_status, progress_percentage, status_message) 
-- VALUES 
-- (1, 'EXTRACT', 'PROCESSING', 45, '正在提取文档数据...'),
-- (1, 'DETECT', 'PENDING', 0, '等待开始异常检测...');
