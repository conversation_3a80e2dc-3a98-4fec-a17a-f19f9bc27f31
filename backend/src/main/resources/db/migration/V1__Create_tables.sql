-- Document Intelligence Analysis System Database Schema
-- Version: 1.0.0
-- Created: 2025-08-07
-- 


-- Create database if not exists
CREATE DATABASE IF NOT EXISTS doc_analysis 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE doc_analysis;

-- 文档表：存储上传文档的基本信息
CREATE TABLE documents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文档ID，主键自增',
    filename VARCHAR(255) NOT NULL COMMENT '存储文件名（系统生成的唯一文件名）',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名（用户上传时的文件名）',
    file_path VARCHAR(500) NOT NULL COMMENT '文件存储路径',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    status ENUM('UPLOADED', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'UPLOADED' COMMENT '文档处理状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='文档基本信息表';

-- 分析结果表：存储MaxKB智能体的分析结果
CREATE TABLE analysis_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分析结果ID，主键自增',
    document_id BIGINT NOT NULL COMMENT '关联的文档ID',
    agent_type ENUM('EXTRACT', 'DETECT') NOT NULL COMMENT '智能体类型：EXTRACT-提取智能体，DETECT-检测智能体',
    result_content JSON NOT NULL COMMENT '分析结果内容（JSON格式）',
    processing_time INT DEFAULT NULL COMMENT '处理耗时（毫秒）',
    api_response_code INT DEFAULT NULL COMMENT 'API响应状态码',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分析完成时间',
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='AI分析结果表';

-- 用户交互记录表：记录用户与系统的交互行为
CREATE TABLE user_interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '交互记录ID，主键自增',
    document_id BIGINT NOT NULL COMMENT '关联的文档ID',
    interaction_type VARCHAR(50) NOT NULL COMMENT '交互类型：highlight-高亮，click-点击，search-搜索等',
    content TEXT COMMENT '交互内容描述',
    position_info JSON COMMENT '位置信息（JSON格式，包含坐标、页码等）',
    session_id VARCHAR(100) DEFAULT NULL COMMENT '用户会话ID',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理信息',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT '用户IP地址',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '交互发生时间',
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='用户交互记录表';

-- 系统配置表：存储系统运行时配置
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT '配置值类型',
    description VARCHAR(500) DEFAULT NULL COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='系统配置表';

-- 操作日志表：记录重要的系统操作
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_desc VARCHAR(500) NOT NULL COMMENT '操作描述',
    document_id BIGINT DEFAULT NULL COMMENT '关联文档ID（如果有）',
    request_params JSON DEFAULT NULL COMMENT '请求参数（JSON格式）',
    response_data JSON DEFAULT NULL COMMENT '响应数据（JSON格式）',
    execution_time INT DEFAULT NULL COMMENT '执行耗时（毫秒）',
    status ENUM('SUCCESS', 'FAILED', 'PARTIAL') DEFAULT 'SUCCESS' COMMENT '执行状态',
    error_message TEXT DEFAULT NULL COMMENT '错误信息（如果有）',
    ip_address VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    user_agent TEXT DEFAULT NULL COMMENT '用户代理',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_operation_type (operation_type),
    INDEX idx_document_id (document_id),
    INDEX idx_created_time (created_time),
    INDEX idx_status (status)
) ENGINE=InnoDB 
  DEFAULT CHARSET=utf8mb4 
  COLLATE=utf8mb4_unicode_ci 
  COMMENT='操作日志表';

-- 插入初始系统配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('max_file_size', '52428800', 'NUMBER', '最大文件上传大小（字节），默认50MB'),
('allowed_file_types', '["application/vnd.openxmlformats-officedocument.wordprocessingml.document"]', 'JSON', '允许上传的文件类型'),
('maxkb_api_timeout', '30000', 'NUMBER', 'MaxKB API调用超时时间（毫秒）'),
('maxkb_retry_attempts', '3', 'NUMBER', 'MaxKB API重试次数'),
('document_retention_days', '90', 'NUMBER', '文档保留天数'),
('enable_operation_log', 'true', 'BOOLEAN', '是否启用操作日志'),
('system_version', '1.0.0', 'STRING', '系统版本号');
