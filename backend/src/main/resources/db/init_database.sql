-- Document Intelligence Analysis System Database Initialization
-- Complete database setup script
-- Version: 1.0.0
-- Created: 2025-08-07
-- 


-- 执行顺序：
-- 1. 运行此脚本创建数据库和用户
-- 2. 运行 V1__Create_tables.sql 创建表结构
-- 3. 运行 V2__Create_indexes.sql 创建索引

-- ================================
-- 数据库和用户创建
-- ================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS doc_analysis 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 创建专用用户（生产环境建议使用）
-- CREATE USER IF NOT EXISTS 'doc_analysis_user'@'localhost' IDENTIFIED BY 'DocAnalysis2025!';
-- CREATE USER IF NOT EXISTS 'doc_analysis_user'@'%' IDENTIFIED BY 'DocAnalysis2025!';

-- 授权
-- GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER ON doc_analysis.* TO 'doc_analysis_user'@'localhost';
-- GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER ON doc_analysis.* TO 'doc_analysis_user'@'%';

-- 刷新权限
-- FLUSH PRIVILEGES;

-- ================================
-- 数据库配置优化
-- ================================

-- 使用数据库
USE doc_analysis;

-- 设置数据库级别的配置
-- 注意：这些设置可能需要管理员权限

-- 设置默认字符集和排序规则
-- ALTER DATABASE doc_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ================================
-- 性能优化配置建议
-- ================================

-- 以下配置建议添加到 my.cnf 或 my.ini 文件中：

/*
[mysqld]
# 基础配置
default-storage-engine=InnoDB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# InnoDB配置
innodb_buffer_pool_size=1G
innodb_log_file_size=256M
innodb_log_buffer_size=16M
innodb_flush_log_at_trx_commit=2
innodb_file_per_table=1

# 连接配置
max_connections=200
max_connect_errors=1000
wait_timeout=28800
interactive_timeout=28800

# 查询缓存（MySQL 5.7及以下）
# query_cache_type=1
# query_cache_size=64M

# 慢查询日志
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 二进制日志
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

# JSON支持优化
optimizer_switch='derived_merge=off'
*/

-- ================================
-- 数据库监控视图
-- ================================

-- 创建数据库状态监控视图
CREATE VIEW v_database_status AS
SELECT 
    'Documents' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) as completed_docs,
    COUNT(CASE WHEN status = 'PROCESSING' THEN 1 END) as processing_docs,
    COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed_docs,
    ROUND(AVG(file_size)/1024/1024, 2) as avg_file_size_mb,
    MAX(upload_time) as latest_upload
FROM documents
UNION ALL
SELECT 
    'Analysis Results' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN agent_type = 'EXTRACT' THEN 1 END) as extract_results,
    COUNT(CASE WHEN agent_type = 'DETECT' THEN 1 END) as detect_results,
    0 as failed_docs,
    ROUND(AVG(processing_time), 2) as avg_processing_time_ms,
    MAX(created_time) as latest_analysis
FROM analysis_results
UNION ALL
SELECT 
    'User Interactions' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT document_id) as unique_documents,
    COUNT(DISTINCT session_id) as unique_sessions,
    0 as failed_docs,
    0 as avg_metric,
    MAX(created_time) as latest_interaction
FROM user_interactions;

-- 创建系统健康检查视图
CREATE VIEW v_system_health AS
SELECT 
    'Database Size' as metric,
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as value_mb,
    'MB' as unit
FROM information_schema.tables 
WHERE table_schema = 'doc_analysis'
UNION ALL
SELECT 
    'Total Documents' as metric,
    COUNT(*) as value_mb,
    'count' as unit
FROM documents
UNION ALL
SELECT 
    'Processing Success Rate' as metric,
    ROUND(
        COUNT(CASE WHEN status = 'COMPLETED' THEN 1 END) * 100.0 / 
        NULLIF(COUNT(CASE WHEN status IN ('COMPLETED', 'FAILED') THEN 1 END), 0), 
        2
    ) as value_mb,
    '%' as unit
FROM documents
UNION ALL
SELECT 
    'Avg Processing Time' as metric,
    ROUND(AVG(processing_time), 2) as value_mb,
    'ms' as unit
FROM analysis_results
WHERE processing_time IS NOT NULL;

-- ================================
-- 数据清理存储过程
-- ================================

DELIMITER //

-- 清理过期数据的存储过程
CREATE PROCEDURE CleanupExpiredData(IN retention_days INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE cleanup_date DATETIME;
    DECLARE deleted_docs INT DEFAULT 0;
    DECLARE deleted_logs INT DEFAULT 0;
    
    -- 计算清理日期
    SET cleanup_date = DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 开始事务
    START TRANSACTION;
    
    -- 清理过期的用户交互记录
    DELETE FROM user_interactions 
    WHERE created_time < cleanup_date;
    
    -- 清理过期的操作日志
    DELETE FROM operation_logs 
    WHERE created_time < cleanup_date;
    SET deleted_logs = ROW_COUNT();
    
    -- 清理过期的已完成文档（保留失败的用于分析）
    DELETE d, ar FROM documents d
    LEFT JOIN analysis_results ar ON d.id = ar.document_id
    WHERE d.upload_time < cleanup_date 
    AND d.status = 'COMPLETED';
    SET deleted_docs = ROW_COUNT();
    
    -- 提交事务
    COMMIT;
    
    -- 记录清理结果
    INSERT INTO operation_logs (
        operation_type, 
        operation_desc, 
        execution_time,
        status,
        response_data
    ) VALUES (
        'DATA_CLEANUP',
        CONCAT('Cleaned up data older than ', retention_days, ' days'),
        0,
        'SUCCESS',
        JSON_OBJECT(
            'deleted_documents', deleted_docs,
            'deleted_logs', deleted_logs,
            'cleanup_date', cleanup_date
        )
    );
    
END //

DELIMITER ;

-- ================================
-- 初始化完成标记
-- ================================

-- 插入数据库初始化完成标记
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('database_initialized', 'true', 'BOOLEAN', '数据库是否已初始化'),
('database_version', '1.0.0', 'STRING', '数据库版本'),
('initialization_date', NOW(), 'STRING', '数据库初始化日期')
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
updated_time = NOW();

-- 显示初始化完成信息
SELECT 
    'Database Initialization Completed' as status,
    DATABASE() as database_name,
    NOW() as completion_time,
    VERSION() as mysql_version;
