# 文档智能分析系统数据库设计

## 概述

本数据库设计用于支持文档智能分析系统，采用MySQL 8.0，支持JSON字段存储复杂数据结构，优化了查询性能和数据完整性。

## 数据库结构

### 核心表

#### 1. documents（文档表）
存储上传文档的基本信息
- `id`: 主键，自增
- `filename`: 系统生成的唯一文件名
- `original_name`: 用户上传的原始文件名
- `file_path`: 文件存储路径
- `file_size`: 文件大小（字节）
- `upload_time`: 上传时间
- `status`: 处理状态（UPLOADED/PROCESSING/COMPLETED/FAILED）

#### 2. analysis_results（分析结果表）
存储MaxKB智能体的分析结果
- `id`: 主键，自增
- `document_id`: 关联文档ID
- `agent_type`: 智能体类型（EXTRACT/DETECT）
- `result_content`: 分析结果（JSON格式）
- `processing_time`: 处理耗时（毫秒）
- `api_response_code`: API响应状态码

#### 3. user_interactions（用户交互表）
记录用户与系统的交互行为
- `id`: 主键，自增
- `document_id`: 关联文档ID
- `interaction_type`: 交互类型（highlight/click/search等）
- `content`: 交互内容描述
- `position_info`: 位置信息（JSON格式）
- `session_id`: 用户会话ID

### 辅助表

#### 4. system_config（系统配置表）
存储系统运行时配置
- 支持STRING、NUMBER、BOOLEAN、JSON类型配置
- 包含配置描述和启用状态

#### 5. operation_logs（操作日志表）
记录重要的系统操作
- 支持操作类型分类和状态追踪
- 包含请求参数和响应数据的JSON存储

## 索引设计

### 性能优化索引
- **documents表**: 状态索引、时间索引、复合索引
- **analysis_results表**: 文档关联索引、类型索引、时间索引
- **user_interactions表**: 文档关联索引、会话索引、类型索引

### JSON字段索引
- 为`result_content`中的常用字段创建虚拟列索引
- 支持高效的JSON数据查询

## 安装和初始化

### 1. 数据库创建
```sql
-- 执行初始化脚本
mysql -u root -p < init_database.sql
```

### 2. 表结构创建
```sql
-- 创建表结构
mysql -u root -p doc_analysis < V1__Create_tables.sql
```

### 3. 索引创建
```sql
-- 创建索引
mysql -u root -p doc_analysis < V2__Create_indexes.sql
```

### 4. 功能测试
```sql
-- 运行测试脚本
mysql -u root -p doc_analysis < test_database.sql
```

## 配置说明

### application.yml配置
```yaml
spring:
  datasource:
    url: ******************************************************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 连接池配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

## JSON数据结构

### 分析结果JSON格式

#### 提取智能体结果
```json
{
  "status": "success",
  "data": {
    "content": [
      {
        "type": "paragraph",
        "text": "提取的文本内容",
        "position": {
          "start": 100,
          "end": 200,
          "page": 1
        },
        "confidence": 0.95
      }
    ]
  },
  "timestamp": "2025-08-07T12:00:00Z"
}
```

#### 检测智能体结果
```json
{
  "status": "success",
  "data": {
    "detections": [
      {
        "type": "grammar",
        "description": "语法错误检测",
        "position": {
          "start": 150,
          "end": 180,
          "page": 1
        },
        "severity": "medium",
        "suggestion": "建议修改为..."
      }
    ]
  },
  "timestamp": "2025-08-07T12:00:00Z"
}
```

### 用户交互JSON格式
```json
{
  "start": 100,
  "end": 200,
  "page": 1,
  "coordinates": {
    "x": 150,
    "y": 300
  },
  "viewport": {
    "width": 1920,
    "height": 1080
  }
}
```

## 查询示例

### 1. 获取文档及其分析结果
```sql
SELECT 
    d.filename,
    d.status,
    ar.agent_type,
    JSON_EXTRACT(ar.result_content, '$.status') as analysis_status
FROM documents d
LEFT JOIN analysis_results ar ON d.id = ar.document_id
WHERE d.id = ?;
```

### 2. 查询特定类型的分析结果
```sql
SELECT 
    document_id,
    JSON_EXTRACT(result_content, '$.data.content') as extracted_content
FROM analysis_results
WHERE agent_type = 'EXTRACT'
AND document_id = ?;
```

### 3. 统计用户交互行为
```sql
SELECT 
    interaction_type,
    COUNT(*) as count,
    COUNT(DISTINCT document_id) as unique_documents
FROM user_interactions
WHERE created_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY interaction_type;
```

## 性能监控

### 1. 使用监控视图
```sql
-- 查看数据库状态
SELECT * FROM v_database_status;

-- 查看系统健康状况
SELECT * FROM v_system_health;

-- 查看索引使用情况
SELECT * FROM v_index_usage;
```

### 2. 慢查询监控
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
```

## 数据维护

### 1. 定期清理过期数据
```sql
-- 调用清理存储过程（清理90天前的数据）
CALL CleanupExpiredData(90);
```

### 2. 索引维护
```sql
-- 分析表统计信息
ANALYZE TABLE documents, analysis_results, user_interactions;

-- 优化表
OPTIMIZE TABLE documents, analysis_results, user_interactions;
```

## 备份和恢复

### 1. 数据备份
```bash
# 完整备份
mysqldump -u root -p --single-transaction --routines --triggers doc_analysis > backup_$(date +%Y%m%d).sql

# 仅结构备份
mysqldump -u root -p --no-data doc_analysis > schema_backup.sql
```

### 2. 数据恢复
```bash
# 恢复数据
mysql -u root -p doc_analysis < backup_20250807.sql
```

## 安全建议

1. **用户权限**: 为应用创建专用数据库用户，限制权限
2. **连接加密**: 生产环境启用SSL连接
3. **密码策略**: 使用强密码并定期更换
4. **访问控制**: 限制数据库服务器的网络访问
5. **审计日志**: 启用MySQL审计日志记录敏感操作

## 故障排除

### 常见问题

1. **连接超时**: 检查连接池配置和网络连接
2. **JSON查询慢**: 考虑为常用JSON字段创建虚拟列索引
3. **磁盘空间不足**: 定期清理过期数据和日志文件
4. **锁等待**: 优化事务大小，避免长时间持有锁

### 性能调优

1. **InnoDB缓冲池**: 设置为物理内存的70-80%
2. **查询缓存**: MySQL 8.0已移除，使用应用层缓存
3. **分区表**: 对于大量历史数据，考虑按时间分区
4. **读写分离**: 高并发场景下考虑主从复制
