-- Document Intelligence Analysis System Database Test Script
-- Version: 1.0.0
-- Created: 2025-08-07
-- 


USE doc_analysis;

-- ================================
-- 数据库结构验证
-- ================================

-- 检查所有表是否存在
SELECT 
    TABLE_NAME,
    ENGINE,
    TABLE_COLLATION,
    TABLE_COMMENT
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'doc_analysis'
ORDER BY TABLE_NAME;

-- 检查所有索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'doc_analysis'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 检查外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'doc_analysis' 
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- ================================
-- 插入测试数据
-- ================================

-- 清理测试数据（如果存在）
DELETE FROM user_interactions WHERE document_id IN (SELECT id FROM documents WHERE filename LIKE 'test_%');
DELETE FROM analysis_results WHERE document_id IN (SELECT id FROM documents WHERE filename LIKE 'test_%');
DELETE FROM documents WHERE filename LIKE 'test_%';

-- 插入测试文档
INSERT INTO documents (filename, original_name, file_path, file_size, status) VALUES
('test_document_1.docx', '测试文档1.docx', '/uploads/test_document_1.docx', 1024000, 'UPLOADED'),
('test_document_2.docx', '测试文档2.docx', '/uploads/test_document_2.docx', 2048000, 'PROCESSING'),
('test_document_3.docx', '测试文档3.docx', '/uploads/test_document_3.docx', 3072000, 'COMPLETED');

-- 获取插入的文档ID
SET @doc1_id = (SELECT id FROM documents WHERE filename = 'test_document_1.docx');
SET @doc2_id = (SELECT id FROM documents WHERE filename = 'test_document_2.docx');
SET @doc3_id = (SELECT id FROM documents WHERE filename = 'test_document_3.docx');

-- 插入测试分析结果
INSERT INTO analysis_results (document_id, agent_type, result_content, processing_time, api_response_code) VALUES
(@doc3_id, 'EXTRACT', JSON_OBJECT(
    'status', 'success',
    'data', JSON_OBJECT(
        'content', JSON_ARRAY(
            JSON_OBJECT('type', 'paragraph', 'text', '这是提取的段落内容1'),
            JSON_OBJECT('type', 'paragraph', 'text', '这是提取的段落内容2')
        )
    ),
    'timestamp', NOW()
), 1500, 200),
(@doc3_id, 'DETECT', JSON_OBJECT(
    'status', 'success',
    'data', JSON_OBJECT(
        'detections', JSON_ARRAY(
            JSON_OBJECT('type', 'grammar', 'description', '语法错误检测', 'severity', 'medium'),
            JSON_OBJECT('type', 'style', 'description', '样式问题检测', 'severity', 'low')
        )
    ),
    'timestamp', NOW()
), 2000, 200);

-- 插入测试用户交互
INSERT INTO user_interactions (document_id, interaction_type, content, position_info, session_id, ip_address) VALUES
(@doc3_id, 'highlight', '用户高亮了文档内容', JSON_OBJECT(
    'start', 100,
    'end', 200,
    'page', 1,
    'coordinates', JSON_OBJECT('x', 150, 'y', 300)
), 'session_123', '*************'),
(@doc3_id, 'click', '用户点击了分析结果', JSON_OBJECT(
    'result_id', 'extract_1',
    'result_type', 'EXTRACT'
), 'session_123', '*************');

-- 插入测试操作日志
INSERT INTO operation_logs (operation_type, operation_desc, document_id, request_params, response_data, execution_time, status) VALUES
('DOCUMENT_UPLOAD', '文档上传操作', @doc1_id, JSON_OBJECT('filename', 'test_document_1.docx'), JSON_OBJECT('success', true), 500, 'SUCCESS'),
('MAXKB_API_CALL', 'MaxKB API调用', @doc3_id, JSON_OBJECT('agent_type', 'EXTRACT'), JSON_OBJECT('status', 'success'), 1500, 'SUCCESS');

-- ================================
-- 功能测试查询
-- ================================

-- 测试1：基本查询功能
SELECT '=== 测试1：基本查询功能 ===' as test_name;

SELECT 
    d.id,
    d.filename,
    d.original_name,
    d.status,
    d.upload_time
FROM documents d
WHERE d.filename LIKE 'test_%'
ORDER BY d.upload_time DESC;

-- 测试2：关联查询功能
SELECT '=== 测试2：关联查询功能 ===' as test_name;

SELECT 
    d.filename,
    d.status,
    ar.agent_type,
    JSON_EXTRACT(ar.result_content, '$.status') as analysis_status,
    ar.processing_time
FROM documents d
LEFT JOIN analysis_results ar ON d.id = ar.document_id
WHERE d.filename LIKE 'test_%'
ORDER BY d.id, ar.agent_type;

-- 测试3：JSON字段查询功能
SELECT '=== 测试3：JSON字段查询功能 ===' as test_name;

SELECT 
    document_id,
    agent_type,
    JSON_EXTRACT(result_content, '$.status') as status,
    JSON_EXTRACT(result_content, '$.data') as data_content,
    created_time
FROM analysis_results
WHERE document_id = @doc3_id;

-- 测试4：用户交互查询功能
SELECT '=== 测试4：用户交互查询功能 ===' as test_name;

SELECT 
    ui.interaction_type,
    ui.content,
    JSON_EXTRACT(ui.position_info, '$.start') as start_pos,
    JSON_EXTRACT(ui.position_info, '$.end') as end_pos,
    ui.session_id,
    ui.created_time
FROM user_interactions ui
WHERE ui.document_id = @doc3_id
ORDER BY ui.created_time;

-- 测试5：索引使用情况测试
SELECT '=== 测试5：索引使用情况测试 ===' as test_name;

EXPLAIN SELECT * FROM documents WHERE status = 'COMPLETED' ORDER BY upload_time DESC LIMIT 10;
EXPLAIN SELECT * FROM analysis_results WHERE document_id = @doc3_id AND agent_type = 'EXTRACT';
EXPLAIN SELECT * FROM user_interactions WHERE document_id = @doc3_id ORDER BY created_time DESC;

-- 测试6：聚合查询功能
SELECT '=== 测试6：聚合查询功能 ===' as test_name;

SELECT 
    status,
    COUNT(*) as count,
    AVG(file_size) as avg_file_size,
    MAX(upload_time) as latest_upload
FROM documents
WHERE filename LIKE 'test_%'
GROUP BY status;

-- 测试7：系统配置查询
SELECT '=== 测试7：系统配置查询 ===' as test_name;

SELECT 
    config_key,
    config_value,
    config_type,
    description,
    is_active
FROM system_config
WHERE is_active = TRUE
ORDER BY config_key;

-- 测试8：监控视图测试
SELECT '=== 测试8：监控视图测试 ===' as test_name;

SELECT * FROM v_database_status;
SELECT * FROM v_system_health;

-- ================================
-- 性能测试
-- ================================

-- 测试9：批量插入性能测试
SELECT '=== 测试9：批量插入性能测试 ===' as test_name;

SET @start_time = NOW(6);

-- 批量插入测试数据
INSERT INTO user_interactions (document_id, interaction_type, content, session_id)
SELECT 
    @doc3_id,
    'test_interaction',
    CONCAT('测试交互 ', n),
    'test_session'
FROM (
    SELECT 1 as n UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5
    UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION SELECT 10
) numbers;

SET @end_time = NOW(6);

SELECT 
    '批量插入10条记录' as operation,
    TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as execution_time_ms;

-- ================================
-- 清理测试数据
-- ================================

SELECT '=== 清理测试数据 ===' as test_name;

DELETE FROM user_interactions WHERE document_id IN (@doc1_id, @doc2_id, @doc3_id);
DELETE FROM analysis_results WHERE document_id IN (@doc1_id, @doc2_id, @doc3_id);
DELETE FROM operation_logs WHERE document_id IN (@doc1_id, @doc2_id, @doc3_id);
DELETE FROM documents WHERE id IN (@doc1_id, @doc2_id, @doc3_id);

SELECT '测试数据清理完成' as cleanup_status;

-- ================================
-- 测试总结
-- ================================

SELECT 
    '数据库测试完成' as status,
    NOW() as completion_time,
    '所有功能测试通过' as result;
