server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: document-analysis-backend
  
  # Database Configuration
  datasource:
    url: jdbc:mysql://${MYSQL_URL:172.16.110.204}:3306/doc_analysis?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: zjiecn
    password: zjiecn@123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true
  
  # Jackson Configuration
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus Configuration
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Logging Configuration
logging:
  level:
    com.docanalysis: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Custom Application Configuration
app:
  # File Storage Configuration
  file:
    upload-dir: ${user.home}/document-analysis/uploads
    max-size: 52428800  # 50MB in bytes
    allowed-types: application/vnd.openxmlformats-officedocument.wordprocessingml.document
  
  # MaxKB API Configuration
  maxkb:
    base-url: ${BASE_URL:http://119.3.106.119:8080}
    extract-agent:
      application-id: ${EXTRACT_APP_ID:36c0f176-612a-11f0-b769-02420a370103}
      api-key: ${EXTRACT_API_KEY:application-451b7a2742cb67333fb6781dd8902a08}
    detect-agent:
      application-id: ${DETECT_APP_ID:4df82ff0-615a-11f0-badc-02420a370103}
      api-key: ${DETECT_API_KEY:application-47f5f4a8127ec92961d1b4da8291c8ef}
    qa-agent:
      application-id: ${QA_APP_ID:6a1ea77a-7809-11f0-8131-06f471f67c47}
      access_token: ${QA_ACCESS_TOKEN:46d35962e89e8010}
      api-key: ${QA_API_KEY:application-a8bb0d5c25e87f0e63aa2584325488dd}
      qa-url: ${BASE_URL:http://119.3.106.119:8080}
    #      access_token: a69936d9f7978fff
    #      api-key: application-66d19879293c85a751057d8290adf54d
    timeout: 30000  # 30 seconds
    retry-attempts: 3
  
  # Async Configuration
  async:
    core-pool-size: 5
    max-pool-size: 20
    queue-capacity: 100
    thread-name-prefix: DocAnalysis-Async-
