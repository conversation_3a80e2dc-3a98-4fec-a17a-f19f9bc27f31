-- ================================================================================================
-- 文档智能分析系统 - 快速部署 MariaDB 脚本
-- Document Intelligence Analysis System - Quick Deploy MariaDB Script
-- ================================================================================================
-- Version: 1.0.0
-- Created: 2025-08-12
-- Purpose: 快速创建基本数据库结构，适用于开发和测试环境
-- ================================================================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库
DROP DATABASE IF EXISTS doc_analysis;
CREATE DATABASE doc_analysis CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE doc_analysis;

-- 1. 文档表
CREATE TABLE documents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    status ENUM('UPLOADED', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'UPLOADED',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT(1) DEFAULT 0,
    
    INDEX idx_status (status),
    INDEX idx_upload_time (upload_time),
    UNIQUE KEY uk_filename (filename)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 分析结果表
CREATE TABLE analysis_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_id BIGINT NOT NULL,
    agent_type ENUM('EXTRACT', 'DETECT') NOT NULL,
    result_content JSON NOT NULL,
    processing_time INT DEFAULT NULL,
    api_response_code INT DEFAULT NULL,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id),
    INDEX idx_agent_type (agent_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 用户交互表
CREATE TABLE user_interactions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    document_id BIGINT NOT NULL,
    interaction_type VARCHAR(50) NOT NULL,
    content TEXT,
    position_info JSON,
    session_id VARCHAR(100),
    ip_address VARCHAR(45),
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id),
    INDEX idx_session_id (session_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 分析任务表
CREATE TABLE analysis_tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    document_id BIGINT NOT NULL,
    agent_type ENUM('EXTRACT', 'DETECT') NOT NULL,
    task_status ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING',
    chat_id VARCHAR(255),
    file_id VARCHAR(255),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_time TIMESTAMP NULL,
    completed_time TIMESTAMP NULL,
    progress_percentage INT DEFAULT 0,
    status_message VARCHAR(500),
    error_message TEXT,
    processing_duration_ms BIGINT,
    retry_count INT DEFAULT 0,
    result_id BIGINT,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (result_id) REFERENCES analysis_results(id) ON DELETE SET NULL,
    INDEX idx_document_agent (document_id, agent_type),
    INDEX idx_status (task_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. 系统配置表
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING',
    description VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. 操作日志表
CREATE TABLE operation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    operation_type VARCHAR(50) NOT NULL,
    operation_desc VARCHAR(255),
    document_id BIGINT,
    request_params JSON,
    response_data JSON,
    execution_time INT,
    status ENUM('SUCCESS', 'FAILED', 'PENDING') DEFAULT 'SUCCESS',
    error_message TEXT,
    ip_address VARCHAR(45),
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL,
    INDEX idx_operation_type (operation_type),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入基本配置
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('database_version', '1.0.0', 'STRING', '数据库版本'),
('max_file_size', '52428800', 'NUMBER', '最大文件大小50MB'),
('allowed_file_types', '["pdf", "doc", "docx", "txt", "xlsx"]', 'JSON', '允许的文件类型'),
('maxkb_api_timeout', '30000', 'NUMBER', 'API超时时间'),
('analysis_batch_size', '10', 'NUMBER', '批量处理大小');

-- 创建基本视图
CREATE VIEW v_document_status AS
SELECT 
    status,
    COUNT(*) as count,
    ROUND(AVG(file_size)/1024/1024, 2) as avg_size_mb
FROM documents 
WHERE deleted = 0 
GROUP BY status;

SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT 
    '快速部署完成' as message,
    DATABASE() as database_name,
    NOW() as completion_time;

-- 验证表创建
SELECT TABLE_NAME, TABLE_ROWS 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'doc_analysis' 
ORDER BY TABLE_NAME;
